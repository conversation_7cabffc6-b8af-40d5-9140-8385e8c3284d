import os
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'

import numpy as np
import pandas as pd
import json
from scipy.spatial.distance import cdist
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import NMF
from sklearn.cluster import KMeans
from sklearn.neighbors import kneighbors_graph
from sklearn.metrics import silhouette_score  # 确保在此处导入轮廓系数函数
import matplotlib.pyplot as plt
import seaborn as sns
from pyproj import Proj
from pulp import *
import torch
import torch.nn as nn
import torch.optim as optim
from torch_geometric.data import Data
from torch_geometric.nn import GCNConv
import torch.nn.functional as F
import matplotlib.font_manager as fm  # 添加字体管理模块
import pymc as pm  # 贝叶斯建模
import arviz as az  # 用于贝叶斯结果分析

# ======================================
# 1. 常量定义
# ======================================
EXCEEDANCE_THRESHOLDS = {
    'Cd': 0.26, 'As': 35, 'Pb': 80, 'Cr': 159,
    'Ni': 58, 'Zn': 200, 'Cu': 45
}
METALS = list(EXCEEDANCE_THRESHOLDS.keys())
SOURCE_TYPES = ['atmosphere', 'irrigation', 'pesticide', 'manure']
SEARCH_RADIUS_KM = 50
DEG_TO_KM = 111

# ======================================
# 2. 核心污染溯源模型
# ======================================
def build_source_fingerprints(sources, pollutants):
    """构建标准化污染源指纹"""
    # 确保污染物数据是数值类型
    source_data = sources[pollutants].apply(pd.to_numeric, errors='coerce')

    # 处理NaN值
    if source_data.isna().sum().sum() > 0:
        source_data = source_data.fillna(source_data.median())

    # 转换为浮点数数组以避免 np.isnan 错误
    clipped_data = source_data.astype(float).copy()

    # 异常值处理
    q_low = np.percentile(clipped_data, 10, axis=0)
    q_high = np.percentile(clipped_data, 90, axis=0)
    for i, col in enumerate(clipped_data.columns):
        clipped_data[col] = clipped_data[col].clip(lower=q_low[i], upper=q_high[i])
    
    # 在标准化前将DataFrame转换为NumPy数组
    clipped_data_arr = clipped_data.values  # 新增：转换为NumPy数组
    
    # 按比例标准化 (修复Pandas sum()不支持keepdims的问题)
    row_sums = clipped_data_arr.sum(axis=1).reshape(-1, 1)  # 修改行
    source_fingerprints = np.divide(clipped_data_arr, row_sums,  # 修改行：使用数组
                                    out=np.zeros_like(clipped_data_arr),
                                    where=row_sums != 0)
    
    return source_fingerprints

def build_receptor_fingerprints(samples, pollutants):
    """构建标准化采样点指纹"""
    # 确保污染物数据是数值类型
    sample_data = samples[pollutants].apply(pd.to_numeric, errors='coerce')

    # 处理NaN值
    if sample_data.isna().sum().sum() > 0:
        sample_data = sample_data.fillna(sample_data.median())

    # 转换为浮点数数组以避免 np.isnan 错误
    clipped_data = sample_data.astype(float).copy()

    # 异常值处理
    q_low = np.percentile(clipped_data, 10, axis=0)
    q_high = np.percentile(clipped_data, 90, axis=0)
    for i, col in enumerate(clipped_data.columns):
        clipped_data[col] = clipped_data[col].clip(lower=q_low[i], upper=q_high[i])
    
    # 在标准化前将DataFrame转换为NumPy数组
    clipped_data_arr = clipped_data.values  # 新增：转换为NumPy数组
    
    # 按比例标准化 (修复Pandas sum()不支持keepdims的问题)
    row_sums = clipped_data_arr.sum(axis=1).reshape(-1, 1)  # 修改行
    receptor_fingerprints = np.divide(clipped_data_arr, row_sums,  # 修改行：使用数组
                                      out=np.zeros_like(clipped_data_arr),
                                      where=row_sums != 0)
    
    return receptor_fingerprints

def calculate_fingerprint_similarity(source_fp, receptor_fp):
    """计算污染源与采样点的指纹相似度"""
    # 计算余弦相似度
    cos_sim = cosine_similarity(receptor_fp, source_fp)
    
    # 计算欧氏距离相似度
    euclidean_dist = cdist(receptor_fp, source_fp, 'euclidean')
    max_dist = np.max(euclidean_dist)
    euclidean_sim = 1 - (euclidean_dist / max_dist)
    
    # 组合相似度
    combined_sim = 0.7 * cos_sim + 0.3 * euclidean_sim
    return combined_sim

class Autoencoder(nn.Module):
    """自编码器用于学习污染模式特征"""
    def __init__(self, input_dim, encoding_dim=8):
        super(Autoencoder, self).__init__()
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, 32), nn.ReLU(),
            nn.Linear(32, encoding_dim), nn.ReLU()
        )
        self.decoder = nn.Sequential(
            nn.Linear(encoding_dim, 32), nn.ReLU(),
            nn.Linear(32, input_dim), nn.Sigmoid()
        )
    
    def forward(self, x):
        encoded = self.encoder(x)
        decoded = self.decoder(encoded)
        return decoded, encoded

def train_autoencoder(receptor_fp, encoding_dim=8, epochs=200):
    input_dim = receptor_fp.shape[1]
    model = Autoencoder(input_dim, encoding_dim)
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    
    tensor_data = torch.tensor(receptor_fp, dtype=torch.float32)
    
    for epoch in range(epochs):
        optimizer.zero_grad()
        reconstructed, encoded = model(tensor_data)
        loss = criterion(reconstructed, tensor_data)
        loss.backward()
        optimizer.step()
    
    with torch.no_grad():
        _, encoded_features = model(tensor_data)
    
    return encoded_features.numpy()

def cluster_analysis(features, n_clusters=None, min_clusters=3, max_clusters=8):
    """执行聚类分析
    :param min_clusters: 最小聚类数量，默认3类
    :param max_clusters: 最大聚类数量，默认8类
    """
    # 确保最大聚类数不超过样本量
    max_clusters = min(max_clusters, len(features) - 1) if len(features) > 1 else 1
    
    if n_clusters is None:
        # 自动确定聚类数量
        distortions = []
        sil_scores = []  # 重命名变量避免冲突
        possible_clusters = range(min_clusters, max_clusters + 1)
        
        for k in possible_clusters:
            # 计算肘部法则指标
            km = KMeans(n_clusters=k, random_state=0)
            clusters = km.fit_predict(features)
            distortions.append(km.inertia_)
            
            # 计算轮廓系数（当k>1时）
            if k > 1:
                try:
                    # 使用正确的函数名和变量名
                    score = silhouette_score(features, clusters)
                    sil_scores.append(score)
                except Exception as e:
                    print(f"计算轮廓系数失败: {e}")
                    sil_scores.append(0)  # 默认值为0
            else:
                sil_scores.append(0)  # 单类轮廓系数为0
        
        # 双重评估：肘部法则+轮廓系数
        if len(sil_scores) > 0:
            # 选择轮廓系数最高的聚类数
            best_silhouette_idx = np.argmax(sil_scores)
            n_clusters = possible_clusters[best_silhouette_idx]
        else:
            # 寻找肘部拐点
            diff = np.diff(distortions)
            if len(diff) > 0:
                # 更灵敏的拐点检测
                normalized_diff = diff / np.max(np.abs(diff))
                n_clusters = np.argmax(normalized_diff < -0.15) + min_clusters
            else:
                n_clusters = min_clusters
    
    # 确保聚类数在有效范围内
    n_clusters = max(min_clusters, min(n_clusters, max_clusters))
    
    kmeans = KMeans(n_clusters=n_clusters, random_state=42)
    clusters = kmeans.fit_predict(features)
    return clusters

def build_graph_data(samples, features):
    """构建图神经网络所需数据结构"""
    # 节点特征
    x = torch.tensor(features, dtype=torch.float)
    
    # 构建空间位置
    coords = samples[['lon', 'lat']].values
    
    # 计算距离 (使用平面投影)
    p = Proj(proj='aeqd', ellps='WGS84', lat_0=coords[:, 1].mean(), lon_0=coords[:, 0].mean())
    x_proj, y_proj = p(coords[:, 0], coords[:, 1])
    projected_coords = np.column_stack([x_proj, y_proj])
    
    # 创建K近邻图
    edge_index = kneighbors_graph(
        projected_coords, n_neighbors=8, mode='connectivity'
    ).tocoo()
    
    edge_index = torch.tensor(np.array([edge_index.row, edge_index.col]), dtype=torch.long)
    
    return Data(x=x, edge_index=edge_index, pos=torch.tensor(projected_coords, dtype=torch.float))

class GCN(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim):
        super(GCN, self).__init__()
        self.conv1 = GCNConv(input_dim, hidden_dim)
        self.conv2 = GCNConv(hidden_dim, output_dim)
    
    def forward(self, data):
        x, edge_index = data.x, data.edge_index
        x = self.conv1(x, edge_index)
        x = torch.relu(x)
        x = self.conv2(x, edge_index)
        return x

def train_gnn(data, hidden_dim=16, output_dim=8, epochs=100):
    """训练GNN获取空间增强特征"""
    model = GCN(input_dim=data.x.size(1), hidden_dim=hidden_dim, output_dim=output_dim)
    optimizer = optim.Adam(model.parameters(), lr=0.01)
    
    model.train()
    for epoch in range(epochs):
        optimizer.zero_grad()
        out = model(data)
        neighbor_sim = F.cosine_similarity(
            out[data.edge_index[0]], out[data.edge_index[1]]
        )
        loss = 1 - torch.mean(neighbor_sim)
        loss.backward()
        optimizer.step()
    
    model.eval()
    with torch.no_grad():
        embeddings = model(data)
    return embeddings.detach().numpy()

def optimal_transport_model(sources, receptors, samples_data, source_data, pollutants):
    """使用最优传输量化污染贡献"""
    # 准备污染源和采样点位置
    source_coords = source_data[['lon', 'lat']].values
    sample_coords = samples_data[['lon', 'lat']].values
    
    # 计算空间距离成本
    spatial_dist = cdist(sample_coords, source_coords, 'euclidean')
    max_dist = np.max(spatial_dist)
    spatial_cost = spatial_dist / max_dist
    
    # 计算指纹差异成本
    similarity = cosine_similarity(receptors, sources)
    fingerprint_cost = 1 - similarity
    
    # 组合成本函数
    total_cost = 0.6 * spatial_cost + 0.4 * fingerprint_cost
    
    # 创建线性规划问题
    prob = LpProblem("Pollution_Attribution", LpMinimize)
    
    # 决策变量
    num_samples = len(samples_data)
    num_sources = len(source_data)
    var_names = [f'x_{i}_{j}' for i in range(num_samples) for j in range(num_sources)]
    variables = LpVariable.dicts("Contrib", var_names, 0)
    
    # 目标函数
    prob += lpSum([variables[f'x_{i}_{j}'] * total_cost[i, j]
                   for i in range(num_samples) for j in range(num_sources)])
    
    # 约束条件
    for i in range(num_samples):
        prob += lpSum([variables[f'x_{i}_{j}'] for j in range(num_sources)]) == 1
    
    for j in range(num_sources):
        source_total = source_data.iloc[j][pollutants].sum()
        prob += lpSum([variables[f'x_{i}_{j}'] * samples_data[pollutants].iloc[i].sum()
                       for i in range(num_samples)]) <= 1.2 * source_total
    
    # 求解问题
    status = prob.solve(PULP_CBC_CMD(msg=False))
    
    # 提取结果
    contribution_matrix = np.zeros((num_samples, num_sources))
    for i in range(num_samples):
        for j in range(num_sources):
            var_name = f'x_{i}_{j}'
            contribution_matrix[i, j] = variables[var_name].varValue
    
    return contribution_matrix

def run_bayesian_model(data, sources, pollutants, n_factors=4, draws=1000, tune=1000):
    """
    使用贝叶斯非负矩阵分解进行污染源解析
    返回：
        contributions: 样本对各因子的贡献权重 (n_samples, n_factors)
        components: 各因子的污染源指纹 (n_factors, n_features)
    """
    # 数据预处理
    data = np.array(data[pollutants]).astype(float)
    
    # 标准化数据
    data_mean = np.mean(data, axis=0)
    data_std = np.std(data, axis=0)
    data_norm = (data - data_mean) / (data_std + 1e-8)
    
    # 构建污染源先验信息
    source_matrix = np.array(sources.groupby('source_type')[pollutants].mean())
    n_sources, n_features = source_matrix.shape
    
    # 如果因子数大于污染源类型数，使用污染源先验作为部分因子
    if n_factors > n_sources:
        extra_factors = n_factors - n_sources
        # 添加随机生成的额外因子
        extra_sources = np.random.rand(extra_factors, n_features)
        source_matrix = np.vstack([source_matrix, extra_sources])
    
    with pm.Model() as model:
        # 定义维度
        n_samples, n_features = data_norm.shape
        
        # 定义先验分布
        # 使用污染源先验作为基础
        source_priors = pm.Dirichlet('source_priors', a=np.ones(n_sources), shape=(n_sources, n_factors))
        
        # 贡献矩阵（样本对因子的权重）
        W = pm.Dirichlet('W', a=np.ones(n_factors), shape=(n_samples, n_factors))
        
        # 成分矩阵（因子特征）
        H = pm.Normal('H', mu=0, sigma=1, shape=(n_factors, n_features))
        
        # 观测模型
        mu = pm.math.dot(W, H)
        Y_obs = pm.Normal('Y_obs', mu=mu, sigma=1, observed=data_norm)
        
    # 采样
    with model:
        trace = pm.sample(draws=draws, tune=tune, chains=1, progressbar=False)
    
    # 提取后验估计
    posterior_W = np.mean(trace.posterior['W'].values, axis=0)
    posterior_H = np.mean(trace.posterior['H'].values, axis=0)
    
    # 反标准化
    posterior_H_unscaled = posterior_H * data_std + data_mean
    
    return posterior_W, posterior_H_unscaled

def spatial_correction_with_rf(samples, sources, contribution_matrix):
    """使用随机森林进行空间校正"""
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.preprocessing import StandardScaler
    
    # 构建空间特征矩阵
    spatial_features = []
    for idx, sample in samples.iterrows():
        sample_coords = [sample['lon'], sample['lat']]
        
        # 计算到所有污染源的距离
        distances = []
        for _, source in sources.iterrows():
            dist = np.linalg.norm(np.array(sample_coords) - 
                                 np.array([source['lon'], source['lat']]))
            distances.append(dist)
            
        # 添加经纬度和距离特征
        features = [sample['lon'], sample['lat']] + distances
        spatial_features.append(features)
    
    # 标准化特征
    scaler = StandardScaler()
    S = scaler.fit_transform(spatial_features)
    
    # 初始化校正后的贡献矩阵
    corrected_contribution = np.zeros_like(contribution_matrix)
    
    # 对每个污染源训练校正模型
    for j in range(contribution_matrix.shape[1]):
        rf = RandomForestRegressor(n_estimators=100, random_state=42)
        rf.fit(S, contribution_matrix[:, j])
        corrected_contribution[:, j] = rf.predict(S)
    
    # 归一化保证每行和为1
    row_sums = corrected_contribution.sum(axis=1)
    row_sums[row_sums == 0] = 1  # 避免除零
    corrected_contribution = corrected_contribution / row_sums[:, np.newaxis]
    
    return corrected_contribution

def calculate_confidence_score(contributions, similarities, pollution_cluster, spatial_cluster):
    """
    计算单个样本的置信度评分
    综合考虑：
    1. 贡献率分布集中度
    2. 指纹相似度
    3. 聚类一致性
    """
    # 1. 贡献率集中度 - 前2个污染源贡献率差值
    sorted_contrib = np.sort(contributions)[::-1]  # 降序排列
    if len(sorted_contrib) >= 2:
        concentration = sorted_contrib[0] - sorted_contrib[1]
    else:
        concentration = sorted_contrib[0] if len(sorted_contrib) == 1 else 0
    
    # 2. 最佳匹配相似度
    max_similarity = np.max(similarities) if len(similarities) > 0 else 0
    
    # 3. 聚类一致性（简单版本）
    cluster_consistency = 1.0 if pollution_cluster != -1 and spatial_cluster != -1 and pollution_cluster == spatial_cluster else 0.5
    
    # 综合置信度计算（可调整权重）
    confidence = (
        0.4 * concentration +   # 贡献率集中度
        0.3 * max_similarity +   # 最大相似度
        0.3 * cluster_consistency  # 聚类一致性
    )
    
    # 映射到0-5分制
    return min(5.0, max(0.0, 5.0 * confidence))

def integrate_results(soil_data, sources, contribution_matrix, similarity_matrix, sample_clusters, spatial_clusters):
    """
    整合所有分析结果，生成最终污染源溯源报告
    """
    # 初始化结果列表
    results = []
    
    # 获取采样点ID
    sample_ids = soil_data['sample_id'].values if 'sample_id' in soil_data.columns else range(len(soil_data))
    
    # 获取所有金属污染物
    metals = [col for col in soil_data.columns if col in EXCEEDANCE_THRESHOLDS]
    
    # 生成详细报告 - 修复循环逻辑
    for i, sample_id in enumerate(sample_ids):
        # 获取当前样本的超标污染物
        exceedance_metals = {m: soil_data.iloc[i][m] for m in metals if m in soil_data.columns and soil_data.iloc[i][m] > EXCEEDANCE_THRESHOLDS.get(m, float('inf'))}
        
        # 获取主要污染源（贡献率前5）
        main_sources = []
        for j, source_type in enumerate(sources['source_type'].unique()):
            # 计算该污染源对当前样本的综合贡献
            if j < contribution_matrix.shape[1]:  # 防止索引越界
                contribution = contribution_matrix[i, j]
                if contribution > 0:
                    main_sources.append({
                        'source_type': source_type,
                        'contribution': contribution,
                        'exceedance_metals': {m: v for m, v in exceedance_metals.items()}
                    })

        # 按贡献率排序并取前5
        main_sources.sort(key=lambda x: x['contribution'], reverse=True)
        main_sources = main_sources[:5]

        # 获取空间聚类信息
        cluster_id = sample_clusters[i] if sample_clusters is not None and i < len(sample_clusters) else -1

        # 计算置信度评分
        confidence_score = calculate_confidence_score(
            contribution_matrix[i],
            similarity_matrix[i],
            sample_clusters[i] if sample_clusters is not None else -1,
            spatial_clusters[i] if spatial_clusters is not None else -1
        )

        # 构建结果字典 - 每个样本独立构建
        result_dict = {
            'sample_id': sample_id,
            'latitude': soil_data.iloc[i]['lat'] if 'lat' in soil_data.columns else None,
            'longitude': soil_data.iloc[i]['lon'] if 'lon' in soil_data.columns else None,
            'metals': {m: soil_data.iloc[i][m] for m in metals if m in soil_data.columns},
            'main_sources': main_sources,
            'cluster_id': cluster_id,
            'confidence_score': confidence_score,
            'pollution_cluster': sample_clusters[i] if sample_clusters is not None and i < len(sample_clusters) else -1,
            'spatial_cluster': spatial_clusters[i] if spatial_clusters is not None and i < len(spatial_clusters) else -1
        }
        
        # 将当前样本结果添加到结果列表
        results.append(result_dict)
    
    # 转换为DataFrame
    result_df = pd.DataFrame(results)
    
    # 添加每个污染源的贡献列
    # 确保sources有'sample'列
    if 'sample' in sources.columns:
        # 遍历每个污染源
        for j in range(contribution_matrix.shape[1]):
            source_id = sources.iloc[j]['sample']
            # 添加该污染源的贡献列
            result_df[f'contrib_{source_id}'] = contribution_matrix[:, j]
    else:
        # 如果sources没有'sample'列，则使用索引
        for j in range(contribution_matrix.shape[1]):
            result_df[f'contrib_{j}'] = contribution_matrix[:, j]
    
    return result_df

# ======================================
# 8. 结果整合与可视化
# ======================================
def visualize_results(samples, sources, report, pollutants, output_dir):
    """
    创建多维度可视化分析
    """
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    
    # 1. 空间污染分布与污染源位置 - 独立图表
    plt.figure(figsize=(10, 8))
    total_pollution = samples[pollutants].sum(axis=1)
    total_pollution = np.maximum(total_pollution, 0)
    log_total = np.zeros_like(total_pollution)
    for i, val in enumerate(total_pollution):
        if val > 0: log_total[i] = np.log1p(val)
    
    samples_plot = samples.copy()
    samples_plot['log_total'] = log_total
    
    # 修复坐标列名：使用'lon'和'lat'
    scatter = sns.scatterplot(
        x='lon', y='lat', size='log_total', hue='log_total',  # 修复这里
        data=samples_plot, palette='viridis', alpha=0.7, legend='brief'
    )
    
    # 按污染源类型区分标记
    source_types = sources['source_type'].unique()
    markers = ['o', 's', 'D', '^']  # 圆形、方形、菱形、三角形
    colors = ['red', 'blue', 'green', 'purple']
    
    for i, source_type in enumerate(source_types):
        type_sources = sources[sources['source_type'] == source_type]
        plt.scatter(
            type_sources['lon'], type_sources['lat'], 
            s=100, c=colors[i], marker=markers[i], 
            label=f'{source_type}污染源', alpha=0.8
        )
    
    plt.title('污染分布与污染源位置')
    plt.xlabel('经度')
    plt.ylabel('纬度')
    plt.legend(loc='best', fontsize=9)
    
    # 保存图1
    img_path1 = os.path.join(output_dir, 'pollution_distribution.png')
    if os.path.exists(img_path1):
        os.remove(img_path1)
    plt.savefig(img_path1, dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 聚类分析结果 - 独立图表
    plt.figure(figsize=(10, 8))
    
    # 计算聚类数量
    pollution_clusters = report['pollution_cluster'].nunique()
    spatial_clusters = report['spatial_cluster'].nunique()
    
    # 为聚类结果分配更丰富的颜色
    pollution_palette = sns.color_palette("husl", pollution_clusters)
    spatial_markers = ['o', 's', 'D', '^', 'v', '<', '>', 'p'] * 2  # 扩展标记样式
    
    sns.scatterplot(
        x='longitude', y='latitude', 
        hue='pollution_cluster', 
        style='spatial_cluster',
        palette=pollution_palette,
        markers=spatial_markers[:spatial_clusters],
        data=report, 
        s=80,
        legend="full"
    )
    
    plt.scatter(
        sources['lon'], sources['lat'], s=100, c='black', marker='X', label='污染源'
    )
    plt.title('污染和空间聚类')
    
    # 添加图例说明
    handles, labels = plt.gca().get_legend_handles_labels()
    # 过滤掉污染源标记
    valid_handles = [h for h, l in zip(handles, labels) if '污染源' not in l]
    valid_labels = [l for l in labels if '污染源' not in l]
    plt.legend(valid_handles, valid_labels, loc='best', fontsize=8)
    
    # 保存图2
    img_path2 = os.path.join(output_dir, 'pollution_clustering.png')
    if os.path.exists(img_path2):
        os.remove(img_path2)
    plt.savefig(img_path2, dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 污染源贡献热力图 - 拆分为4个独立图表
    contrib_columns = [f'contrib_{sid}' for sid in sources['sample']]
    contrib_data = report[contrib_columns]
    
    # 为每种污染源类型创建独立热力图
    for source_type in SOURCE_TYPES:
        # 获取该类型的所有污染源ID
        type_source_ids = sources[sources['source_type'] == source_type]['sample'].tolist()
        type_columns = [f'contrib_{sid}' for sid in type_source_ids]
        
        # 只保留该类型的贡献数据
        type_data = contrib_data[type_columns]
        
        if not type_data.empty:
            plt.figure(figsize=(12, 8))
            
            # 简化列名（去掉 "contrib_" 前缀）
            type_data.columns = [col.split('_')[-1] for col in type_data.columns]
            
            # 绘制热力图
            sns.heatmap(
                type_data, cmap='YlOrRd',
                yticklabels=report['sample'],  # 使用土壤样品编号作为纵坐标
                xticklabels=type_data.columns,  # 使用污染源样品编号作为横坐标
                cbar_kws={'label': '贡献比例'}
            )
            plt.title(f'{source_type}污染源贡献热力图', fontsize=10)  # 修改：添加fontsize参数
            plt.ylabel('土壤样品编号')
            plt.xlabel(f'{source_type}污染源样品编号')
            plt.xticks(rotation=45, fontsize=8)
            plt.yticks(fontsize=8)
            
            # 保存热力图
            img_path = os.path.join(output_dir, f'contribution_heatmap_{source_type}.png')
            if os.path.exists(img_path):
                os.remove(img_path)
            plt.savefig(img_path, dpi=300, bbox_inches='tight')
            plt.close()
    
    # 4. 置信度分布 - 独立图表
    plt.figure(figsize=(10, 6))
    sns.histplot(report['confidence'], bins=20, kde=True)
    plt.title('置信度分布')
    plt.xlabel('置信度 (0-5)')
    
    # 保存图4
    img_path4 = os.path.join(output_dir, 'confidence_distribution.png')
    if os.path.exists(img_path4):
        os.remove(img_path4)
    plt.savefig(img_path4, dpi=300, bbox_inches='tight')
    plt.close()
    
    
    # 重构报告格式：保留原有匹配列并添加中文排名列
    # 获取所有贡献列
    contrib_cols = [col for col in report.columns if col.startswith('contrib_')]
    
    # 创建新的列名（中文）
    new_columns = []
    for i in range(1, 6):
        new_columns.extend([f'排名{i}_污染源', f'排名{i}_贡献率(%)'])
    
    # 创建新的DataFrame存储重构后的数据
    reformed_report = pd.DataFrame(index=report.index, columns=[
        '样品编号', '经度', '纬度', '污染聚类', '空间聚类', '置信度',
        '最佳匹配源', '最佳匹配相似度', '次佳匹配源', '次佳匹配相似度', '三佳匹配源', '三佳匹配相似度'
    ] + new_columns)
    
    # 填充基本列
    reformed_report['样品编号'] = report['sample']
    reformed_report['经度'] = report['lon']
    reformed_report['纬度'] = report['lat']
    reformed_report['污染聚类'] = report['pollution_cluster']
    reformed_report['空间聚类'] = report['spatial_cluster']
    reformed_report['置信度'] = report['confidence']
    
    # 保留原有匹配列（转换为中文）
    reformed_report['最佳匹配源'] = report['top_1_match']
    reformed_report['最佳匹配相似度'] = report['top_1_sim']
    reformed_report['次佳匹配源'] = report['top_2_match']
    reformed_report['次佳匹配相似度'] = report['top_2_sim']
    reformed_report['三佳匹配源'] = report['top_3_match']
    reformed_report['三佳匹配相似度'] = report['top_3_sim']
    
    # 处理每个样本的贡献数据
    for idx, row in report.iterrows():
        # 获取当前样本的所有贡献值
        contrib_values = row[contrib_cols]
        
        # 按贡献值降序排序
        sorted_contrib = contrib_values.sort_values(ascending=False)
        
        # 提取前5个污染源及其贡献率
        for rank in range(1, 6):
            if rank <= len(sorted_contrib):
                # 获取污染源ID（去掉"contrib_"前缀）
                source_id = sorted_contrib.index[rank-1].replace('contrib_', '')
                # 贡献率转换为百分比
                contrib_percent = sorted_contrib.iloc[rank-1] * 100
            else:
                source_id = ''
                contrib_percent = 0.0
                
            # 填充到新报告中
            reformed_report.at[idx, f'排名{rank}_污染源'] = source_id
            reformed_report.at[idx, f'排名{rank}_贡献率(%)'] = contrib_percent
    
    # 导出CSV报告
    csv_path = os.path.join(output_dir, 'pollution_attribution_report.csv')
    if os.path.exists(csv_path):
        os.remove(csv_path)
    reformed_report.to_csv(csv_path, index=False)
    print(f"已保存CSV报告到: {csv_path}")

# ======================================
# 3. 数据加载与处理
# ======================================
def load_data(data_dir):
    """加载所有数据文件"""
    data_files = {
        'soil': 'soil.csv',
        'atmosphere': 'atmosphere.csv',
        'irrigation': 'irrigation.csv',
        'pesticide': 'pesticide.csv',
        'manure': 'manure.csv'
    }
    
    datasets = {}
    
    for data_type, filename in data_files.items():
        filepath = os.path.join(data_dir, filename)
        if os.path.exists(filepath):
            try:
                # 尝试多种编码
                for encoding in ['utf-8', 'gbk', 'latin1']:
                    try:
                        df = pd.read_csv(filepath, encoding=encoding)
                        datasets[data_type] = df
                        print(f"成功加载: {filename} ({len(df)} 条记录)")
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    print(f"警告: 无法解析 {filename}，跳过")
            except Exception as e:
                print(f"加载 {filename} 出错: {str(e)}")
        else:
            print(f"警告: 文件不存在 {filename}")
    
    return datasets

# ======================================
# 4. 结果导出
# ======================================
def export_results(results, output_dir):
    """导出结果为JSON文件"""
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, 'pollution_source_contributions.json')
    
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"结果已导出到: {output_path}")
    except Exception as e:
        print(f"导出结果失败: {str(e)}")

# ======================================
# 5. 主工作流程
# ======================================
def main():
    # 配置路径
    data_dir = r"D:\python\机器学习\精准溯源（无工业数据版）\data"
    output_dir = r"D:\python\机器学习\精准溯源（无工业数据版）\results"
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载数据
    print("==== 加载数据 ====")
    datasets = load_data(data_dir)
    
    # 检查必需数据
    if 'soil' not in datasets:
        print("错误: 缺少土壤数据!")
        return
    
    # 分离土壤数据和污染源数据
    soil_data = datasets.pop('soil')
    source_data_dict = datasets
    
    # 合并所有污染源数据
    all_sources = pd.DataFrame()
    for source_type, df in source_data_dict.items():
        if not df.empty:
            df['source_type'] = source_type
            all_sources = pd.concat([all_sources, df], ignore_index=True)
    
    if all_sources.empty:
        print("错误: 缺少污染源数据!")
        return
    
    # 构建污染源指纹
    print("==== 构建污染源指纹 ====")
    source_fingerprints = build_source_fingerprints(all_sources, METALS)
    
    # 构建采样点指纹
    print("==== 构建采样点指纹 ====")
    receptor_fingerprints = build_receptor_fingerprints(soil_data, METALS)
    
    # 计算指纹相似度
    print("==== 计算指纹相似度 ====")
    similarity_matrix = calculate_fingerprint_similarity(source_fingerprints, receptor_fingerprints)
    
    # 深度聚类分析 - 增加最小聚类数
    print("==== 深度聚类分析 ====")
    encoded_features = train_autoencoder(receptor_fingerprints)
    sample_clusters = cluster_analysis(encoded_features, min_clusters=3)
    
    # 空间图神经网络 - 增加最小聚类数
    print("==== 空间图神经网络 ====")
    graph_data = build_graph_data(soil_data, receptor_fingerprints)
    gnn_embeddings = train_gnn(graph_data)
    spatial_clusters = cluster_analysis(gnn_embeddings, min_clusters=3)
    
    # 最优传输模型
    print("==== 最优传输模型 ====")
    contribution_matrix = optimal_transport_model(
        source_fingerprints, receptor_fingerprints,
        soil_data, all_sources, METALS
    )
    
    # === 新增: 空间校正和不确定性量化已在integrate_results中调用 ===
    
    # 结果整合
    print("==== 结果整合 ====")
    attribution_report = integrate_results(
        soil_data, all_sources, contribution_matrix,
        similarity_matrix, sample_clusters, spatial_clusters
    )
    
    # 可视化 - 修复：传递output_dir参数
    print("==== 可视化结果 ====")
    visualize_results(soil_data, all_sources, attribution_report, METALS, output_dir)
    
    # 导出结果
    print("==== 导出结果 ====")
    export_results(attribution_report.to_dict('records'), output_dir)
    
    print("处理完成!")




if __name__ == "__main__":
    main()