"""
真实数据分析程序 - 基于实际土壤和污染源数据的完整分析

该程序使用真实的数据文件进行污染源解析：
- 土壤样本: 114个样本
- 污染源: 213个源（大气、灌溉、农药、有机肥、工业）

作者: 环境科学与机器学习团队
"""

import os
import sys
import numpy as np
import pandas as pd
import warnings
from typing import Optional, Dict, Any
import logging

# 导入模块
from config import Config, get_config
from data_processor import RealDataProcessor, REAL_METALS, REAL_SOURCE_TYPES
from pmf_analyzer import PMFAnalyzer
from feature_engineer import AdvancedFeatureEngineer
from spatial_validation import SpatialCrossValidator
from deep_learning import DeepLearningProcessor, DeepLearningPredictor
from optimization import OptimalTransportModel
from visualization import AcademicVisualizer
from result_analyzer import ResultAnalyzer

warnings.filterwarnings('ignore')

class RealDataAnalyzer:
    """真实数据分析器"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        初始化分析器
        
        Args:
            config: 配置对象
        """
        self.config = config or get_config()
        
        # 设置日志
        self._setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 初始化各个模块
        # 确定数据目录路径 - 相对于当前脚本所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        data_dir = os.path.join(current_dir, 'data')
        self.data_processor = RealDataProcessor(data_dir)
        self.pmf_analyzer = PMFAnalyzer(self.config)
        self.feature_engineer = AdvancedFeatureEngineer()
        self.spatial_validator = SpatialCrossValidator()
        self.deep_learning_processor = DeepLearningProcessor(self.config)
        self.deep_learning_predictor = DeepLearningPredictor(self.config)  # 新增：端到端预测器
        self.optimal_transport = OptimalTransportModel()
        self.visualizer = AcademicVisualizer(self.config)
        self.result_analyzer = ResultAnalyzer(self.config)
        
        # 存储分析结果
        self.soil_data = None
        self.source_data = None
        self.analysis_results = {}
        
    def _setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('real_data_analysis.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    def run_complete_analysis(self, output_dir: str = 'real_results') -> Dict[str, Any]:
        """
        运行完整的真实数据分析
        
        Args:
            output_dir: 输出目录
            
        Returns:
            Dict: 完整的分析结果
        """
        try:
            self.logger.info("=" * 80)
            self.logger.info("开始真实数据污染源解析分析")
            self.logger.info("=" * 80)
            
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 第一步：加载真实数据
            self.logger.info("第一步：加载真实数据")
            self._load_real_data()
            
            # 第二步：PMF源解析
            self.logger.info("第二步：PMF源解析分析")
            self._run_pmf_analysis()
            
            # 第三步：特征工程
            self.logger.info("第三步：高级特征工程")
            self._run_feature_engineering()
            
            # 第四步：深度学习分析
            self.logger.info("第四步：深度学习分析")
            self._run_deep_learning_analysis()
            
            # 第五步：最优传输模型
            self.logger.info("第五步：最优传输模型")
            self._run_optimal_transport_analysis()
            
            # 第六步：空间验证
            self.logger.info("第六步：空间交叉验证")
            self._run_spatial_validation()
            
            # 第七步：结果整合
            self.logger.info("第七步：结果整合")
            final_results = self._integrate_results()
            
            # 第八步：可视化
            self.logger.info("第八步：生成可视化")
            self._generate_visualizations(output_dir)
            
            # 第九步：导出结果
            self.logger.info("第九步：导出结果")
            self._export_results(final_results, output_dir)
            
            self.logger.info("=" * 80)
            self.logger.info("真实数据分析完成！")
            self.logger.info("=" * 80)
            
            return {
                'final_results': final_results,
                'analysis_components': self.analysis_results,
                'output_directory': output_dir
            }
            
        except Exception as e:
            self.logger.error(f"分析过程中发生错误: {str(e)}")
            raise
    
    def _load_real_data(self) -> None:
        """加载真实数据"""
        # 加载土壤数据
        self.soil_data = self.data_processor.load_soil_data()
        self.logger.info(f"成功加载 {len(self.soil_data)} 个土壤样本")
        
        # 加载污染源数据
        self.source_data = self.data_processor.load_all_source_data()
        self.logger.info(f"成功加载 {len(self.source_data)} 个污染源")
        
        # 生成数据摘要
        data_summary = self.data_processor.get_data_summary(self.soil_data, self.source_data)
        self.analysis_results['data_summary'] = data_summary
        
        self.logger.info("真实数据加载完成")
    
    def _run_pmf_analysis(self) -> None:
        """运行PMF分析"""
        # 构建污染源指纹
        source_fingerprints = self.pmf_analyzer.build_source_fingerprints(
            self.source_data, REAL_METALS
        )
        
        # 构建受体指纹
        receptor_fingerprints = self.pmf_analyzer.build_receptor_fingerprints(
            self.soil_data, REAL_METALS
        )
        
        # 计算指纹相似度
        similarity_matrix = self.pmf_analyzer.calculate_fingerprint_similarity(
            source_fingerprints, receptor_fingerprints
        )
        
        # PMF源解析
        W, H = self.pmf_analyzer.run_pmf_analysis(self.soil_data, REAL_METALS)

        # 应用风向校正到PMF贡献矩阵（基于2.5.py的改进）
        W_corrected = self.pmf_analyzer.apply_wind_correction_to_contribution(
            W, self.soil_data, self.source_data
        )

        # 解释源成分谱
        source_interpretations = self.pmf_analyzer.interpret_source_profiles(REAL_METALS)

        # 存储结果
        self.analysis_results.update({
            'source_fingerprints': source_fingerprints,
            'receptor_fingerprints': receptor_fingerprints,
            'similarity_matrix': similarity_matrix,
            'pmf_contributions': W_corrected,  # 使用校正后的贡献矩阵
            'pmf_profiles': H,
            'source_interpretations': source_interpretations,
            'pmf_contributions_original': W  # 保存原始PMF结果用于对比
        })
        
        self.logger.info("PMF分析完成")
    
    def _run_feature_engineering(self) -> None:
        """运行特征工程"""
        # 创建高级特征
        engineered_features = self.feature_engineer.fit_transform(
            self.soil_data, self.source_data, REAL_METALS
        )
        
        # 获取特征重要性分组
        feature_groups = self.feature_engineer.get_feature_importance_groups()
        
        # 存储结果
        self.analysis_results.update({
            'engineered_features': engineered_features,
            'feature_groups': feature_groups
        })
        
        self.logger.info(f"特征工程完成，共创建 {engineered_features.shape[1]} 个特征")
    
    def _run_deep_learning_analysis(self) -> None:
        """运行深度学习分析"""
        # 自编码器降维
        encoded_features = self.deep_learning_processor.train_autoencoder(
            self.analysis_results['receptor_fingerprints']
        )
        
        # 构建图数据
        graph_data = self.deep_learning_processor.build_graph_data(
            self.soil_data, self.analysis_results['receptor_fingerprints']
        )
        
        # 训练GNN
        gnn_embeddings = self.deep_learning_processor.train_gnn(graph_data)
        
        # 无监督贡献率预测
        dl_contributions = self.deep_learning_processor.train_unsupervised_contribution_model(
            self.analysis_results['receptor_fingerprints'],
            self.analysis_results['source_fingerprints'],
            self.soil_data,
            self.source_data
        )

        # 新增：训练端到端预测模型
        if 'pmf_contributions' in self.analysis_results:
            print("训练端到端污染源贡献率预测模型...")
            self.deep_learning_predictor.train_model(
                self.soil_data,
                self.source_data,
                self.analysis_results['pmf_contributions'],
                epochs=100
            )

            # 测试预测功能
            test_location = [self.soil_data['lon'].mean(), self.soil_data['lat'].mean()]
            predicted_contributions, attention_weights = self.deep_learning_predictor.predict_contributions(
                test_location, self.source_data
            )
            print(f"测试预测结果 - 大气:{predicted_contributions[0]:.3f}, 灌溉:{predicted_contributions[1]:.3f}, "
                  f"农药:{predicted_contributions[2]:.3f}, 有机肥:{predicted_contributions[3]:.3f}")

        # 存储结果
        self.analysis_results.update({
            'encoded_features': encoded_features,
            'gnn_embeddings': gnn_embeddings,
            'dl_contributions': dl_contributions,
            'end_to_end_predictor': self.deep_learning_predictor if self.deep_learning_predictor.is_trained else None
        })

        self.logger.info("深度学习分析完成")
    
    def _run_optimal_transport_analysis(self) -> None:
        """运行最优传输分析"""
        # 计算最优传输贡献矩阵
        ot_contributions = self.optimal_transport.calculate_contribution_matrix(
            self.analysis_results['source_fingerprints'],
            self.analysis_results['receptor_fingerprints'],
            self.soil_data,
            self.source_data,
            REAL_METALS
        )
        
        # 存储结果
        self.analysis_results['ot_contributions'] = ot_contributions
        
        self.logger.info("最优传输分析完成")
    
    def _run_spatial_validation(self) -> None:
        """运行空间交叉验证"""
        # 准备验证数据
        coordinates = self.soil_data[['lon', 'lat']].values
        features = self.analysis_results['engineered_features'].values
        
        # 使用PMF贡献作为目标变量进行验证
        pmf_contributions = self.analysis_results['pmf_contributions']
        
        validation_results = {}
        for i in range(pmf_contributions.shape[1]):
            target = pmf_contributions[:, i]
            
            # 空间交叉验证
            cv_results = []
            for train_idx, test_idx in self.spatial_validator.spatial_split(coordinates, target):
                # 训练简单的回归模型进行验证
                from sklearn.ensemble import RandomForestRegressor
                model = RandomForestRegressor(n_estimators=100, random_state=42)
                
                X_train, X_test = features[train_idx], features[test_idx]
                y_train, y_test = target[train_idx], target[test_idx]
                
                model.fit(X_train, y_train)
                y_pred = model.predict(X_test)
                
                from sklearn.metrics import r2_score
                score = r2_score(y_test, y_pred)
                cv_results.append(score)
            
            validation_results[f'source_{i+1}'] = {
                'mean_r2': np.mean(cv_results),
                'std_r2': np.std(cv_results),
                'cv_scores': cv_results
            }
        
        self.analysis_results['spatial_validation'] = validation_results
        
        self.logger.info("空间交叉验证完成")
    
    def _integrate_results(self) -> pd.DataFrame:
        """整合所有分析结果"""
        # 使用最优传输结果作为主要贡献矩阵
        contribution_matrix = self.analysis_results['ot_contributions']
        similarity_matrix = self.analysis_results['similarity_matrix']
        
        # 整合结果
        final_results = self.result_analyzer.integrate_results(
            self.soil_data,
            self.source_data,
            contribution_matrix,
            similarity_matrix
        )
        
        return final_results
    
    def _generate_visualizations(self, output_dir: str) -> None:
        """生成可视化图表"""
        # 获取最终结果
        final_results = self._integrate_results()
        
        # 生成结果可视化
        self.visualizer.visualize_results(
            self.soil_data, self.source_data, final_results, REAL_METALS, output_dir
        )
        
        # 生成训练过程可视化
        self.visualizer.visualize_training_process(
            self.soil_data, self.source_data, final_results, REAL_METALS, output_dir
        )
        
        # 生成风向影响分析
        self.visualizer.visualize_wind_influence_analysis(
            self.soil_data, self.source_data, output_dir
        )
        
        # 导出可视化摘要
        self.visualizer.export_visualization_summary(output_dir)
        
        self.logger.info("可视化图表生成完成")
    
    def _export_results(self, final_results: pd.DataFrame, output_dir: str) -> None:
        """导出分析结果"""
        # 导出主要结果
        self.result_analyzer.export_results(final_results, output_dir)
        
        # 保存文本报告
        self.result_analyzer.save_text_report(final_results, output_dir)
        
        # 导出PMF结果
        self.pmf_analyzer.export_results(output_dir, self.soil_data, REAL_METALS)
        
        # 导出处理后的原始数据
        self.data_processor.export_processed_data(self.soil_data, self.source_data, 
                                                 os.path.join(output_dir, 'processed_data'))
        
        self.logger.info("分析结果导出完成")

def main():
    """主函数"""
    try:
        # 创建分析器并运行分析
        analyzer = RealDataAnalyzer()
        results = analyzer.run_complete_analysis('real_results')
        
        print("\n" + "=" * 60)
        print("真实数据分析完成！主要结果:")
        print("=" * 60)
        
        # 打印摘要信息
        final_results = results['final_results']
        summary_stats = analyzer.result_analyzer.generate_summary_statistics(final_results)
        
        print(f"土壤样本数: {summary_stats['total_samples']}")
        print(f"平均置信度: {summary_stats['confidence_stats']['mean']:.2f}")
        print(f"高置信度样本: {summary_stats['high_confidence_samples']} ({summary_stats['high_confidence_samples']/summary_stats['total_samples']*100:.1f}%)")
        
        print(f"\n污染源类型分布:")
        for source_type, count in summary_stats['main_source_distribution'].items():
            percentage = count / summary_stats['total_samples'] * 100
            print(f"  {source_type}: {count} 样本 ({percentage:.1f}%)")
        
        print(f"\n详细结果已保存到: real_results/")
        print("- attribution_results.csv: 主要分析结果")
        print("- analysis_report.txt: 文本格式报告")
        print("- *.png: 可视化图表")
        print("- processed_data/: 处理后的原始数据")
        
    except Exception as e:
        print(f"分析失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
