"""
优化算法模块

该模块包含：
1. 最优传输模型
2. TPE超参数优化
3. 不确定性量化
4. 蒙特卡洛方法
5. 集成优化框架


"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import warnings
from functools import lru_cache

# 优化库
from pulp import *
from scipy.spatial.distance import cdist
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import QuantileRegressor

# 深度学习
import torch
import torch.nn as nn

# 高级优化库
try:
    import optuna
    from optuna.samplers import TPESampler
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False
    print("Warning: Optuna not available. TPE optimization will be disabled.")

from config import Config, get_config

warnings.filterwarnings('ignore')

class OptimalTransportModel:
    """最优传输模型"""
    
    def __init__(self, spatial_weight: float = 0.8, fingerprint_weight: float = 0.2):
        """
        初始化最优传输模型
        
        Args:
            spatial_weight: 空间距离权重
            fingerprint_weight: 指纹相似度权重
        """
        self.spatial_weight = spatial_weight
        self.fingerprint_weight = fingerprint_weight
        
    def calculate_contribution_matrix(self, sources: np.ndarray, receptors: np.ndarray,
                                    samples_data: pd.DataFrame, source_data: pd.DataFrame,
                                    pollutants: List[str]) -> np.ndarray:
        """
        使用最优传输量化污染贡献
        
        Args:
            sources: 源指纹矩阵
            receptors: 受体指纹矩阵
            samples_data: 样本数据
            source_data: 源数据
            pollutants: 污染物列表
            
        Returns:
            np.ndarray: 贡献率矩阵
        """
        try:
            print("使用最优传输量化污染贡献...")

            # 准备污染源和采样点位置
            source_coords = source_data[['lon', 'lat']].values
            sample_coords = samples_data[['lon', 'lat']].values

            # 计算空间距离成本 - 使用指数衰减增强距离影响
            spatial_dist = cdist(sample_coords, source_coords, 'euclidean')
            max_dist = np.max(spatial_dist)
            normalized_dist = spatial_dist / max_dist

            # 应用指数衰减：距离越远，成本指数增长
            spatial_cost = np.exp(2 * normalized_dist) - 1  # 指数衰减，让近距离源更有优势

            # 计算指纹差异成本
            similarity = cosine_similarity(receptors, sources)
            fingerprint_cost = 1 - similarity

            # 组合成本函数 - 提高空间权重，确保距离近的源有更高贡献
            total_cost = self.spatial_weight * spatial_cost + self.fingerprint_weight * fingerprint_cost

            # 创建线性规划问题
            prob = LpProblem("Pollution_Attribution", LpMinimize)

            num_samples, num_sources = total_cost.shape

            # 决策变量 - 每个样本到每个源的传输量
            x = {}
            for i in range(num_samples):
                for j in range(num_sources):
                    x[i, j] = LpVariable(f"x_{i}_{j}", lowBound=0, upBound=1)

            # 目标函数：最小化总传输成本
            prob += lpSum([total_cost[i, j] * x[i, j]
                          for i in range(num_samples)
                          for j in range(num_sources)])

            # 约束条件：每个样本的贡献率之和为1
            for i in range(num_samples):
                prob += lpSum([x[i, j] for j in range(num_sources)]) == 1

            # 求解线性规划问题
            prob.solve(PULP_CBC_CMD(msg=0))

            # 提取贡献矩阵
            contribution_matrix = np.zeros((num_samples, num_sources))
            for i in range(num_samples):
                for j in range(num_sources):
                    contribution_matrix[i, j] = x[i, j].varValue if x[i, j].varValue else 0

            # 确保每行和为1（归一化）
            row_sums = contribution_matrix.sum(axis=1)
            row_sums[row_sums == 0] = 1  # 避免除零
            contribution_matrix = contribution_matrix / row_sums[:, np.newaxis]

            print(f"最优传输计算完成，贡献矩阵形状: {contribution_matrix.shape}")
            print(f"贡献率范围: [{contribution_matrix.min():.4f}, {contribution_matrix.max():.4f}]")

            return contribution_matrix

        except Exception as e:
            raise Exception(f"最优传输模型计算失败: {str(e)}")

class UncertaintyQuantifier:
    """
    集成不确定性量化框架

    该类实现了多种不确定性量化方法，包括：
    1. 分位数回归 (Quantile Regression)
    2. 蒙特卡洛方法 (Monte Carlo)
    3. 集成方法的不确定性 (Ensemble Uncertainty)
    """

    def __init__(self, n_estimators: int = 100, quantiles: List[float] = [0.05, 0.95], 
                 method: str = 'quantile'):
        self.n_estimators = n_estimators
        self.quantiles = quantiles
        self.method = method
        self.models = {}
        self.is_fitted = False

    def fit(self, X: np.ndarray, y: np.ndarray) -> None:
        """
        训练不确定性量化模型

        Args:
            X: 特征矩阵
            y: 目标变量
        """
        if self.method == 'quantile':
            self._fit_quantile_regression(X, y)
        elif self.method == 'ensemble':
            self._fit_ensemble_uncertainty(X, y)
        elif self.method == 'monte_carlo':
            self._fit_monte_carlo_uncertainty(X, y)
        else:
            raise ValueError(f"Unknown uncertainty method: {self.method}")

        self.is_fitted = True

    def _fit_quantile_regression(self, X: np.ndarray, y: np.ndarray) -> None:
        """拟合分位数回归模型"""
        for quantile in self.quantiles:
            model = QuantileRegressor(quantile=quantile, alpha=0.1)
            model.fit(X, y)
            self.models[f'quantile_{quantile}'] = model

        # 添加均值模型
        mean_model = RandomForestRegressor(n_estimators=100, random_state=42)
        mean_model.fit(X, y)
        self.models['mean'] = mean_model

    def _fit_ensemble_uncertainty(self, X: np.ndarray, y: np.ndarray) -> None:
        """拟合集成不确定性模型"""
        self.models['ensemble'] = []
        
        for i in range(self.n_estimators):
            # 使用bootstrap采样
            n_samples = len(X)
            bootstrap_idx = np.random.choice(n_samples, n_samples, replace=True)
            
            model = RandomForestRegressor(n_estimators=50, random_state=i)
            model.fit(X[bootstrap_idx], y[bootstrap_idx])
            self.models['ensemble'].append(model)

    def _fit_monte_carlo_uncertainty(self, X: np.ndarray, y: np.ndarray) -> None:
        """拟合蒙特卡洛不确定性模型"""
        from deep_learning import MCDropoutNet
        
        self.mc_model = MCDropoutNet(X.shape[1])
        self._train_mc_model(X, y)
        self.models['monte_carlo'] = self.mc_model

    def _train_mc_model(self, X: np.ndarray, y: np.ndarray) -> None:
        """训练蒙特卡洛模型"""
        X_tensor = torch.tensor(X, dtype=torch.float32)
        y_tensor = torch.tensor(y, dtype=torch.float32).reshape(-1, 1)

        optimizer = torch.optim.Adam(self.mc_model.parameters(), lr=0.001)
        criterion = nn.MSELoss()

        for epoch in range(200):
            optimizer.zero_grad()
            outputs = self.mc_model(X_tensor)
            loss = criterion(outputs, y_tensor)
            loss.backward()
            optimizer.step()

    def predict_with_uncertainty(self, X: np.ndarray) -> Dict[str, np.ndarray]:
        """
        预测均值和不确定性区间

        Args:
            X: 特征矩阵

        Returns:
            Dict: 包含均值、不确定性和分位数预测的字典
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")

        if self.method == 'quantile':
            return self._predict_quantile_uncertainty(X)
        elif self.method == 'ensemble':
            return self._predict_ensemble_uncertainty(X)
        elif self.method == 'monte_carlo':
            return self._predict_monte_carlo_uncertainty(X)

    def _predict_quantile_uncertainty(self, X: np.ndarray) -> Dict[str, np.ndarray]:
        """分位数不确定性预测"""
        predictions = {}
        
        # 均值预测
        predictions['mean'] = self.models['mean'].predict(X)
        
        # 分位数预测
        for quantile in self.quantiles:
            model_key = f'quantile_{quantile}'
            predictions[f'quantile_{quantile}'] = self.models[model_key].predict(X)
        
        # 计算不确定性
        lower_bound = predictions[f'quantile_{self.quantiles[0]}']
        upper_bound = predictions[f'quantile_{self.quantiles[1]}']
        predictions['uncertainty'] = (upper_bound - lower_bound) / 2
        predictions['lower_bound'] = lower_bound
        predictions['upper_bound'] = upper_bound
        
        return predictions

    def _predict_ensemble_uncertainty(self, X: np.ndarray) -> Dict[str, np.ndarray]:
        """集成不确定性预测"""
        predictions_list = []
        
        for model in self.models['ensemble']:
            pred = model.predict(X)
            predictions_list.append(pred)
        
        predictions_array = np.array(predictions_list)
        
        return {
            'mean': np.mean(predictions_array, axis=0),
            'uncertainty': np.std(predictions_array, axis=0),
            'lower_bound': np.percentile(predictions_array, self.quantiles[0] * 100, axis=0),
            'upper_bound': np.percentile(predictions_array, self.quantiles[1] * 100, axis=0)
        }

    def _predict_monte_carlo_uncertainty(self, X: np.ndarray) -> Dict[str, np.ndarray]:
        """蒙特卡洛不确定性预测"""
        predictions_list = []

        self.mc_model.train()  # 保持dropout激活
        with torch.no_grad():
            X_tensor = torch.tensor(X, dtype=torch.float32)
            for _ in range(100):  # 100次蒙特卡洛采样
                pred = self.mc_model(X_tensor).numpy()
                predictions_list.append(pred.flatten())

        predictions_array = np.array(predictions_list)

        return {
            'mean': np.mean(predictions_array, axis=0),
            'uncertainty': np.std(predictions_array, axis=0),
            'lower_bound': np.percentile(predictions_array, 5, axis=0),
            'upper_bound': np.percentile(predictions_array, 95, axis=0)
        }

class HyperparameterOptimizer:
    """
    基于TPE的超参数优化框架

    该类使用Optuna库实现Tree-structured Parzen Estimator (TPE)
    贝叶斯优化方法，用于自动调优模型超参数。
    """

    def __init__(self, n_trials: int = 100, timeout: int = 3600, direction: str = 'maximize'):
        self.n_trials = n_trials
        self.timeout = timeout
        self.direction = direction
        self.study = None
        self.best_params = None
        self.best_value = None

    def optimize_ensemble_model(self, X_train: np.ndarray, y_train: np.ndarray, 
                               X_val: np.ndarray, y_val: np.ndarray,
                               sources: Optional[pd.DataFrame] = None, 
                               cv_folds: int = 3) -> Tuple[Dict[str, Any], float]:
        """
        优化集成模型超参数

        Args:
            X_train, y_train: 训练数据
            X_val, y_val: 验证数据
            sources: 污染源数据（可选）
            cv_folds: 交叉验证折数

        Returns:
            Tuple: (最优参数, 最优分数)
        """
        if not OPTUNA_AVAILABLE:
            print("Warning: Optuna not available. Using default parameters.")
            return self._get_default_parameters(), 0.0

        def objective(trial):
            try:
                # 最优传输参数 - 提高空间权重范围
                ot_spatial_weight = trial.suggest_float('ot_spatial_weight', 0.7, 0.9)
                ot_fingerprint_weight = 1 - ot_spatial_weight

                # 深度学习参数
                dl_hidden_dims = trial.suggest_categorical(
                    'dl_hidden_dims', [[32, 16], [64, 32], [64, 32, 16], [128, 64, 32]]
                )
                dl_dropout = trial.suggest_float('dl_dropout', 0.1, 0.5)
                dl_lr = trial.suggest_float('dl_lr', 1e-4, 1e-2, log=True)
                dl_epochs = trial.suggest_int('dl_epochs', 50, 200)

                # GNN参数
                gnn_hidden_dim = trial.suggest_categorical('gnn_hidden_dim', [32, 64, 128])
                gnn_num_layers = trial.suggest_int('gnn_num_layers', 2, 5)
                gnn_dropout = trial.suggest_float('gnn_dropout', 0.1, 0.5)

                # 随机森林参数
                rf_n_estimators = trial.suggest_int('rf_n_estimators', 50, 200)
                rf_max_depth = trial.suggest_int('rf_max_depth', 5, 20)
                rf_min_samples_split = trial.suggest_int('rf_min_samples_split', 2, 10)
                rf_min_samples_leaf = trial.suggest_int('rf_min_samples_leaf', 1, 5)

                # 集成权重
                w1 = trial.suggest_float('ensemble_w1', 0.1, 0.5)
                w2 = trial.suggest_float('ensemble_w2', 0.1, 0.5)
                w3 = trial.suggest_float('ensemble_w3', 0.1, 0.5)
                w4 = 1 - w1 - w2 - w3
                ensemble_weights = [w1, w2, w3, w4]

                # 不确定性量化参数
                uq_method = trial.suggest_categorical('uq_method', ['quantile', 'ensemble'])
                uq_quantiles = [0.05, 0.95]  # 固定分位数

                # 构建参数字典
                params = {
                    'ot_weights': (ot_spatial_weight, ot_fingerprint_weight),
                    'dl_params': {
                        'hidden_dims': dl_hidden_dims,
                        'dropout': dl_dropout,
                        'lr': dl_lr,
                        'epochs': dl_epochs
                    },
                    'gnn_params': {
                        'hidden_dim': gnn_hidden_dim,
                        'num_layers': gnn_num_layers,
                        'dropout': gnn_dropout
                    },
                    'rf_params': {
                        'n_estimators': rf_n_estimators,
                        'max_depth': rf_max_depth,
                        'min_samples_split': rf_min_samples_split,
                        'min_samples_leaf': rf_min_samples_leaf
                    },
                    'ensemble_weights': ensemble_weights,
                    'uq_params': {
                        'method': uq_method,
                        'quantiles': uq_quantiles
                    }
                }

                # 训练和评估模型
                score = self._evaluate_model_with_params(
                    params, X_train, y_train, X_val, y_val, sources, cv_folds
                )

                return score

            except Exception as e:
                print(f"Error in trial: {e}")
                return 0.0

        # 创建优化研究
        self.study = optuna.create_study(
            direction=self.direction,
            sampler=TPESampler(seed=42)
        )

        # 执行优化
        print(f"Starting hyperparameter optimization with {self.n_trials} trials...")
        self.study.optimize(
            objective,
            n_trials=self.n_trials,
            timeout=self.timeout
        )

        self.best_params = self.study.best_params
        self.best_value = self.study.best_value

        print(f"Optimization completed. Best score: {self.best_value:.4f}")

        return self.best_params, self.best_value

    def _evaluate_model_with_params(self, params: Dict[str, Any], 
                                   X_train: np.ndarray, y_train: np.ndarray,
                                   X_val: np.ndarray, y_val: np.ndarray,
                                   sources: Optional[pd.DataFrame], 
                                   cv_folds: int) -> float:
        """使用给定参数评估模型"""
        # 这里应该实现具体的模型训练和评估逻辑
        # 为了简化，返回一个模拟的分数
        from sklearn.ensemble import RandomForestRegressor
        from sklearn.metrics import r2_score
        
        # 使用随机森林作为基准模型
        rf_params = params.get('rf_params', {})
        model = RandomForestRegressor(**rf_params, random_state=42)
        model.fit(X_train, y_train)
        
        y_pred = model.predict(X_val)
        score = r2_score(y_val, y_pred)
        
        return score

    def _get_default_parameters(self) -> Dict[str, Any]:
        """获取默认参数"""
        return {
            'ot_weights': (0.6, 0.4),
            'dl_params': {
                'hidden_dims': [64, 32],
                'dropout': 0.2,
                'lr': 0.001,
                'epochs': 100
            },
            'gnn_params': {
                'hidden_dim': 64,
                'num_layers': 3,
                'dropout': 0.2
            },
            'rf_params': {
                'n_estimators': 100,
                'max_depth': 10,
                'min_samples_split': 2,
                'min_samples_leaf': 1
            },
            'ensemble_weights': [0.3, 0.3, 0.2, 0.2],
            'uq_params': {
                'method': 'quantile',
                'quantiles': [0.05, 0.95]
            }
        }

@lru_cache(maxsize=128)
def cached_distance_calculation(coord1: Tuple[float, float], 
                              coord2: Tuple[float, float]) -> float:
    """缓存距离计算结果"""
    return np.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)

def monte_carlo_uncertainty(model, X: np.ndarray, n_samples: int = 1000) -> Tuple[np.ndarray, np.ndarray]:
    """
    独立的蒙特卡洛不确定性估计函数

    Args:
        model: 训练好的模型
        X: 输入特征
        n_samples: 蒙特卡洛采样次数

    Returns:
        Tuple: (均值预测, 标准差)
    """
    predictions = []

    for _ in range(n_samples):
        # 添加输入噪声
        X_noisy = X + np.random.normal(0, 0.01, X.shape)
        pred = model.predict(X_noisy)
        predictions.append(pred)

    predictions = np.array(predictions)
    mean_pred = np.mean(predictions, axis=0)
    std_pred = np.std(predictions, axis=0)

    return mean_pred, std_pred
