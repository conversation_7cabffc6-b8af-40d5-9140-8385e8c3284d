"""
特征工程模块

该模块包含：
1. 空间特征工程
2. 化学交互特征
3. 统计特征工程
4. 时间特征工程
5. 特征标准化

"""

import numpy as np
import pandas as pd
from typing import List, Dict, Optional, Tuple
from sklearn.preprocessing import StandardScaler
import warnings

from config import Config, METALS, SOURCE_TYPES, get_config

warnings.filterwarnings('ignore')

class AdvancedFeatureEngineer:
    """
    高级特征工程框架

    该类实现了多种特征工程方法，包括：
    1. 空间特征工程 (Spatial Feature Engineering)
    2. 化学交互特征 (Chemical Interaction Features)
    3. 时间特征工程 (Temporal Feature Engineering)
    4. 统计特征工程 (Statistical Feature Engineering)

    参数:
        include_interactions (bool): 是否包含交互特征
        spatial_features (bool): 是否包含空间特征
        statistical_features (bool): 是否包含统计特征
    """

    def __init__(self, include_interactions=True, spatial_features=True,
                 statistical_features=True):
        self.include_interactions = include_interactions
        self.spatial_features = spatial_features
        self.statistical_features = statistical_features
        self.scalers = {}
        self.encoders = {}
        self.feature_names = []

    def fit_transform(self, samples: pd.DataFrame, sources: pd.DataFrame, 
                     pollutants: List[str]) -> pd.DataFrame:
        """
        拟合并转换特征

        参数:
            samples (DataFrame): 样本数据
            sources (DataFrame): 污染源数据
            pollutants (list): 污染物列表

        返回:
            DataFrame: 工程化特征
        """
        features_list = []

        # 1. 基础化学特征
        chemical_features = samples[pollutants].copy()
        features_list.append(chemical_features)

        # 2. 空间特征
        if self.spatial_features:
            spatial_features = self.create_spatial_features(samples, sources)
            features_list.append(spatial_features)

        # 3. 统计特征
        if self.statistical_features:
            statistical_features = self.create_statistical_features(samples, pollutants)
            features_list.append(statistical_features)

        # 4. 交互特征
        if self.include_interactions:
            interaction_features = self.create_interaction_features(chemical_features)
            features_list.append(interaction_features)

        # 合并所有特征
        all_features = pd.concat(features_list, axis=1)

        # 特征标准化
        all_features = self._standardize_features(all_features)

        self.feature_names = list(all_features.columns)

        return all_features

    def create_spatial_features(self, samples: pd.DataFrame, 
                              sources: pd.DataFrame) -> pd.DataFrame:
        """创建空间特征"""
        features = []

        for _, sample in samples.iterrows():
            sample_coords = np.array([sample['lon'], sample['lat']])

            # 基础距离特征
            distances = []
            for _, source in sources.iterrows():
                source_coords = np.array([source['lon'], source['lat']])
                dist = np.linalg.norm(sample_coords - source_coords)
                distances.append(dist)

            # 距离统计特征
            dist_features = {
                'min_distance': np.min(distances),
                'mean_distance': np.mean(distances),
                'std_distance': np.std(distances),
                'median_distance': np.median(distances),
                'max_distance': np.max(distances)
            }

            # 空间密度特征
            nearby_sources_5km = np.sum(np.array(distances) < 5.0)
            nearby_sources_10km = np.sum(np.array(distances) < 10.0)
            nearby_sources_20km = np.sum(np.array(distances) < 20.0)

            dist_features.update({
                'source_density_5km': nearby_sources_5km,
                'source_density_10km': nearby_sources_10km,
                'source_density_20km': nearby_sources_20km,
                'source_density_ratio': nearby_sources_5km / (nearby_sources_20km + 1)
            })

            # 方向特征
            angles = []
            for _, source in sources.iterrows():
                source_coords = np.array([source['lon'], source['lat']])
                angle = np.arctan2(
                    source_coords[1] - sample_coords[1],
                    source_coords[0] - sample_coords[0]
                )
                angles.append(angle)

            dist_features.update({
                'dominant_direction': np.mean(angles),
                'direction_variance': np.var(angles),
                'direction_std': np.std(angles)
            })

            # 最近污染源特征
            nearest_idx = np.argmin(distances)
            nearest_source = sources.iloc[nearest_idx]
            dist_features.update({
                'nearest_source_type': nearest_source['source_type'],
                'nearest_source_distance': distances[nearest_idx]
            })

            features.append(dist_features)

        spatial_df = pd.DataFrame(features)

        # 编码分类特征
        if 'nearest_source_type' in spatial_df.columns:
            spatial_df = pd.get_dummies(spatial_df, columns=['nearest_source_type'],
                                      prefix='nearest_type')

        return spatial_df

    def create_statistical_features(self, samples: pd.DataFrame, 
                                  pollutants: List[str]) -> pd.DataFrame:
        """创建统计特征"""
        chemical_data = samples[pollutants]

        statistical_features = {}

        # 单变量统计特征
        for metal in pollutants:
            values = chemical_data[metal]
            statistical_features.update({
                f'{metal}_log': np.log1p(values),
                f'{metal}_sqrt': np.sqrt(values),
                f'{metal}_square': values ** 2,
                f'{metal}_zscore': (values - values.mean()) / values.std(),
                f'{metal}_percentile_rank': values.rank(pct=True)
            })

        # 多变量统计特征
        statistical_features.update({
            'total_metals': chemical_data.sum(axis=1),
            'mean_metals': chemical_data.mean(axis=1),
            'std_metals': chemical_data.std(axis=1),
            'max_metals': chemical_data.max(axis=1),
            'min_metals': chemical_data.min(axis=1),
            'range_metals': chemical_data.max(axis=1) - chemical_data.min(axis=1),
            'cv_metals': chemical_data.std(axis=1) / (chemical_data.mean(axis=1) + 1e-8)
        })

        return pd.DataFrame(statistical_features)

    def create_interaction_features(self, chemical_features: pd.DataFrame) -> pd.DataFrame:
        """创建化学元素交互特征"""
        interactions = {}

        metals = chemical_features.columns

        # 两两交互特征
        for i, metal1 in enumerate(metals):
            for j, metal2 in enumerate(metals[i+1:], i+1):
                # 比值特征
                ratio_name = f'{metal1}_{metal2}_ratio'
                interactions[ratio_name] = (
                    chemical_features[metal1] / (chemical_features[metal2] + 1e-8)
                )

                # 乘积特征
                product_name = f'{metal1}_{metal2}_product'
                interactions[product_name] = (
                    chemical_features[metal1] * chemical_features[metal2]
                )

                # 差值特征
                diff_name = f'{metal1}_{metal2}_diff'
                interactions[diff_name] = (
                    chemical_features[metal1] - chemical_features[metal2]
                )

                # 相关性特征（局部）
                corr_name = f'{metal1}_{metal2}_local_corr'
                # 使用滑动窗口计算局部相关性
                window_size = min(50, len(chemical_features))
                local_corr = []
                for k in range(len(chemical_features)):
                    start_idx = max(0, k - window_size // 2)
                    end_idx = min(len(chemical_features), k + window_size // 2)
                    window_data = chemical_features.iloc[start_idx:end_idx]
                    corr_val = window_data[metal1].corr(window_data[metal2])
                    local_corr.append(corr_val if not np.isnan(corr_val) else 0)

                interactions[corr_name] = local_corr

        return pd.DataFrame(interactions)

    def create_temporal_features(self, samples: pd.DataFrame) -> pd.DataFrame:
        """创建时间特征（如果有时间数据）"""
        # 如果样本数据包含时间信息，可以创建时间特征
        temporal_features = {}

        if 'date' in samples.columns:
            dates = pd.to_datetime(samples['date'])

            temporal_features.update({
                'year': dates.dt.year,
                'month': dates.dt.month,
                'day': dates.dt.day,
                'dayofyear': dates.dt.dayofyear,
                'quarter': dates.dt.quarter,
                'season': dates.dt.month % 12 // 3 + 1,
                'is_summer': (dates.dt.month.isin([6, 7, 8])).astype(int),
                'is_winter': (dates.dt.month.isin([12, 1, 2])).astype(int)
            })

        return pd.DataFrame(temporal_features) if temporal_features else pd.DataFrame()

    def _standardize_features(self, features: pd.DataFrame) -> pd.DataFrame:
        """标准化特征"""
        numeric_columns = features.select_dtypes(include=[np.number]).columns

        for col in numeric_columns:
            if col not in self.scalers:
                self.scalers[col] = StandardScaler()
                features[col] = self.scalers[col].fit_transform(features[[col]])
            else:
                features[col] = self.scalers[col].transform(features[[col]])

        return features

    def transform(self, samples: pd.DataFrame, sources: pd.DataFrame, 
                 pollutants: List[str]) -> pd.DataFrame:
        """转换新数据（使用已拟合的转换器）"""
        features_list = []

        # 基础化学特征
        chemical_features = samples[pollutants].copy()
        features_list.append(chemical_features)

        # 空间特征
        if self.spatial_features:
            spatial_features = self.create_spatial_features(samples, sources)
            features_list.append(spatial_features)

        # 统计特征
        if self.statistical_features:
            statistical_features = self.create_statistical_features(samples, pollutants)
            features_list.append(statistical_features)

        # 交互特征
        if self.include_interactions:
            interaction_features = self.create_interaction_features(chemical_features)
            features_list.append(interaction_features)

        # 合并特征
        all_features = pd.concat(features_list, axis=1)

        # 应用已拟合的标准化
        all_features = self._standardize_features(all_features)

        return all_features

    def get_feature_importance_groups(self) -> Dict[str, List[str]]:
        """获取特征重要性分组"""
        groups = {
            'chemical_features': [],
            'spatial_features': [],
            'statistical_features': [],
            'interaction_features': []
        }

        for feature_name in self.feature_names:
            if any(metal in feature_name for metal in METALS) and '_' not in feature_name:
                groups['chemical_features'].append(feature_name)
            elif any(keyword in feature_name for keyword in ['distance', 'density', 'direction', 'nearest']):
                groups['spatial_features'].append(feature_name)
            elif any(keyword in feature_name for keyword in ['log', 'sqrt', 'square', 'zscore', 'total', 'mean', 'std']):
                groups['statistical_features'].append(feature_name)
            elif '_' in feature_name and any(keyword in feature_name for keyword in ['ratio', 'product', 'diff', 'corr']):
                groups['interaction_features'].append(feature_name)

        return groups
