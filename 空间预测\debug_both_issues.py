#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
综合调试脚本

同时调试PyTorch Geometric依赖问题和张量维度不匹配问题
"""

import torch
import numpy as np
import logging
import traceback
import sys

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_pytorch_geometric_status():
    """测试PyTorch Geometric状态"""
    print("=" * 60)
    print("测试PyTorch Geometric状态")
    print("=" * 60)
    
    try:
        from train_tsisp import TORCH_GEOMETRIC_AVAILABLE, KNN_GRAPH_AVAILABLE, RADIUS_GRAPH_AVAILABLE
        
        print(f"TORCH_GEOMETRIC_AVAILABLE: {TORCH_GEOMETRIC_AVAILABLE}")
        print(f"KNN_GRAPH_AVAILABLE: {KNN_GRAPH_AVAILABLE}")
        print(f"RADIUS_GRAPH_AVAILABLE: {RADIUS_GRAPH_AVAILABLE}")
        
        if TORCH_GEOMETRIC_AVAILABLE:
            if KNN_GRAPH_AVAILABLE and RADIUS_GRAPH_AVAILABLE:
                print("✓ PyTorch Geometric图构建功能完全可用")
                return "full"
            else:
                print("⚠️ PyTorch Geometric已安装但图构建函数不可用")
                print("建议安装: pip install torch-cluster")
                return "partial"
        else:
            print("📦 PyTorch Geometric不可用，使用fallback实现")
            return "fallback"
            
    except Exception as e:
        print(f"状态检查失败: {e}")
        return "error"

def test_graph_construction():
    """测试图构建功能"""
    print("\n" + "=" * 60)
    print("测试图构建功能")
    print("=" * 60)
    
    try:
        from train_tsisp import SpatialGraphBuilder
        
        # 创建图构建器
        graph_builder = SpatialGraphBuilder(k_neighbors=3, radius=1.0)
        
        # 创建测试数据
        coords = torch.randn(10, 3)
        features = torch.randn(10, 64)
        
        print(f"测试数据: coords={coords.shape}, features={features.shape}")
        
        # 测试图构建
        edge_index, edge_attr = graph_builder.build_spatial_graph(coords, features)
        
        print(f"✓ 图构建成功")
        print(f"  edge_index: {edge_index.shape}")
        print(f"  edge_attr: {edge_attr.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ 图构建失败: {e}")
        traceback.print_exc()
        return False

def analyze_dimension_calculation():
    """分析维度计算"""
    print("\n" + "=" * 60)
    print("分析维度计算")
    print("=" * 60)
    
    try:
        from train_tsisp import Config
        
        # 显示当前配置
        print(f"当前配置:")
        print(f"  encoding_dim: {Config.encoding_dim}")
        print(f"  embedding_dim: {Config.embedding_dim}")
        print(f"  site_attributes: {Config.site_attributes}")
        print(f"  point_attributes: {Config.point_attributes}")
        print(f"  learnable_encoding: {Config.learnable_encoding}")
        
        # 计算各部分维度
        if Config.learnable_encoding:
            spatial_dim = Config.encoding_dim
        else:
            spatial_dim = 3 + 6 * Config.fourier_bands
        
        site_attr_dim = Config.embedding_dim + (len(Config.site_attributes) - 1)
        point_attr_dim = len(Config.point_attributes)
        total_feature_dim = spatial_dim + site_attr_dim + point_attr_dim
        
        print(f"\n理论维度计算:")
        print(f"  spatial_dim: {spatial_dim}")
        print(f"  site_attr_dim: {site_attr_dim} = {Config.embedding_dim} + {len(Config.site_attributes) - 1}")
        print(f"  point_attr_dim: {point_attr_dim}")
        print(f"  total_feature_dim: {total_feature_dim}")
        
        return {
            'spatial_dim': spatial_dim,
            'site_attr_dim': site_attr_dim,
            'point_attr_dim': point_attr_dim,
            'total_feature_dim': total_feature_dim
        }
        
    except Exception as e:
        print(f"维度分析失败: {e}")
        traceback.print_exc()
        return None

def test_feature_encoding():
    """测试特征编码"""
    print("\n" + "=" * 60)
    print("测试特征编码")
    print("=" * 60)
    
    try:
        from train_tsisp import Config, GeoSpatialGNN
        
        # 设置配置
        Config.architecture = 'geospatial_gnn'
        Config.learnable_encoding = True
        
        # 创建模型
        num_industries = 5
        model = GeoSpatialGNN(num_industries)
        
        print(f"✓ 模型创建成功")
        
        # 创建测试数据 - 确保维度正确
        batch_size = 16
        
        test_batch = {
            'local_coordinates': torch.randn(batch_size, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size, 1)).float(),  # industry
                torch.randn(batch_size, len(Config.site_attributes) - 1)    # other site attrs
            ], dim=1),
            'point_attributes': torch.randn(batch_size, len(Config.point_attributes))
        }
        
        print(f"测试数据形状:")
        for key, value in test_batch.items():
            print(f"  {key}: {value.shape}")
        
        # 逐步测试特征编码
        coords = test_batch['local_coordinates']
        site_attrs = test_batch['site_attributes']
        point_attrs = test_batch['point_attributes']
        
        print(f"\n逐步特征编码:")
        
        # 1. 空间编码
        spatial_enc = model.spatial_encoder(coords)
        print(f"  1. 空间编码: {coords.shape} -> {spatial_enc.shape}")
        
        # 2. 场地属性编码
        industry_ids = site_attrs[:, 0].long()
        industry_enc = model.industry_embedding(industry_ids)
        other_site_attrs = site_attrs[:, 1:]
        site_enc = torch.cat([industry_enc, other_site_attrs], dim=1)
        print(f"  2. 场地编码: industry({industry_enc.shape}) + other({other_site_attrs.shape}) = {site_enc.shape}")
        
        # 3. 点属性编码
        point_enc = point_attrs
        print(f"  3. 点属性编码: {point_enc.shape}")
        
        # 4. 特征拼接
        raw_features = torch.cat([spatial_enc, site_enc, point_enc], dim=1)
        print(f"  4. 特征拼接: {spatial_enc.shape[1]} + {site_enc.shape[1]} + {point_enc.shape[1]} = {raw_features.shape}")
        
        # 5. 检查与期望维度的匹配
        expected_dim = model.feature_fusion[0].in_features
        actual_dim = raw_features.shape[1]
        
        print(f"\n维度匹配检查:")
        print(f"  期望输入维度: {expected_dim}")
        print(f"  实际输入维度: {actual_dim}")
        print(f"  差异: {expected_dim - actual_dim}")
        
        if actual_dim == expected_dim:
            print("  ✓ 维度匹配正确")
            
            # 测试特征融合
            try:
                fused_features = model.feature_fusion(raw_features)
                print(f"  ✓ 特征融合成功: {raw_features.shape} -> {fused_features.shape}")
                return True
            except Exception as e:
                print(f"  ✗ 特征融合失败: {e}")
                return False
        else:
            print(f"  ✗ 维度不匹配")
            
            # 分析哪个组件的维度有问题
            print(f"\n详细分析:")
            print(f"  空间编码维度: {spatial_enc.shape[1]} (期望: {model.expected_spatial_dim})")
            print(f"  场地编码维度: {site_enc.shape[1]} (期望: {model.expected_site_attr_dim})")
            print(f"  点属性维度: {point_enc.shape[1]} (期望: {model.expected_point_attr_dim})")
            
            return False
        
    except Exception as e:
        print(f"特征编码测试失败: {e}")
        traceback.print_exc()
        return False

def test_problematic_batch_size():
    """测试问题批次大小（500）"""
    print("\n" + "=" * 60)
    print("测试问题批次大小（500）")
    print("=" * 60)
    
    try:
        from train_tsisp import Config, GeoSpatialGNN
        
        Config.architecture = 'geospatial_gnn'
        Config.learnable_encoding = True
        
        num_industries = 5
        model = GeoSpatialGNN(num_industries)
        
        # 测试原始错误中的批次大小
        batch_size = 500
        
        print(f"测试批次大小: {batch_size}")
        
        test_batch = {
            'local_coordinates': torch.randn(batch_size, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size, 1)).float(),
                torch.randn(batch_size, len(Config.site_attributes) - 1)
            ], dim=1),
            'point_attributes': torch.randn(batch_size, len(Config.point_attributes)),
            'concentrations': torch.randn(batch_size, 1)
        }
        
        all_points = {
            'local_coordinates': torch.randn(batch_size, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size, 1)).float(),
                torch.randn(batch_size, len(Config.site_attributes) - 1)
            ], dim=1),
            'point_attributes': torch.randn(batch_size, len(Config.point_attributes))
        }
        
        print(f"数据形状验证:")
        print(f"  coordinates: {test_batch['local_coordinates'].shape}")
        print(f"  site_attributes: {test_batch['site_attributes'].shape}")
        print(f"  point_attributes: {test_batch['point_attributes'].shape}")
        
        try:
            model.eval()
            with torch.no_grad():
                final_pred, _, _, _, _ = model(test_batch, all_points, training=False)
                print(f"✓ 批次大小 {batch_size} 测试成功: {final_pred.shape}")
                return True
        except RuntimeError as e:
            if "mat1 and mat2 shapes cannot be multiplied" in str(e):
                print(f"✗ 批次大小 {batch_size} 仍有维度错误: {e}")
                return False
            else:
                print(f"✗ 批次大小 {batch_size} 其他错误: {e}")
                return False
        except Exception as e:
            print(f"✗ 批次大小 {batch_size} 失败: {e}")
            return False
        
    except Exception as e:
        print(f"批次大小测试失败: {e}")
        return False

def main():
    """主函数"""
    print("TSISP综合问题调试工具")
    print("同时调试PyTorch Geometric依赖和张量维度不匹配问题\n")
    
    # 1. 测试PyTorch Geometric状态
    pg_status = test_pytorch_geometric_status()
    
    # 2. 测试图构建功能
    graph_ok = test_graph_construction()
    
    # 3. 分析维度计算
    dimension_info = analyze_dimension_calculation()
    
    # 4. 测试特征编码
    encoding_ok = test_feature_encoding()
    
    # 5. 测试问题批次大小
    batch_ok = False
    if encoding_ok:
        batch_ok = test_problematic_batch_size()
    
    # 6. 总结
    print("\n" + "=" * 60)
    print("调试结果总结")
    print("=" * 60)
    
    print(f"PyTorch Geometric状态: {pg_status}")
    if graph_ok:
        print("✓ 图构建功能正常")
    else:
        print("✗ 图构建功能有问题")
    
    if encoding_ok:
        print("✓ 特征编码正常")
    else:
        print("✗ 特征编码有问题")
    
    if batch_ok:
        print("✓ 问题批次大小（500）测试通过")
    else:
        print("✗ 问题批次大小（500）测试失败")
    
    success = graph_ok and encoding_ok and batch_ok
    
    if success:
        print("\n🎉 所有问题已解决！")
        print("现在可以正常运行TSISP模型")
    else:
        print("\n❌ 仍有问题需要解决")
        
        if pg_status == "partial":
            print("建议: pip install torch-cluster")
        if not encoding_ok:
            print("建议: 检查配置参数和维度计算")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
