"""
该模块包含：
1. 全局配置参数
2. 污染物定义和阈值
3. 风向配置
4. 模型超参数
5. 可视化配置
"""

import os
import numpy as np

# ======================================
# 环境变量设置
# ======================================
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'

# ======================================
# 全局配置类
# ======================================
class Config:
    """全局配置类"""
    
    # 基础参数
    RANDOM_STATE = 42
    N_JOBS = 1
    
    # 文件路径配置
    DATA_DIR = "data"
    OUTPUT_DIR = "results"
    
    # 数据文件名
    SOIL_DATA_FILE = "soil_samples.csv"
    SOURCE_DATA_FILE = "pollution_sources.csv"
    
    # 坐标系配置
    SOURCE_CRS = "EPSG:4326"  # WGS84
    TARGET_CRS = "EPSG:32649"  # UTM Zone 49N (适用于中国东部)
    
    # 空间分析参数
    BUFFER_DISTANCE_KM = 5.0
    SPATIAL_CV_FOLDS = 5
    
    # 模型训练参数
    TRAIN_TEST_SPLIT = 0.8
    VALIDATION_SPLIT = 0.2
    
    # 深度学习参数
    BATCH_SIZE = 32
    LEARNING_RATE = 0.001
    EPOCHS = 100
    EARLY_STOPPING_PATIENCE = 10
    
    # 优化参数
    OPTUNA_TRIALS = 100
    OPTUNA_TIMEOUT = 3600  # 1小时
    
    # 可视化参数
    FIGURE_DPI = 300
    FIGURE_FORMAT = 'png'
    FONT_SIZE = 12
    
    # 置信度阈值
    HIGH_CONFIDENCE_THRESHOLD = 3.5
    MEDIUM_CONFIDENCE_THRESHOLD = 1.5

# ======================================
# 污染物配置
# ======================================
# 重金属污染物列表
METALS = ['Cd', 'Pb', 'Zn', 'Cu', 'Cr', 'Ni', 'As']

# 污染物阈值配置 (mg/kg)
POLLUTION_THRESHOLDS = {
    'Cd': {
        'background': 0.2,
        'warning': 0.6,
        'pollution': 1.0,
        'severe': 3.0
    },
    'Pb': {
        'background': 25,
        'warning': 70,
        'pollution': 120,
        'severe': 400
    },
    'Zn': {
        'background': 100,
        'warning': 200,
        'pollution': 300,
        'severe': 500
    },
    'Cu': {
        'background': 35,
        'warning': 50,
        'pollution': 100,
        'severe': 200
    },
    'Cr': {
        'background': 90,
        'warning': 150,
        'pollution': 200,
        'severe': 300
    },
    'Ni': {
        'background': 40,
        'warning': 60,
        'pollution': 100,
        'severe': 200
    },
    'As': {
        'background': 15,
        'warning': 25,
        'pollution': 40,
        'severe': 80
    }
}

# ======================================
# 污染源类型配置
# ======================================
SOURCE_TYPES = ['atmosphere', 'irrigation', 'pesticide', 'manure']

# 单位转换因子（修正版 - 确保合理的贡献率分布）
UNIT_CONVERSION_FACTOR = {
    'atmosphere': 1.0,     # 大气沉降：基准单位
    'irrigation': 1.0,     # 灌溉水：基准单位
    'pesticide': 1.0,      # 农药：不再过度降低，保持合理权重
    'manure': 1.0          # 有机肥：不再过度降低，保持合理权重
}

# 污染源特征配置
SOURCE_CHARACTERISTICS = {
    'atmosphere': {
        'dominant_metals': ['Pb', 'Zn', 'Cd'],
        'typical_ratios': {'Pb/Zn': 0.3, 'Cd/Pb': 0.01},
        'description': '大气沉降源',
        'color': '#1f77b4'  # 蓝色
    },
    'irrigation': {
        'dominant_metals': ['Cd', 'As', 'Cr'],
        'typical_ratios': {'Cd/As': 0.05, 'Cr/As': 2.0},
        'description': '灌溉水源',
        'color': '#ff7f0e'  # 橙色
    },
    'pesticide': {
        'dominant_metals': ['Cu', 'As', 'Zn'],
        'typical_ratios': {'Cu/As': 1.5, 'Zn/Cu': 0.8},
        'description': '农药施用源',
        'color': '#2ca02c'  # 绿色
    },
    'manure': {
        'dominant_metals': ['Zn', 'Cu', 'Ni'],
        'typical_ratios': {'Zn/Cu': 2.0, 'Cu/Ni': 1.2},
        'description': '有机肥施用源',
        'color': '#d62728'  # 红色
    }
}

# ======================================
# 风向配置
# ======================================
WIND_CONFIG = {
    'dominant_directions': {
        'N': 0.05,
        'NE': 0.08,
        'E': 0.15,
        'SE': 0.45,
        'S': 0.25,
        'SW': 0.10,
        'W': 0.05,
        'NW': 0.02
    },
    'seasonal_variations': {
        'spring': {'SE': 0.50, 'S': 0.30, 'E': 0.20},
        'summer': {'SE': 0.40, 'S': 0.35, 'SW': 0.25},
        'autumn': {'SE': 0.45, 'S': 0.20, 'N': 0.35},
        'winter': {'SE': 0.50, 'N': 0.35, 'S': 0.15}
    },
    'average_speed': 3.2,  # m/s
    'dispersion_coefficient': 0.1
}

# ======================================
# 模型超参数配置
# ======================================
MODEL_CONFIGS = {
    'random_forest': {
        'n_estimators': 200,
        'max_depth': 10,
        'min_samples_split': 5,
        'min_samples_leaf': 2,
        'random_state': Config.RANDOM_STATE
    },
    'gradient_boosting': {
        'n_estimators': 150,
        'learning_rate': 0.1,
        'max_depth': 8,
        'random_state': Config.RANDOM_STATE
    },
    'gcn': {
        'hidden_channels': 64,
        'num_layers': 3,
        'dropout': 0.2,
        'learning_rate': 0.001
    },
    'nmf': {
        'n_components': 4,
        'init': 'nndsvd',
        'max_iter': 1000,
        'random_state': Config.RANDOM_STATE
    }
}

# ======================================
# 可视化配置
# ======================================
VISUALIZATION_CONFIG = {
    'academic_colors': {
        'blue': '#1f77b4',
        'orange': '#ff7f0e',
        'green': '#2ca02c',
        'red': '#d62728',
        'purple': '#9467bd',
        'brown': '#8c564b',
        'pink': '#e377c2',
        'gray': '#7f7f7f',
        'olive': '#bcbd22',
        'cyan': '#17becf'
    },
    'figure_params': {
        'dpi': Config.FIGURE_DPI,
        'format': Config.FIGURE_FORMAT,
        'bbox_inches': 'tight',
        'facecolor': 'white'
    },
    'font_params': {
        'size': Config.FONT_SIZE,
        'family': 'sans-serif',
        'weight': 'normal'
    }
}

# ======================================
# 特征工程配置
# ======================================
FEATURE_CONFIG = {
    'distance_radii_km': [1.0, 3.0, 5.0, 10.0],
    'density_radii_km': [1.0, 3.0, 5.0, 10.0],
    'k_nearest_neighbors': [3, 5, 10],
    'potential_decay_factor': 2.0,
    'interaction_threshold': 0.15
}

# ======================================
# 验证配置
# ======================================
VALIDATION_CONFIG = {
    'spatial_cv': {
        'n_splits': Config.SPATIAL_CV_FOLDS,
        'buffer_distance_km': Config.BUFFER_DISTANCE_KM,
        'method': 'spatial_clustering'
    },
    'metrics': ['r2', 'rmse', 'mae', 'mape'],
    'confidence_levels': [0.68, 0.95, 0.99]
}

# ======================================
# 导出配置
# ======================================
EXPORT_CONFIG = {
    'report_format': 'csv',
    'include_metadata': True,
    'precision': 4,
    'encoding': 'utf-8'
}

def get_config():
    """获取配置对象"""
    return Config()

def get_metal_thresholds(metal):
    """获取指定重金属的阈值"""
    return POLLUTION_THRESHOLDS.get(metal, {})

def get_source_characteristics(source_type):
    """获取指定污染源类型的特征"""
    return SOURCE_CHARACTERISTICS.get(source_type, {})

def get_academic_colors():
    """获取学术配色方案"""
    return VISUALIZATION_CONFIG['academic_colors']

def get_model_config(model_name):
    """获取指定模型的配置"""
    return MODEL_CONFIGS.get(model_name, {})
