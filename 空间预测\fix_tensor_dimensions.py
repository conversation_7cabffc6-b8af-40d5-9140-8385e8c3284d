#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
张量维度修复脚本

专门修复TSISP GeoSpatial-GNN架构中的张量维度不匹配问题
提供具体的修复方案和验证代码
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

def calculate_correct_dimensions():
    """计算正确的维度配置"""
    
    print("=" * 60)
    print("计算正确的维度配置")
    print("=" * 60)
    
    # 基础配置
    input_dim = 3  # x, y, depth
    num_scales = 3
    embedding_dim = 32
    
    # 计算空间编码维度
    # 目标：使得所有维度都能整除
    
    # 方案1：使用72维编码（72 = 3 * 2 * 3 * 4）
    encoding_dim_1 = 72
    freq_dim_1 = 4  # 每个尺度每个坐标轴的频率数
    multiscale_dim_1 = num_scales * input_dim * freq_dim_1 * 2  # 72
    geological_dim = 16
    total_spatial_1 = multiscale_dim_1 + geological_dim  # 88
    
    print(f"方案1 (encoding_dim=72):")
    print(f"  freq_dim: {freq_dim_1}")
    print(f"  multiscale_dim: {multiscale_dim_1}")
    print(f"  geological_dim: {geological_dim}")
    print(f"  total_spatial_dim: {total_spatial_1}")
    
    # 方案2：使用96维编码
    encoding_dim_2 = 96
    freq_dim_2 = 5  # 每个尺度每个坐标轴的频率数
    multiscale_dim_2 = num_scales * input_dim * freq_dim_2 * 2  # 90
    total_spatial_2 = multiscale_dim_2 + geological_dim  # 106
    
    print(f"\n方案2 (encoding_dim=96):")
    print(f"  freq_dim: {freq_dim_2}")
    print(f"  multiscale_dim: {multiscale_dim_2}")
    print(f"  geological_dim: {geological_dim}")
    print(f"  total_spatial_dim: {total_spatial_2}")
    
    # 计算特征融合层的输入维度
    site_attr_dim = embedding_dim + 2  # industry_embedding + 2 other attributes
    point_attr_dim = 2  # organic, depth
    
    total_feature_dim_1 = total_spatial_1 + site_attr_dim + point_attr_dim
    total_feature_dim_2 = total_spatial_2 + site_attr_dim + point_attr_dim
    
    print(f"\n特征融合层输入维度:")
    print(f"  site_attr_dim: {site_attr_dim}")
    print(f"  point_attr_dim: {point_attr_dim}")
    print(f"  方案1 total_feature_dim: {total_feature_dim_1}")
    print(f"  方案2 total_feature_dim: {total_feature_dim_2}")
    
    return {
        'plan1': {
            'encoding_dim': encoding_dim_1,
            'freq_dim': freq_dim_1,
            'total_feature_dim': total_feature_dim_1
        },
        'plan2': {
            'encoding_dim': encoding_dim_2,
            'freq_dim': freq_dim_2,
            'total_feature_dim': total_feature_dim_2
        }
    }

class FixedLearnableMultiScaleSpatialEncoder(nn.Module):
    """修复后的学习式多尺度空间编码器"""
    
    def __init__(self, input_dim=3, encoding_dim=72, num_scales=3):
        super().__init__()
        self.input_dim = input_dim
        self.encoding_dim = encoding_dim
        self.num_scales = num_scales
        
        # 计算频率维度
        geological_dim = 16
        available_dim = encoding_dim - geological_dim  # 为地质特征预留空间
        self.freq_dim = available_dim // (num_scales * input_dim * 2)
        
        # 实际的多尺度特征维度
        self.actual_multiscale_dim = num_scales * input_dim * self.freq_dim * 2
        
        print(f"FixedEncoder dimensions:")
        print(f"  encoding_dim: {encoding_dim}")
        print(f"  freq_dim: {self.freq_dim}")
        print(f"  actual_multiscale_dim: {self.actual_multiscale_dim}")
        print(f"  geological_dim: {geological_dim}")
        print(f"  fusion_input: {self.actual_multiscale_dim + geological_dim}")
        
        # 学习式频率参数
        self.frequency_weights = nn.Parameter(torch.randn(num_scales, input_dim, self.freq_dim))
        self.phase_shifts = nn.Parameter(torch.randn(num_scales, input_dim, self.freq_dim))
        
        # 各向异性变换矩阵
        self.anisotropic_transform = nn.Parameter(torch.eye(input_dim))
        
        # 地质先验嵌入网络
        self.geological_prior = nn.Sequential(
            nn.Linear(input_dim, 32),
            nn.ReLU(),
            nn.Linear(32, geological_dim),
            nn.Tanh()
        )
        
        # 多尺度融合网络
        fusion_input_dim = self.actual_multiscale_dim + geological_dim
        self.scale_fusion = nn.Sequential(
            nn.Linear(fusion_input_dim, encoding_dim),
            nn.LayerNorm(encoding_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
    
    def forward(self, xyz):
        batch_size = xyz.shape[0]
        
        # 各向异性变换
        xyz_transformed = torch.matmul(xyz, self.anisotropic_transform)
        
        # 多尺度编码
        scale_features = []
        for scale in range(self.num_scales):
            freq = self.frequency_weights[scale]  # [3, freq_dim]
            phase = self.phase_shifts[scale]      # [3, freq_dim]
            
            xyz_expanded = xyz_transformed.unsqueeze(-1)  # [B, 3, 1]
            freqs = xyz_expanded * freq.unsqueeze(0)      # [B, 3, freq_dim]
            phases = freqs + phase.unsqueeze(0)
            
            sin_features = torch.sin(phases)  # [B, 3, freq_dim]
            cos_features = torch.cos(phases)  # [B, 3, freq_dim]
            
            scale_feature = torch.cat([sin_features, cos_features], dim=-1)  # [B, 3, 2*freq_dim]
            scale_feature = scale_feature.flatten(start_dim=1)  # [B, 3 * 2 * freq_dim]
            scale_features.append(scale_feature)
        
        # 多尺度特征融合
        multi_scale_features = torch.cat(scale_features, dim=-1)  # [B, actual_multiscale_dim]
        
        # 地质先验特征
        geological_features = self.geological_prior(xyz)  # [B, 16]
        
        # 最终融合
        combined_features = torch.cat([multi_scale_features, geological_features], dim=-1)
        encoded = self.scale_fusion(combined_features)
        
        return encoded

def test_fixed_encoder():
    """测试修复后的编码器"""
    print("\n" + "=" * 60)
    print("测试修复后的编码器")
    print("=" * 60)
    
    # 测试不同的配置
    configs = [
        {'encoding_dim': 72, 'num_scales': 3},
        {'encoding_dim': 96, 'num_scales': 3},
        {'encoding_dim': 108, 'num_scales': 3},
    ]
    
    for config in configs:
        print(f"\n测试配置: {config}")
        
        try:
            encoder = FixedLearnableMultiScaleSpatialEncoder(**config)
            
            # 测试前向传播
            batch_sizes = [1, 16, 100, 500]
            for batch_size in batch_sizes:
                coords = torch.randn(batch_size, 3)
                encoded = encoder(coords)
                
                expected_shape = (batch_size, config['encoding_dim'])
                actual_shape = encoded.shape
                
                if actual_shape == expected_shape:
                    print(f"  ✓ batch_size={batch_size}: {actual_shape}")
                else:
                    print(f"  ✗ batch_size={batch_size}: 得到{actual_shape}, 期望{expected_shape}")
                    
        except Exception as e:
            print(f"  ✗ 配置失败: {e}")

def generate_config_fix():
    """生成配置修复代码"""
    print("\n" + "=" * 60)
    print("生成配置修复代码")
    print("=" * 60)
    
    fix_code = '''
# 在train_tsisp.py的Config类中，将以下行：
# encoding_dim = 64
# 修改为：
encoding_dim = 72  # 确保能被(num_scales * input_dim * 2)整除

# 或者使用更大的维度：
# encoding_dim = 96
# encoding_dim = 108

# 同时确保gnn_hidden_dim足够大以容纳所有特征：
gnn_hidden_dim = 128  # 或更大的值
'''
    
    print(fix_code)
    
    # 计算推荐的gnn_hidden_dim
    encoding_dim = 72
    embedding_dim = 32
    site_attr_dim = embedding_dim + 2  # industry + 2 other attrs
    point_attr_dim = 2  # organic, depth
    total_feature_dim = encoding_dim + site_attr_dim + point_attr_dim
    
    print(f"推荐的配置:")
    print(f"  encoding_dim = {encoding_dim}")
    print(f"  gnn_hidden_dim = {max(128, total_feature_dim)}  # 至少{total_feature_dim}")

def main():
    """主函数"""
    print("TSISP张量维度修复工具")
    print("解决RuntimeError: mat1 and mat2 shapes cannot be multiplied")
    
    # 1. 计算正确的维度
    dimensions = calculate_correct_dimensions()
    
    # 2. 测试修复后的编码器
    test_fixed_encoder()
    
    # 3. 生成修复代码
    generate_config_fix()
    
    print("\n" + "=" * 60)
    print("修复步骤总结")
    print("=" * 60)
    print("1. 将Config.encoding_dim改为72或96")
    print("2. 确保Config.gnn_hidden_dim >= 总特征维度")
    print("3. 运行debug_tensor_shapes.py验证修复")
    print("4. 重新训练模型")

if __name__ == "__main__":
    main()
