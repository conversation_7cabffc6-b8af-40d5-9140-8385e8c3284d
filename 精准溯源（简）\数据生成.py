import numpy as np
import pandas as pd
import os
from scipy.spatial.distance import cdist  # 添加这行导入


def generate_sample_data(num_samples=100, num_factories=8, num_pollutants=6, output_dir="simulated_data"):
    """
    生成模拟污染数据集并保存为CSV文件

    参数:
    num_samples: 采样点数量 (默认100)
    num_factories: 工厂数量 (默认8)
    num_pollutants: 污染物种类数量 (默认6)
    output_dir: 输出目录 (默认'simulated_data')
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 1. 生成工厂数据
    factories = pd.DataFrame({
        'factory_id': [f'F{i}' for i in range(num_factories)],
        'lon': np.random.uniform(120, 122, num_factories),
        'lat': np.random.uniform(30, 32, num_factories),
        'type': np.random.choice(['Chemical', 'Metal', 'Textile', 'Electronics'], num_factories)
    })

    # 添加污染物排放特征 (每个工厂有独特的"指纹")
    pollutants = [f'Pollutant_{i}' for i in range(num_pollutants)]
    for p in pollutants:
        base_value = np.abs(np.random.normal(0, 1, num_factories))
        factories[p] = np.abs(base_value + np.random.uniform(0.5, 2, num_factories))

    # 2. 生成采样点位置
    # 80%的采样点分布在工厂附近，20%随机分布
    n_near = int(num_samples * 0.8)

    # 创建空列表来存储这些点的经纬度
    lon_near = []
    lat_near = []

    # 确保每个工厂附近有大致相等数量的点
    points_per_factory = n_near // num_factories

    for factory_idx in range(num_factories):
        # 每个工厂生成points_per_factory个点
        factory_lon = factories.at[factory_idx, 'lon']
        factory_lat = factories.at[factory_idx, 'lat']

        # 生成该工厂附近的点
        lon_points = np.random.normal(factory_lon, 0.1, points_per_factory)
        lat_points = np.random.normal(factory_lat, 0.1, points_per_factory)

        lon_near.append(lon_points)
        lat_near.append(lat_points)

    # 将列表转换为数组
    lon_near = np.concatenate(lon_near)
    lat_near = np.concatenate(lat_near)

    # 生成剩余20%的随机分布点
    lon_random = np.random.uniform(120, 122, int(num_samples * 0.2))
    lat_random = np.random.uniform(30, 32, int(num_samples * 0.2))

    # 创建采样点DataFrame
    samples = pd.DataFrame({
        'sample_id': [f'S{i}' for i in range(num_samples)],
        'lon': np.concatenate([lon_near, lon_random]),
        'lat': np.concatenate([lat_near, lat_random])
    })

    # 3. 生成采样点污染物浓度
    # 初始化污染物浓度DataFrame
    sample_pollutants = pd.DataFrame(
        np.zeros((num_samples, num_pollutants)),
        columns=pollutants,
        dtype=np.float64
    )

    # 生成采样点污染物浓度
    for idx, sample in samples.iterrows():
        # 计算到所有工厂的距离
        distances = cdist(
            [[sample['lon'], sample['lat']]],
            factories[['lon', 'lat']].values
        )[0]

        # 获取最近的3个工厂
        closest_indices = np.argsort(distances)[:3]
        closest_factories = factories.iloc[closest_indices]

        # 计算权重（基于距离的倒数）
        weights = 1 / (distances[closest_indices] + 1e-6)  # 避免除以零
        weights /= weights.sum()  # 归一化权重

        # 计算污染物浓度
        for i, factory_idx in enumerate(closest_indices):
            factory = factories.iloc[factory_idx]
            distance_factor = np.exp(-distances[factory_idx] * 50)
            sample_pollutants.loc[idx] += weights[i] * factory[pollutants].values * distance_factor

    # 添加随机噪声和背景污染
    sample_pollutants += np.abs(np.random.normal(0, 0.1, sample_pollutants.shape))
    background = np.random.uniform(0.1, 0.3, num_pollutants)
    sample_pollutants += background

    # 合并到采样点数据
    samples = pd.concat([samples, sample_pollutants], axis=1)

    # 4. 保存为CSV文件
    factories_file = os.path.join(output_dir, "factories.csv")
    samples_file = os.path.join(output_dir, "samples.csv")

    factories.to_csv(factories_file, index=False)
    samples.to_csv(samples_file, index=False)

    print(f"模拟数据已生成并保存至: {output_dir}")
    print(f"- 工厂数据: {factories_file}")
    print(f"- 采样点数据: {samples_file}")
    print(f"包含 {num_factories} 家工厂, {num_samples} 个采样点, {num_pollutants} 种污染物")


# 生成模拟数据
if __name__ == "__main__":
    # 可以调整这些参数
    num_samples = 100
    num_factories = 8
    num_pollutants = 6
    output_dir = "simulated_data"

    generate_sample_data(num_samples, num_factories, num_pollutants, output_dir)