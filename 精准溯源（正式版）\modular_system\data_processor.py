"""
真实数据处理器 - 适配实际数据格式

该模块专门处理实际的土壤和污染源数据：
1. 土壤样本数据 (soil.csv)
2. 大气沉降源数据 (atmosphere.csv)
3. 灌溉水源数据 (irrigation.csv)
4. 农药源数据 (pesticide.csv)
5. 有机肥源数据 (manure.csv)
6. 工业源数据 (industry.csv)

"""

import os
import numpy as np
import pandas as pd
import warnings
from typing import Tuple, Dict, List, Optional
from pyproj import Proj, transform

warnings.filterwarnings('ignore')

# 导入配置
from config import UNIT_CONVERSION_FACTOR

# 实际数据中的重金属列表
REAL_METALS = ['Cu', 'Cr', 'Ni', 'Zn', 'Pb', 'Cd', 'As']

# 实际污染源类型
REAL_SOURCE_TYPES = ['atmosphere', 'irrigation', 'pesticide', 'manure', 'industry']

class RealDataProcessor:
    """真实数据处理器"""
    
    def __init__(self, data_dir: str = "data"):
        """
        初始化数据处理器
        
        Args:
            data_dir: 数据目录路径
        """
        self.data_dir = data_dir
        
        # 坐标转换器 (WGS84 -> UTM Zone 48N，适用于四川地区)
        self.proj_wgs84 = Proj(init='epsg:4326')
        self.proj_utm = Proj(init='epsg:32648')  # UTM Zone 48N
        
    def load_soil_data(self) -> pd.DataFrame:
        """
        加载土壤样本数据
        
        Returns:
            pd.DataFrame: 处理后的土壤样本数据
        """
        try:
            # 读取土壤数据
            soil_file = os.path.join(self.data_dir, 'soil.csv')
            soil_data = pd.read_csv(soil_file, encoding='utf-8')
            
            print(f"原始土壤数据: {soil_data.shape}")
            print(f"列名: {list(soil_data.columns)}")
            
            # 数据清理
            soil_data = self._clean_soil_data(soil_data)
            
            # 坐标转换
            soil_data = self._convert_coordinates(soil_data)
            
            print(f"处理后土壤数据: {soil_data.shape}")
            print(f"重金属浓度统计:")
            for metal in REAL_METALS:
                if metal in soil_data.columns:
                    values = soil_data[metal]
                    print(f"  {metal}: 均值={values.mean():.2f}, 范围=[{values.min():.2f}, {values.max():.2f}]")
            
            return soil_data
            
        except Exception as e:
            raise Exception(f"加载土壤数据失败: {str(e)}")
    
    def load_all_source_data(self) -> pd.DataFrame:
        """
        加载所有污染源数据并合并
        
        Returns:
            pd.DataFrame: 合并后的污染源数据
        """
        try:
            all_sources = []
            
            # 1. 加载大气沉降源
            atmosphere_data = self._load_source_file('atmosphere.csv', 'atmosphere')
            if not atmosphere_data.empty:
                all_sources.append(atmosphere_data)
                print(f"大气沉降源: {len(atmosphere_data)} 个样本")
            
            # 2. 加载灌溉水源
            irrigation_data = self._load_source_file('irrigation.csv', 'irrigation')
            if not irrigation_data.empty:
                all_sources.append(irrigation_data)
                print(f"灌溉水源: {len(irrigation_data)} 个样本")
            
            # 3. 加载农药源
            pesticide_data = self._load_source_file('pesticide.csv', 'pesticide')
            if not pesticide_data.empty:
                all_sources.append(pesticide_data)
                print(f"农药源: {len(pesticide_data)} 个样本")
            
            # 4. 加载有机肥源
            manure_data = self._load_source_file('manure.csv', 'manure')
            if not manure_data.empty:
                all_sources.append(manure_data)
                print(f"有机肥源: {len(manure_data)} 个样本")
            
            # 5. 加载工业源 (特殊处理)
            industry_data = self._load_industry_data()
            if not industry_data.empty:
                all_sources.append(industry_data)
                print(f"工业源: {len(industry_data)} 个样本")
            
            # 合并所有污染源数据
            if all_sources:
                combined_sources = pd.concat(all_sources, ignore_index=True)
                
                # 坐标转换
                combined_sources = self._convert_coordinates(combined_sources)
                
                print(f"合并后污染源总数: {len(combined_sources)}")
                print(f"污染源类型分布:")
                print(combined_sources['source_type'].value_counts())
                
                return combined_sources
            else:
                raise Exception("未找到任何污染源数据")
                
        except Exception as e:
            raise Exception(f"加载污染源数据失败: {str(e)}")
    
    def _clean_soil_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """清理土壤数据"""
        # 确保必要的列存在
        required_cols = ['sample', 'lon', 'lat'] + REAL_METALS
        missing_cols = [col for col in required_cols if col not in data.columns]
        if missing_cols:
            print(f"警告: 缺少列 {missing_cols}")
        
        # 数据类型转换
        data['sample'] = data['sample'].astype(str)
        data['lon'] = pd.to_numeric(data['lon'], errors='coerce')
        data['lat'] = pd.to_numeric(data['lat'], errors='coerce')
        
        # 重金属数据转换
        for metal in REAL_METALS:
            if metal in data.columns:
                data[metal] = pd.to_numeric(data[metal], errors='coerce')
        
        # 移除坐标缺失的样本
        before_count = len(data)
        data = data.dropna(subset=['lon', 'lat'])
        after_count = len(data)
        if before_count != after_count:
            print(f"移除了 {before_count - after_count} 个坐标缺失的样本")
        
        # 处理重金属浓度缺失值
        for metal in REAL_METALS:
            if metal in data.columns:
                missing_count = data[metal].isnull().sum()
                if missing_count > 0:
                    print(f"警告: {metal} 有 {missing_count} 个缺失值，将用均值填充")
                    data[metal] = data[metal].fillna(data[metal].mean())
        
        return data
    
    def _load_source_file(self, filename: str, source_type: str) -> pd.DataFrame:
        """加载单个污染源文件"""
        try:
            file_path = os.path.join(self.data_dir, filename)
            if not os.path.exists(file_path):
                print(f"警告: 文件 {filename} 不存在")
                return pd.DataFrame()
            
            data = pd.read_csv(file_path, encoding='utf-8')
            
            # 添加源类型列
            data['source_type'] = source_type
            
            # 数据清理
            data['sample'] = data['sample'].astype(str)
            data['lon'] = pd.to_numeric(data['lon'], errors='coerce')
            data['lat'] = pd.to_numeric(data['lat'], errors='coerce')
            
            # 重金属数据转换
            for metal in REAL_METALS:
                if metal in data.columns:
                    data[metal] = pd.to_numeric(data[metal], errors='coerce')
                    # 填充缺失值
                    if data[metal].isnull().sum() > 0:
                        data[metal] = data[metal].fillna(data[metal].mean())

            # 应用单位转换因子（基于2.5.py）
            if source_type in UNIT_CONVERSION_FACTOR:
                factor = UNIT_CONVERSION_FACTOR[source_type]
                for metal in REAL_METALS:
                    if metal in data.columns:
                        data[metal] = data[metal] * factor
                print(f"已对 {source_type} 应用单位转换因子: {factor}")

            # 移除坐标缺失的样本
            data = data.dropna(subset=['lon', 'lat'])
            
            return data
            
        except Exception as e:
            print(f"加载 {filename} 失败: {str(e)}")
            return pd.DataFrame()
    
    def _load_industry_data(self) -> pd.DataFrame:
        """特殊处理工业源数据 - 检查是否有重金属数据"""
        try:
            file_path = os.path.join(self.data_dir, 'industry.csv')
            if not os.path.exists(file_path):
                print("警告: industry.csv 文件不存在")
                return pd.DataFrame()

            # 尝试不同的编码
            for encoding in ['utf-8', 'gbk', 'gb2312']:
                try:
                    data = pd.read_csv(file_path, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue
            else:
                print("警告: 无法读取industry.csv文件，编码问题")
                return pd.DataFrame()

            print(f"工业源原始数据列: {list(data.columns)}")

            # 检查是否有重金属数据列
            has_metal_data = any(metal in data.columns for metal in REAL_METALS)

            if not has_metal_data:
                print("警告: industry.csv 缺少重金属污染数据，仅有坐标信息不足以进行污染源解析")
                print("跳过工业源数据，建议补充重金属浓度数据")
                return pd.DataFrame()

            # 检查是否有坐标列
            if 'lon' in data.columns and 'lat' in data.columns:
                # 创建标准格式的工业源数据
                valid_coords = pd.to_numeric(data['lon'], errors='coerce').notna() & \
                              pd.to_numeric(data['lat'], errors='coerce').notna()

                if valid_coords.sum() > 0:
                    valid_data = data[valid_coords].copy()

                    industry_data = pd.DataFrame({
                        'sample': [f'IND_{i:03d}' for i in range(len(valid_data))],
                        'lon': pd.to_numeric(valid_data['lon'], errors='coerce'),
                        'lat': pd.to_numeric(valid_data['lat'], errors='coerce'),
                        'source_type': 'industry'
                    })

                    # 添加实际的重金属数据
                    for metal in REAL_METALS:
                        if metal in valid_data.columns:
                            industry_data[metal] = pd.to_numeric(valid_data[metal], errors='coerce')
                            # 填充缺失值
                            if industry_data[metal].isnull().sum() > 0:
                                industry_data[metal] = industry_data[metal].fillna(industry_data[metal].mean())
                        else:
                            print(f"警告: 工业源数据缺少 {metal} 浓度数据")
                            return pd.DataFrame()

                    return industry_data
                else:
                    print("警告: industry.csv 中没有有效的坐标数据")
                    return pd.DataFrame()
            else:
                print("警告: industry.csv 缺少坐标信息")
                return pd.DataFrame()

        except Exception as e:
            print(f"加载工业源数据失败: {str(e)}")
            return pd.DataFrame()
    
    def _convert_coordinates(self, data: pd.DataFrame) -> pd.DataFrame:
        """坐标系转换：WGS84 -> UTM"""
        try:
            # WGS84转UTM
            utm_x, utm_y = transform(self.proj_wgs84, self.proj_utm, 
                                   data['lon'].values, data['lat'].values)
            
            data = data.copy()
            data['utm_x'] = utm_x
            data['utm_y'] = utm_y
            
            return data
            
        except Exception as e:
            print(f"坐标转换失败: {str(e)}")
            # 如果转换失败，使用原始坐标
            data['utm_x'] = data['lon'] * 111000  # 简单的度到米转换
            data['utm_y'] = data['lat'] * 111000
            return data
    
    def get_data_summary(self, soil_data: pd.DataFrame, 
                        source_data: pd.DataFrame) -> Dict[str, any]:
        """获取数据摘要"""
        summary = {
            'soil_samples': {
                'count': len(soil_data),
                'coordinate_range': {
                    'lon': [soil_data['lon'].min(), soil_data['lon'].max()],
                    'lat': [soil_data['lat'].min(), soil_data['lat'].max()]
                },
                'metal_statistics': {}
            },
            'pollution_sources': {
                'total_count': len(source_data),
                'type_distribution': source_data['source_type'].value_counts().to_dict(),
                'coordinate_range': {
                    'lon': [source_data['lon'].min(), source_data['lon'].max()],
                    'lat': [source_data['lat'].min(), source_data['lat'].max()]
                }
            }
        }
        
        # 重金属统计
        for metal in REAL_METALS:
            if metal in soil_data.columns:
                values = soil_data[metal]
                summary['soil_samples']['metal_statistics'][metal] = {
                    'mean': values.mean(),
                    'std': values.std(),
                    'min': values.min(),
                    'max': values.max(),
                    'median': values.median()
                }
        
        return summary
    
    def export_processed_data(self, soil_data: pd.DataFrame, 
                            source_data: pd.DataFrame, 
                            output_dir: str = 'processed_data') -> None:
        """导出处理后的数据"""
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            # 导出土壤数据
            soil_output = soil_data[['sample', 'lon', 'lat'] + REAL_METALS].copy()
            soil_output.to_csv(os.path.join(output_dir, 'processed_soil_samples.csv'), 
                              index=False, encoding='utf-8')
            
            # 导出污染源数据
            source_output = source_data[['sample', 'lon', 'lat', 'source_type'] + REAL_METALS].copy()
            source_output.to_csv(os.path.join(output_dir, 'processed_pollution_sources.csv'), 
                                index=False, encoding='utf-8')
            
            print(f"处理后的数据已导出到: {output_dir}")
            
        except Exception as e:
            raise Exception(f"数据导出失败: {str(e)}")

def main():
    """测试数据处理器"""
    try:
        # 创建数据处理器
        processor = RealDataProcessor()
        
        # 加载数据
        print("正在加载土壤数据...")
        soil_data = processor.load_soil_data()
        
        print("\n正在加载污染源数据...")
        source_data = processor.load_all_source_data()
        
        # 生成数据摘要
        print("\n生成数据摘要...")
        summary = processor.get_data_summary(soil_data, source_data)
        
        print(f"\n数据摘要:")
        print(f"土壤样本: {summary['soil_samples']['count']} 个")
        print(f"污染源: {summary['pollution_sources']['total_count']} 个")
        print(f"污染源类型分布: {summary['pollution_sources']['type_distribution']}")
        
        # 导出处理后的数据
        print("\n导出处理后的数据...")
        processor.export_processed_data(soil_data, source_data)
        
        print("\n数据处理完成！")
        
    except Exception as e:
        print(f"数据处理失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
