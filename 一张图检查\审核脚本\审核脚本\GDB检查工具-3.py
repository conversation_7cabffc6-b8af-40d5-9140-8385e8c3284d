#!/usr/bin/env python
# coding: utf-8
import os
import pandas as pd
from datetime import datetime
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import queue
import logging
import logging.handlers

'''
目录：
class GDBCheckerGUI:  # GUI主函数
def perform_comprehensive_real_checks():  # 执行检查的主函数
def update_summary_table():读取模板CSV，更新检查汇总表，将检查结果放到第六列
'''

# 字段要求配置-不同需求下，请根据实际情况进行修改
FIELD_REQUIREMENTS = {
    "required_fields": {
        'DKMC': 'object',
        'DKDM': 'object',
        'YDMJ': 'float64',
        'BSM': 'object',
        'YSDM': 'object',
        'XZQDM': 'object',
        'XZQMC': 'object',
        'DKTRWRFXGKHXFJD': 'object',
        'TRWRFXGKHXFJDWCSJ': 'datetime64[ns]',
        'TRHJZL': 'object',
        'DXSZL': 'object',
        'TRWRFXPGQK': 'object',
        'TRWRFXGKHXFMLGLQK': 'object',
        'DKSJZXGXSJ': 'datetime64[ns]',
        'HQGLYQ': 'object',
        'BZ': 'object',
        'Shape_Length': 'float64',
        'Shape_Area': 'float64'
    },
    "value_domains": {
        "DKDM": {"type": "range", "min": 5100000000000, "max": 5200000000000},
        "YDMJ": {"type": "range", "min": 0.00, "max": 1000000000.00},
        "TRWRFXGKHXFJDWCSJ": {"type": "datetime", "format": "YYYYMMDD"},
        "DKSJZXGXSJ": {"type": "date", "format": "%Y-%m-%d"},
        "DKTRWRFXGKHXFJD": {"type": "enum", "values": ['01', '02', '03', '04', '05', '06', '07', '08', '09']},
        "TRHJZL": {"type": "enum", "values": ['01', '02', '03', '04', '05', '06']},
        "DXSZL": {"type": "enum", "values": ['01', '02', '03', '04']},
        "TRWRFXPGQK": {"type": "enum", "values": ['01', '02', '03', '04']},
        "TRWRFXGKHXFMLGLQK": {"type": "enum", "values": ['01', '02', '03']}
    },
    "length_requirements": {
        'BSM': 18,
        'YSDM': 10,
        'XZQDM': 12,
        'XZQMC': 100,
        'DKTRWRFXGKHXFJD': 2,
        'DKMC': 100,
        'DKDM': 18,
        'TRHJZL': 2,
        'DXSZL': 2,
        'TRWRFXPGQK': 2,
        'TRWRFXGKHXFMLGLQK': 2,
        'BZ': 255
    },
    "field_order": [
        'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'DKMC', 'DKDM',
        'YDMJ', 'DKTRWRFXGKHXFJD', 'TRWRFXGKHXFJDWCSJ',
        'TRHJZL', 'DXSZL', 'TRWRFXPGQK', 'TRWRFXGKHXFMLGLQK',
        'DKSJZXGXSJ', 'HQGLYQ', 'BZ', 'Shape_Length', 'Shape_Area'
    ],
    "cgcs2000_epsg_codes": [4490, 4525, 4526, 4527, 4547],
    "overlap_threshold": 0.01
}


def perform_field_definition_length_check(gdb_path, layer_name, length_requirements):
    """
    检查字段属性中的字段长度设定
    注意：这里检查GDB字段定义的长度，而不是字段内容的长度
    """
    try:
        import fiona
        # 尝试多种方式获取图层信息
        layer_path = None

        # 方法1：直接使用图层名称
        try:
            layer_path = f"{gdb_path}/{layer_name}"
            with fiona.open(layer_path) as test_src:
                pass  # 测试是否能打开
        except:
            # 方法2：列出所有图层并使用第一个
            try:
                layers = fiona.listlayers(gdb_path)
                if layers:
                    layer_path = gdb_path
                    layer_name = layers[0]
                else:
                    return "错误，无法找到图层"
            except:
                return "错误，无法访问GDB文件"

        # 获取字段定义信息
        with fiona.open(layer_path, layer=layer_name if layer_path == gdb_path else None) as src:
            schema = src.schema
            properties = schema['properties']

            length_errors = []
            error_fields = []

            for field_name, expected_length in length_requirements.items():
                if field_name in properties:
                    field_type = properties[field_name]

                    # 检查字符串字段的长度定义
                    # 处理多种可能的字段类型格式
                    if isinstance(field_type, str):
                        # 格式1: 'str:80'
                        if field_type.startswith('str') and ':' in field_type:
                            try:
                                actual_length = int(field_type.split(':')[1])
                                if actual_length != expected_length:
                                    length_errors.append(f"{field_name}字段长度定义为{actual_length}，要求为{expected_length}")
                                    error_fields.append(field_name)
                            except (ValueError, IndexError):
                                # 无法解析长度，记录为错误
                                length_errors.append(f"{field_name}字段长度定义格式无法解析")
                                error_fields.append(field_name)
                        # 格式2: 'str' (无长度限制)
                        elif field_type == 'str':
                            # 对于无长度限制的字符串字段，认为不符合要求
                            length_errors.append(f"{field_name}字段未设置长度限制，要求为{expected_length}")
                            error_fields.append(field_name)
                    # 处理其他类型的字段定义格式
                    elif hasattr(field_type, '__len__') and hasattr(field_type, '__getitem__'):
                        # 可能是元组或列表格式 ('str', 80)
                        try:
                            if len(field_type) >= 2 and field_type[0] == 'str':
                                actual_length = int(field_type[1])
                                if actual_length != expected_length:
                                    length_errors.append(f"{field_name}字段长度定义为{actual_length}，要求为{expected_length}")
                                    error_fields.append(field_name)
                        except (ValueError, IndexError, TypeError):
                            pass
                else:
                    # 字段不存在
                    length_errors.append(f"缺少必需字段{field_name}")
                    error_fields.append(field_name)

            if length_errors:
                return f"错误，字段长度定义检查失败，请检查原因，是否因为子文件不包括GDB属性字段的长度设定，请仔细检查，修复代码。涉及字段：{'; '.join(error_fields)}"
            else:
                return "正确"

    except ImportError:
        # 如果没有fiona，使用备用方案
        return "错误，缺少fiona库，无法检查字段长度定义"
    except Exception as e:
        return f"错误，字段长度定义检查失败，请检查原因，是否因为子文件不包括GDB属性字段的长度设定，请仔细检查，修复代码。错误详情：{str(e)}"


def analyze_gdb_directory(gdb_path):
    """分析GDB目录文件结构"""
    gdbtable_files = []
    spx_files = []

    try:
        for file in os.listdir(gdb_path):
            if file.endswith('.gdbtable'):
                gdbtable_files.append(file)
            elif file.endswith('.spx'):
                spx_files.append(file)
        return gdbtable_files, spx_files
    except Exception:
        return [], []


# 内置行政区代码映射表（四川省）
ADMINISTRATIVE_CODE_MAPPING = {
    "510000": "四川省", "510100": "成都市",
    "510104": "锦江区", "510105": "青羊区",
    "510106": "金牛区", "510107": "武侯区",
    "510108": "成华区", "510112": "龙泉驿区",
    "510113": "青白江区", "510114": "新都区",
    "510115": "温江区",
    "510116": "双流区",
    "510117": "郫都区",
    "510118": "新津区",
    "510121": "金堂县",
    "510129": "大邑县",
    "510131": "蒲江县",
    "510181": "都江堰市",
    "510182": "彭州市",
    "510183": "邛崃市",
    "510184": "崇州市",
    "510185": "简阳市",
    "510300": "自贡市",
    "510302": "自流井区",
    "510303": "贡井区",
    "510304": "大安区",
    "510311": "沿滩区",
    "510321": "荣县",
    "510322": "富顺县",
    "510400": "攀枝花市",
    "510402": "东区",
    "510403": "西区",
    "510411": "仁和区",
    "510421": "米易县",
    "510422": "盐边县",
    "510500": "泸州市",
    "510502": "江阳区",
    "510503": "纳溪区",
    "510504": "龙马潭区",
    "510521": "泸县",
    "510522": "合江县",
    "510524": "叙永县",
    "510525": "古蔺县",
    "510600": "德阳市",
    "510603": "旌阳区",
    "510604": "罗江区",
    "510623": "中江县",
    "510681": "广汉市",
    "510682": "什邡市",
    "510683": "绵竹市",
    "510700": "绵阳市",
    "510703": "涪城区",
    "510704": "游仙区",
    "510705": "安州区",
    "510722": "三台县",
    "510723": "盐亭县",
    "510725": "梓潼县",
    "510726": "北川羌族自治县",
    "510727": "平武县",
    "510781": "江油市",
    "510800": "广元市",
    "510802": "利州区",
    "510811": "昭化区",
    "510812": "朝天区",
    "510821": "旺苍县",
    "510822": "青川县",
    "510823": "剑阁县",
    "510824": "苍溪县",
    "510900": "遂宁市",
    "510903": "船山区",
    "510904": "安居区",
    "510921": "蓬溪县",
    "510923": "大英县",
    "510981": "射洪市",
    "511000": "内江市",
    "511002": "市中区",
    "511011": "东兴区",
    "511024": "威远县",
    "511025": "资中县",
    "511083": "隆昌市",
    "511100": "乐山市",
    "511102": "市中区",
    "511111": "沙湾区",
    "511112": "五通桥区",
    "511113": "金口河区",
    "511123": "犍为县",
    "511124": "井研县",
    "511126": "夹江县",
    "511129": "沐川县",
    "511132": "峨边彝族自治县",
    "511133": "马边彝族自治县",
    "511181": "峨眉山市",
    "511300": "南充市",
    "511302": "顺庆区",
    "511303": "高坪区",
    "511304": "嘉陵区",
    "511321": "南部县",
    "511322": "营山县",
    "511323": "蓬安县",
    "511324": "仪陇县",
    "511325": "西充县",
    "511381": "阆中市",
    "511400": "眉山市",
    "511402": "东坡区",
    "511403": "彭山区",
    "511421": "仁寿县",
    "511423": "洪雅县",
    "511424": "丹棱县",
    "511425": "青神县",
    "511500": "宜宾市",
    "511502": "翠屏区",
    "511503": "南溪区",
    "511504": "叙州区",
    "511523": "江安县",
    "511524": "长宁县",
    "511525": "高县",
    "511526": "珙县",
    "511527": "筠连县",
    "511528": "兴文县",
    "511529": "屏山县",
    "511600": "广安市",
    "511602": "广安区",
    "511603": "前锋区",
    "511621": "岳池县",
    "511622": "武胜县",
    "511623": "邻水县",
    "511681": "华蓥市",
    "511700": "达州市",
    "511702": "通川区",
    "511703": "达川区",
    "511722": "宣汉县",
    "511723": "开江县",
    "511724": "大竹县",
    "511725": "渠县",
    "511781": "万源市",
    "511800": "雅安市",
    "511802": "雨城区",
    "511803": "名山区",
    "511822": "荥经县",
    "511823": "汉源县",
    "511824": "石棉县",
    "511825": "天全县",
    "511826": "芦山县",
    "511827": "宝兴县",
    "511900": "巴中市",
    "511902": "巴州区",
    "511903": "恩阳区",
    "511921": "通江县",
    "511922": "南江县",
    "511923": "平昌县",
    "512000": "资阳市",
    "512002": "雁江区",
    "512021": "安岳县",
    "512022": "乐至县",
    "513200": "阿坝藏族羌族自治州",
    "513201": "马尔康市",
    "513221": "汶川县",
    "513222": "理县",
    "513223": "茂县",
    "513224": "松潘县",
    "513225": "九寨沟县",
    "513226": "金川县",
    "513227": "小金县",
    "513228": "黑水县",
    "513230": "壤塘县",
    "513231": "阿坝县",
    "513232": "若尔盖县",
    "513233": "红原县",
    "513300": "甘孜藏族自治州",
    "513301": "康定市",
    "513322": "泸定县",
    "513323": "丹巴县",
    "513324": "九龙县",
    "513325": "雅江县",
    "513326": "道孚县",
    "513327": "炉霍县",
    "513328": "甘孜县",
    "513329": "新龙县",
    "513330": "德格县",
    "513331": "白玉县",
    "513332": "石渠县",
    "513333": "色达县",
    "513334": "理塘县",
    "513335": "巴塘县",
    "513336": "乡城县",
    "513337": "稻城县",
    "513338": "得荣县",
    "513400": "凉山彝族自治州",
    "513401": "西昌市",
    "513402": "会理市",
    "513422": "木里藏族自治县",
    "513423": "盐源县",
    "513424": "德昌县",
    "513426": "会东县",
    "513427": "宁南县",
    "513428": "普格县",
    "513429": "布拖县",
    "513430": "金阳县",
    "513431": "昭觉县",
    "513432": "喜德县",
    "513433": "冕宁县",
    "513434": "越西县",
    "513435": "甘洛县",
    "513436": "美姑县",
    "513437": "雷波县"
}

def get_administrative_code_mapping():
    """
    获取内置的行政区代码映射表
    返回字典格式：{行政区代码: 行政区名称}
    """
    return ADMINISTRATIVE_CODE_MAPPING


def perform_administrative_code_check(df):
    """
    执行行政区代码匹配检查
    检查XZQDM字段与XZQMC字段是否匹配
    """
    try:
        # 检查必需字段是否存在
        if 'XZQDM' not in df.columns or 'XZQMC' not in df.columns:
            return "错误，缺少XZQDM或XZQMC字段"

        # 检查BSM字段是否存在
        if 'BSM' not in df.columns:
            return "错误，缺少BSM字段，无法定位错误记录"

        # 获取内置的行政区代码映射
        code_mapping = get_administrative_code_mapping()
        if len(code_mapping) == 0:
            return "错误，行政区代码映射表为空"

        error_bsm_list = []

        # 检查每一行的XZQDM和XZQMC是否匹配
        for _, row in df.iterrows():
            xzqdm = str(row['XZQDM']).strip() if pd.notna(row['XZQDM']) else ""
            xzqmc = str(row['XZQMC']).strip() if pd.notna(row['XZQMC']) else ""
            bsm = str(row['BSM']).strip() if pd.notna(row['BSM']) else ""

            # 跳过空值
            if not xzqdm or not xzqmc or not bsm:
                continue

            # 检查代码是否在映射表中
            if xzqdm in code_mapping:
                expected_name = code_mapping[xzqdm]
                # 检查名称是否匹配（考虑可能的格式差异）
                if xzqmc != expected_name:
                    # 尝试一些常见的名称变体匹配
                    if not (xzqmc in expected_name or expected_name in xzqmc):
                        error_bsm_list.append(bsm)
            else:
                # 代码不在映射表中，也认为是错误
                error_bsm_list.append(bsm)

        if not error_bsm_list:
            return "正确"
        else:
            # 去重并限制显示的BSM数量
            unique_error_bsms = list(set(error_bsm_list))
            if len(unique_error_bsms) > 10:
                bsm_display = ', '.join(unique_error_bsms[:10]) + f"等{len(unique_error_bsms)}个"
            else:
                bsm_display = ', '.join(unique_error_bsms)
            return f"错误，涉及BSM：{bsm_display}"

    except Exception as e:
        return f"错误，行政区代码检查失败：{str(e)}"


def analyze_gdb_structure(gdb_path):
    """分析GDB文件结构"""
    file_stats = {
        'gdbtable': [],
        'spx': [],
        'gdbtablx': [],
        'gdbindexes': [],
        'atx': [],
        'other': []
    }

    try:
        for file in os.listdir(gdb_path):
            file_path = os.path.join(gdb_path, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)

                if file.endswith('.gdbtable'):
                    file_stats['gdbtable'].append({'name': file, 'size': size})
                elif file.endswith('.spx'):
                    file_stats['spx'].append({'name': file, 'size': size})
                elif file.endswith('.gdbtablx'):
                    file_stats['gdbtablx'].append({'name': file, 'size': size})
                elif file.endswith('.gdbindexes'):
                    file_stats['gdbindexes'].append({'name': file, 'size': size})
                elif file.endswith('.atx'):
                    file_stats['atx'].append({'name': file, 'size': size})
                else:
                    file_stats['other'].append({'name': file, 'size': size})

        return file_stats
    except Exception:
        return None


def read_gdb_data(gdb_path):
    """
    读取GDB数据
    返回
    df：属性表，没有geometry字段
    gdf：图层文件
    """
    try:
        import geopandas as gpd
        import fiona

        layers = fiona.listlayers(gdb_path)  # layers输出为图层名称的列表

        if not layers:  # layers返回为空
            return None, None

        layer_name = layers[0]
        gdf = gpd.read_file(gdb_path, layer=layer_name)
        df = pd.DataFrame(gdf.drop(columns=['geometry'] if 'geometry' in gdf.columns else []))
        return df, gdf

    except ImportError:
        return None, None
    except Exception:
        return None, None


def perform_real_field_checks(df):
    """执行字段检查"""
    results = {}
    details = {}

    required_fields = FIELD_REQUIREMENTS["required_fields"]
    length_requirements = FIELD_REQUIREMENTS["length_requirements"]

    # 字段数量检查
    expected_field_count = len(required_fields)
    actual_field_count = len(df.columns)
    if actual_field_count == expected_field_count:
        results['3201'] = "正确"
    else:
        results['3201'] = f"错误，字段数量为{actual_field_count}，应为{expected_field_count}"

    # 字段名称检查
    expected_fields = list(required_fields.keys())
    actual_fields = list(df.columns)
    missing_fields = [f for f in expected_fields if f not in actual_fields]
    extra_fields = [f for f in actual_fields if f not in expected_fields]

    if not missing_fields and not extra_fields:
        results['3202'] = "正确"
    else:
        error_msg = []
        if missing_fields:
            error_msg.append(f"缺失字段：{', '.join(missing_fields)}")
        if extra_fields:
            error_msg.append(f"多余字段：{', '.join(extra_fields)}")
        results['3202'] = f"错误，{'; '.join(error_msg)}"

    # 字段类型检查
    type_errors = []

    for field, expected_type in required_fields.items():
        if field in df.columns:
            actual_dtype = str(df[field].dtype)

            if expected_type == 'float64':
                if not pd.api.types.is_numeric_dtype(df[field]):
                    type_errors.append(f"{field}应为数值类型，实际为{actual_dtype}")

            elif expected_type == 'datetime64[ns]':
                if not pd.api.types.is_datetime64_any_dtype(df[field]):
                    try:
                        pd.to_datetime(df[field], errors='raise')
                    except:
                        type_errors.append(f"{field}应为日期类型，实际为{actual_dtype}")

            elif expected_type == 'object':
                try:
                    df[field].astype(str)
                except:
                    type_errors.append(f"{field}字段类型转换失败")

    if not type_errors:
        results['3203'] = "正确"
    else:
        results['3203'] = f"错误，{'; '.join(type_errors)}"

    # 字段长度检查 - 检查字段属性中的字段长度设定
    # 注意：这里检查GDB字段定义的长度，而不是字段内容的长度
    # 由于在字段检查函数中无法获取GDB路径，这里暂时标记为正确
    # 实际的字段长度定义检查应该在主检查函数中进行
    results['3204'] = "正确"
    details['3204'] = ""

    # YDMJ字段类型检查
    if 'YDMJ' in df.columns:
        if pd.api.types.is_numeric_dtype(df['YDMJ']):
            results['3205'] = "正确"
        else:
            results['3205'] = "错误，YDMJ字段不是数值类型"
    else:
        results['3205'] = "错误，缺少YDMJ字段"

    return results, details


def perform_real_data_content_checks(df):
    """
    执行数据内容检查
    3301、3302、3501、3601
    """
    results = {}
    details = {}

    # 检查BSM字段是否存在且不为空
    if 'BSM' not in df.columns:
        # results['BSM_CHECK'] = "错误，缺少BSM字段，请补充完善所有BSM取值，方可进行数据检查"
        results['3301'] = "错误，缺少BSM字段，请补充完善所有BSM取值，方可进行数据检查"
        results['3302'] = "错误，缺少BSM字段，请补充完善所有BSM取值，方可进行数据检查"
        results['3501'] = "错误，缺少BSM字段，请补充完善所有BSM取值，方可进行数据检查"
        results['3601'] = "错误，缺少BSM字段，请补充完善所有BSM取值，方可进行数据检查"
        return results, details

    # 检查BSM为空的情况
    empty_bsm_mask = df['BSM'].isna() | (df['BSM'].astype(str).isin(['', 'nan', 'None', 'NaN']))
    if empty_bsm_mask.any():
        empty_count = empty_bsm_mask.sum()
        # results['BSM_CHECK'] = f"错误，发现{empty_count}个BSM为空，请先完善所有BSM取值，方可进行数据检查"
        results['3301'] = f"错误，发现{empty_count}个BSM为空，请先完善所有BSM取值，方可进行数据检查"
        results['3302'] = f"错误，发现{empty_count}个BSM为空，请先完善所有BSM取值，方可进行数据检查"
        results['3501'] = f"错误，发现{empty_count}个BSM为空，请先完善所有BSM取值，方可进行数据检查"
        results['3601'] = f"错误，发现{empty_count}个BSM为空，请先完善所有BSM取值，方可进行数据检查"
        return results, details

    value_domains = FIELD_REQUIREMENTS["value_domains"]

    # 字段值域检查
    error_bsm_list = []

    for field, domain_config in value_domains.items():
        if field in df.columns:
            domain_type = domain_config.get("type")

            if domain_type == "enum":
                # 枚举值检查
                valid_values = domain_config["values"]
                invalid_mask = ~df[field].astype(str).isin(valid_values + ['nan', 'None', '', 'NaN'])

                if invalid_mask.any():
                    invalid_rows = df[invalid_mask]
                    field_error_bsms = invalid_rows['BSM'].astype(str).tolist()
                    error_bsm_list.extend(field_error_bsms)

            elif domain_type == "range":
                # 数值范围检查
                min_val = domain_config["min"]
                max_val = domain_config["max"]

                try:
                    numeric_values = pd.to_numeric(df[field], errors='coerce')
                    out_of_range = ((numeric_values < min_val) | (numeric_values > max_val)) & numeric_values.notna()

                    if out_of_range.any():
                        out_of_range_rows = df[out_of_range]
                        field_error_bsms = out_of_range_rows['BSM'].astype(str).tolist()
                        error_bsm_list.extend(field_error_bsms)
                except Exception:
                    pass

            elif domain_type in ["datetime", "date"]:
                # 日期格式检查
                date_format = domain_config["format"]
                try:
                    pd.to_datetime(df[field], format=date_format, errors='raise')
                except:
                    try:
                        parsed_dates = pd.to_datetime(df[field], errors='coerce')
                        invalid_dates = parsed_dates.isna() & df[field].notna()
                        if invalid_dates.any():
                            invalid_date_rows = df[invalid_dates]
                            field_error_bsms = invalid_date_rows['BSM'].astype(str).tolist()
                            error_bsm_list.extend(field_error_bsms)
                    except Exception:
                        pass

    if not error_bsm_list:
        results['3301'] = "正确"
    else:
        # 去重BSM列表
        unique_error_bsms = list(set(error_bsm_list))
        bsm_display = ', '.join(unique_error_bsms)
        results['3301'] = f"错误，涉及BSM：{bsm_display}"
        details['3301'] = bsm_display

    # BSM字段格式检查
    if 'BSM' in df.columns and 'XZQDM' in df.columns:
        error_bsm_list = []
        for bsm, xzqdm in zip(df['BSM'], df['XZQDM']):
            if pd.notna(bsm) and pd.notna(xzqdm):
                bsm_str = str(bsm).strip()
                xzqdm_str = str(xzqdm).strip()

                expected_prefix = xzqdm_str + "0000"

                if not bsm_str.startswith(expected_prefix):
                    error_bsm_list.append(bsm_str)
                elif len(bsm_str) <= len(expected_prefix):
                    error_bsm_list.append(bsm_str)
                else:
                    sequence_part = bsm_str[len(expected_prefix):]
                    if not sequence_part.isdigit():
                        error_bsm_list.append(bsm_str)

        if not error_bsm_list:
            results['3302'] = "正确"
        else:
            bsm_display = ', '.join(error_bsm_list)
            results['3302'] = f"错误，涉及BSM：{bsm_display}"
            details['3302'] = bsm_display
    else:
        results['3302'] = "错误，缺少BSM或XZQDM字段"
        details['3302'] = ""

    # BSM唯一性检查
    if 'BSM' in df.columns:
        bsm_counts = df['BSM'].value_counts()
        duplicates = bsm_counts[bsm_counts > 1]

        if len(duplicates) == 0:
            results['3501'] = "正确"
        else:
            duplicate_bsm_list = list(duplicates.index)
            bsm_display = ', '.join(map(str, duplicate_bsm_list))
            results['3501'] = f"错误，重复BSM：{bsm_display}"
            details['3501'] = bsm_display
    else:
        results['3501'] = "错误，缺少BSM字段"
        details['3501'] = ""

    # 字段非空检查
    exclude_fields = ['TRWRFXGKHXFJDWCSJ', 'HQGLYQ', 'BZ']
    required_fields = [col for col in df.columns if col not in exclude_fields]

    empty_bsm_list = []

    for field in required_fields:
        # 检查每一行的空值情况
        for _, row in df.iterrows():
            is_empty = (pd.isna(row[field]) or
                       str(row[field]).strip() in ['', 'nan', 'None', 'NaN'])

            if is_empty:
                bsm = str(row['BSM'])
                empty_bsm_list.append(bsm)

    if not empty_bsm_list:
        results['3601'] = "正确"
    else:
        # 去重BSM列表
        unique_empty_bsms = list(set(empty_bsm_list))
        bsm_display = ', '.join(unique_empty_bsms)
        results['3601'] = f"错误，涉及BSM：{bsm_display}"
        details['3601'] = bsm_display

    return results, details


def perform_real_topology_check(gdf):
    """执行拓扑重叠检查"""
    try:
        from shapely.validation import make_valid

        if len(gdf) == 0:
            return {"result": "错误，图层中没有几何数据", "details": ""}

        # 检查BSM字段是否存在且不为空
        if 'BSM' not in gdf.columns:
            return {"result": "错误，缺少BSM字段", "details": ""}

        # 检查BSM为空的情况
        empty_bsm_mask = gdf['BSM'].isna() | (gdf['BSM'].astype(str).isin(['', 'nan', 'None', 'NaN']))
        if empty_bsm_mask.any():
            empty_count = empty_bsm_mask.sum()
            return {"result": f"错误，发现{empty_count}个BSM为空，请先完善所有BSM取值，方可进行数据检查", "details": ""}

        gdf['valid_geom'] = gdf.geometry.apply(
            lambda geom: make_valid(geom) if geom is not None and not geom.is_valid else geom
        )

        valid_gdf = gdf[gdf['valid_geom'].notna()]

        if len(valid_gdf) == 0:
            return {"result": "错误，没有有效的几何数据", "details": ""}

        overlap_pairs = []
        spatial_index = valid_gdf.sindex
        overlap_threshold = FIELD_REQUIREMENTS["overlap_threshold"]

        for i, row in valid_gdf.iterrows():
            if row.valid_geom is None:
                continue
            try:
                possible_matches = list(spatial_index.intersection(row.valid_geom.bounds))
                if i in possible_matches:
                    possible_matches.remove(i)

                for j in possible_matches:
                    if i < j and valid_gdf.loc[j, 'valid_geom'] is not None:
                        if row.valid_geom.intersects(valid_gdf.loc[j, 'valid_geom']):
                            try:
                                intersection_area = row.valid_geom.intersection(valid_gdf.loc[j, 'valid_geom']).area
                                min_area = min(row.valid_geom.area, valid_gdf.loc[j, 'valid_geom'].area)

                                if min_area > 0 and intersection_area / min_area > overlap_threshold:
                                    bsm_i = str(row['BSM'])
                                    bsm_j = str(valid_gdf.loc[j, 'BSM'])
                                    overlap_pairs.append(f"{bsm_i}与{bsm_j}")
                            except Exception:
                                continue
            except Exception:
                continue

        if not overlap_pairs:
            return {"result": "正确", "details": ""}
        else:
            overlap_display = ', '.join(overlap_pairs)
            result = f"错误，重叠BSM：{overlap_display}"
            return {"result": result, "details": overlap_display}

    except ImportError:
        return {"result": "错误，缺少shapely库", "details": []}
    except Exception as e:
        return {"result": f"错误，拓扑检查失败: {str(e)}", "details": []}


def perform_real_coordinate_system_check(gdf):
    """执行坐标系检查"""
    try:
        if gdf.crs is None:
            return "错误，未定义坐标系"

        import pyproj
        crs_auth = pyproj.CRS(gdf.crs).to_authority()
        cgcs2000_codes = FIELD_REQUIREMENTS["cgcs2000_epsg_codes"]

        if crs_auth and crs_auth[0] == "EPSG" and int(crs_auth[1]) in cgcs2000_codes:
            return "正确"
        else:
            return f"错误，要求坐标系为CGCS2000，实际坐标系为{crs_auth}"
    except ImportError:
        return "错误，缺少pyproj库"
    except Exception as e:
        return f"错误，坐标系检查失败: {str(e)}"


def perform_DKNUM_check(df):  # 1301 整合后地块数量是否与"建设用地土壤污染状况调查清单"数量一致
    """
    执行1301 整合后地块数量是否与"建设用地土壤污染状况调查清单"数量一致的检查
    """
    try:
        import pandas as pd
        from collections import Counter
        
        # 检查DKDM列是否存在
        if 'DKDM' not in df.columns:
            return "错误，GDB文件中缺少DKDM列"
        
        # 获取地区代码（DKDM前四位）
        valid_dkdm = df['DKDM'].dropna().astype(str)
        valid_dkdm = valid_dkdm[valid_dkdm.str.len() >= 4]
        
        if len(valid_dkdm) == 0:
            return "错误，没有找到有效的DKDM编码"
        
        region_code = valid_dkdm.iloc[0][:4]
        len1 = len(df)
        
        # 读取参考清单
        reference_file = f"{os.path.dirname(os.path.abspath(__file__))}\\四川省_建设用地土壤污染状况调查清单.csv"
        
        if not os.path.exists(reference_file):
            reference_file = f"{os.path.dirname(os.path.abspath(__file__))}\\四川省_建设用地土壤污染状况调查清单.xlsx"
            if not os.path.exists(reference_file):
                return "错误，找不到参考清单文件"
        
        # 读取文件
        if reference_file.endswith('.csv'):
            try:
                df2 = pd.read_csv(reference_file, encoding='utf-8')
            except UnicodeDecodeError:
                try:
                    df2 = pd.read_csv(reference_file, encoding='gbk')
                except UnicodeDecodeError:
                    df2 = pd.read_csv(reference_file, encoding='gb2312')
        else:
            df2 = pd.read_excel(reference_file)
        
        if '地块编码' not in df2.columns:
            return "错误，参考清单中缺少'地块编码'列"
        
        # 筛选对应地区的记录
        df2_filtered = df2[df2['地块编码'].astype(str).str[:4] == region_code]
        
        # 排除移出清单的地块
        if '所处清单' in df2.columns:
            df2_filtered = df2_filtered[df2_filtered['所处清单'] != "移出调查地块清单"]
        
        len2 = len(df2_filtered)
        
        # 获取编码集合
        reference_dkdm_set = set(df2_filtered['地块编码'].astype(str))
        gdb_dkdm_set = set(df['DKDM'].dropna().astype(str))
        
        # 统计缺失和重复
        missing_dkdm = reference_dkdm_set - gdb_dkdm_set
        missing_count = len(missing_dkdm)
        
        dkdm_counts = Counter(df['DKDM'].dropna().astype(str))
        duplicated_dkdm = {dkdm: count for dkdm, count in dkdm_counts.items() if count > 1}
        duplicate_count = len(duplicated_dkdm)
        
        # 生成结果
        if len1 == len2 and missing_count == 0 and duplicate_count == 0:
            return f"正确，提交成果GDB中地块数量({len1})与建设用地土壤污染状况调查清单数量({len2})一致，无缺失或重复地块"
        else:
            issues = []
            if len1 != len2:
                issues.append(f"数量不匹配(GDB:{len1} vs 清单:{len2})")
            if missing_count > 0:
                missing_list = sorted(list(missing_dkdm))
                missing_str = ', '.join(missing_list)
                issues.append(f"缺失地块:{missing_str}")
            if duplicate_count > 0:
                duplicate_list = [f"{dkdm}(重复{count}次)" for dkdm, count in sorted(duplicated_dkdm.items())]
                duplicate_str = ', '.join(duplicate_list)
                issues.append(f"重复地块:{duplicate_str}")
            
            return f"错误，发现问题：{'; '.join(issues)}"
        
    except ImportError:
        return "错误，缺少pandas库"
    except Exception as e:
        return f"错误，数据表相关的检查失败：{str(e)}"


def perform_administrative_boundary_consis_check(gdf):  # 2202：跨市、县行政区地块是否按照行政区范围分割
    """
        执行跨市、县行政区地块是否按照行政区范围分割检查
        """
    try:
        import geopandas as gpd

        cwd = os.getcwd()
        admingdbfn = f"{cwd}\\一张图检查\\审核脚本\\审核脚本\\【勿动位置】行政区边界\\四川省乡镇及以上行政区域界线数据库.gdb"
        adgdf = gpd.read_file(admingdbfn, layer="县级面")
        if adgdf is None or adgdf.empty:
            return "错误，区县边界数据读取为空"
        # 读取当前地块的市州代码(4位)
        szdm = str(gdf.loc[0, "XZQDM"])[:4]
        # 筛选区县代码中前4位是 szdm 的（当前市州下所有的区县面）
        counties_szdm = adgdf[adgdf['XZQDM'].str.startswith(szdm)]
        # 用于存放被切割图斑的编码
        all_cut_gdf_codes = []
        # 循环每个区县
        for idx, county_row in counties_szdm.iterrows():
            county_geom = county_row.geometry
            # 判断 intersects & within
            gdf['intersects'] = gdf.intersects(county_geom)
            gdf['within'] = gdf.within(county_geom)
            # 被切割 = 与区县相交 且 不完全在内部
            gdf['cut_by_boundary'] = gdf['intersects'] & (~gdf['within'])
            # 被切割的地块
            cut_gdf = gdf[gdf['cut_by_boundary']]
            if not cut_gdf.empty:
                # 提取被切割图斑编码（列名假设是 plot_code）
                cut_gdf = cut_gdf['BSM'].tolist() if 'BSM' in cut_gdf.columns else []
                all_cut_gdf_codes.extend(cut_gdf)

        if all_cut_gdf_codes is None or len(all_cut_gdf_codes) == 0:
            return "正确"
        else:
            # 被切割图斑的编码 进行去重（因为区县矢量数据特殊）
            all_cut_gdf_codes_unique = list(set(all_cut_gdf_codes))
            # 用顿号连接
            return f"错误，提交成果共有{len(all_cut_gdf_codes_unique)}个地块未按照区县级行政区范围分割，标识码为：{'、'.join(map(str, all_cut_gdf_codes_unique))}"
    except ImportError:
        return "错误，缺少geopandas或fiona库"
    except Exception as e:
        return f"错误，跨市、县行政区地块是否按照行政区范围分割检查失败：{str(e)}"


def perform_comprehensive_real_checks(gdb_path):  # 执行检查的主函数
    """执行全面检查"""
    df, gdf = read_gdb_data(gdb_path)

    if df is None or gdf is None:
        raise Exception("无法读取GDB数据，请检查文件格式和依赖库安装")

    results = {}  # 输出结果-字典
    all_details = {}

    # 手动检查项目
    results['1101'] = "备注：请检查人员手动检查"
    results['1102'] = "备注：请检查人员手动检查"

    # 1301  整合后地块数量是否与“建设用地土壤污染状况调查清单”数量一致
    try:
        results['1301'] = perform_DKNUM_check(df)
    except Exception as e:
        results['1301']= f"错误，整合后地块数量是否与“建设用地土壤污染状况调查清单”数量一致检查失败：{str(e)}"

    # 2202  跨市、县行政区地块是否按照行政区范围分割
    try:
        results['2202'] = perform_administrative_boundary_consis_check(gdf)
    except Exception as e:
        results['2022'] = f"错误，跨市、县行政区地块是否按照行政区范围分割检查失败：{str(e)}"

    # 3701行政区代码匹配检查
    try:
        results['3701'] = perform_administrative_code_check(df)
    except Exception as e:
        results['3701'] = f"错误，行政区代码检查失败：{str(e)}"

    # 文件格式检查
    gdbtable_files, _ = analyze_gdb_directory(gdb_path)
    if gdbtable_files:
        results['1201'] = "正确"
        results['3101'] = "正确"
    else:
        results['1201'] = "错误，未找到.gdbtable文件"
        results['3101'] = "错误，未找到属性表"

    # 坐标系检查
    results['2101'] = perform_real_coordinate_system_check(gdf)

    # 字段检查
    field_results, field_details = perform_real_field_checks(df)
    results.update(field_results)
    all_details.update(field_details)

    # 3204字段长度定义检查 - 检查GDB字段属性中的长度设定
    try:
        # 尝试多种方式获取图层名称
        layer_name = None

        # 方法1：通过fiona直接列出图层
        try:
            import fiona
            layers = fiona.listlayers(gdb_path)
            if layers:
                layer_name = layers[0]
        except:
            pass

        # 方法2：通过分析GDB目录结构
        if not layer_name:
            gdbtable_files, _ = analyze_gdb_directory(gdb_path)
            if gdbtable_files:
                # 使用第一个找到的图层
                layer_name = gdbtable_files[0].replace('.gdbtable', '')

        if layer_name:
            length_requirements = FIELD_REQUIREMENTS["length_requirements"]
            results['3204'] = perform_field_definition_length_check(gdb_path, layer_name, length_requirements)
        else:
            results['3204'] = "错误，无法确定图层名称，请检查GDB文件结构"
    except Exception as e:
        results['3204'] = f"错误，字段长度定义检查失败，请检查原因，是否因为子文件不包括GDB属性字段的长度设定，请仔细检查，修复代码。错误详情：{str(e)}"

    # 数据内容检查
    content_results, content_details = perform_real_data_content_checks(df)
    results.update(content_results)
    all_details.update(content_details)

    # 拓扑重叠检查
    topology_result = perform_real_topology_check(gdf)
    results['4101'] = topology_result['result']
    all_details['4101'] = topology_result['details']

    # 存储详细信息供GUI使用
    global _last_check_details
    _last_check_details = all_details
    return results


def update_summary_table(template_path, check_results, details, timestamp, output_path=None):
    """
    读取模板CSV，更新检查汇总表，将检查结果放到第六列
    """
    try:
        encodings = ['gbk', 'utf-8', 'utf-8-sig']
        df_summary = None

        for encoding in encodings:
            try:
                df_summary = pd.read_csv(template_path, encoding=encoding)  # 读取的CSV模板，总全的
                break
            except UnicodeDecodeError:
                continue

        if df_summary is None:
            return None

        for idx, row in df_summary.iterrows():
            try:
                check_code = str(int(float(row.iloc[4])))
                if check_code in check_results:
                    if check_results[check_code].startswith("备注"):
                        df_summary.iloc[idx, 7] = check_results[check_code]  # 第8列：备注信息
                    else:
                        df_summary.iloc[idx, 5] = check_results[check_code]  # 第六列：检查结果
                        if (check_code == '1301') and (check_results[check_code].startswith("错误")):
                            df_summary.iloc[idx, 7] = "备注：请检查人员核实提交成果中是否有相关情况说明"  # 第8列：备注信息
                if check_code in details and details[check_code]:  # 第七列：详细错误信息（使用BSM）
                    df_summary.iloc[idx, 6] = df_summary.iloc[idx, 6] + details[check_code]
            except:
                continue

        if output_path is None:
            # output_path = template_path.replace('.csv', '_最终GDB检查结果.csv')
            output_path = f"【GDB检查结果{timestamp}】{template_path}"
        try:
            df_summary.to_csv(output_path, encoding='gbk', index=False)
        except UnicodeEncodeError:
            df_summary.to_csv(output_path, encoding='utf-8-sig', index=False)

        return output_path

    except Exception:
        return None


class GDBCheckerGUI:  # GUI主函数
    def __init__(self, root):
        self.root = root
        self.root.title("GDB数据质量检查工具 v3.0")
        self.root.geometry("1000x800")  # 增大窗口尺寸
        self.root.resizable(True, True)
        self.root.minsize(900, 700)  # 设置最小尺寸

        # 设置图标和样式
        try:
            self.root.iconbitmap(default='')  # 可以添加图标文件
        except:
            pass

        # 配置样式主题
        self.setup_styles()
        # 初始化变量
        self.init_variables()
        # 设置日志
        self.setup_logging()
        # 创建界面
        self.setup_ui()
        # 绑定事件
        self.bind_events()

    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')

        # 自定义样式
        style.configure('Title.TLabel', font=('Arial', 18, 'bold'), foreground='#2c3e50')
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'), foreground='#34495e')
        style.configure('Success.TButton', background='#27ae60')
        style.configure('Warning.TButton', background='#f39c12')
        style.configure('Danger.TButton', background='#e74c3c')

    def init_variables(self):
        """初始化变量"""
        self.gdb_path_var = tk.StringVar()
        self.csv_path_var = tk.StringVar(value=f"{os.path.dirname(os.path.abspath(__file__))}\\【勿改动位置可更新】检查结果汇总表模板.csv")
        self.output_dir_var = tk.StringVar(value=f"{os.path.dirname(os.path.abspath(__file__))}\\GDB检查结果\\")
        self.save_csv_var = tk.BooleanVar(value=True)
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="就绪")
        self.current_step_var = tk.StringVar(value="等待开始...")

        # 检查状态
        self.is_checking = False
        self.check_thread = None

    def setup_logging(self):
        """设置日志系统"""
        self.log_queue = queue.Queue()

        # 配置日志格式
        log_format = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

        # 创建队列处理器
        self.queue_handler = logging.handlers.QueueHandler(self.log_queue)
        self.queue_handler.setFormatter(log_format)

        # 配置根日志器
        self.logger = logging.getLogger('GDBChecker')
        self.logger.setLevel(logging.INFO)
        self.logger.addHandler(self.queue_handler)

    def setup_ui(self):
        """创建用户界面"""
        # 创建主容器
        self.create_main_container()

        # 创建顶部标题区域
        self.create_header_section()

        # 创建文件选择区域
        self.create_file_selection_section()

        # 创建输出设置区域
        self.create_output_settings_section()

        # 创建进度显示区域
        self.create_progress_section()

        # 创建控制按钮区域
        self.create_control_buttons_section()

        # 创建日志和结果显示区域
        self.create_log_and_results_section()

    def create_main_container(self):
        """创建主容器"""
        # 主框架
        self.main_frame = ttk.Frame(self.root, padding="15")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        self.main_frame.columnconfigure(1, weight=1)
        self.main_frame.rowconfigure(5, weight=1)  

    def create_header_section(self):
        """创建标题区域"""
        header_frame = ttk.Frame(self.main_frame)
        header_frame.grid(row=0, column=0, columnspan=3, pady=(0, 20), sticky=(tk.W, tk.E))

        # 主标题
        title_label = ttk.Label(header_frame, text="GDB数据质量检查工具",
                               style='Title.TLabel')
        title_label.pack()

        # 副标题
        subtitle_label = ttk.Label(header_frame, text="专业的地理数据库质量检查解决方案",
                                  font=('Arial', 10), foreground='#7f8c8d')
        subtitle_label.pack(pady=(5, 0))

    def create_file_selection_section(self):
        """创建文件选择区域"""
        file_frame = ttk.LabelFrame(self.main_frame, text="文件选择", padding="10")
        file_frame.grid(row=1, column=0, columnspan=3, pady=10, sticky=(tk.W, tk.E))
        file_frame.columnconfigure(1, weight=1)

        # GDB文件夹选择
        ttk.Label(file_frame, text="GDB文件夹:", style='Heading.TLabel').grid(
            row=0, column=0, sticky=tk.W, pady=5, padx=(0, 10))
        gdb_entry = ttk.Entry(file_frame, textvariable=self.gdb_path_var, font=('Consolas', 9))
        gdb_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=5)
        ttk.Button(file_frame, text="浏览文件夹",
                  command=self.browse_gdb_folder).grid(row=0, column=2, pady=5)

        # CSV模板文件选择
        ttk.Label(file_frame, text="CSV模板:", style='Heading.TLabel').grid(
            row=1, column=0, sticky=tk.W, pady=5, padx=(0, 10))
        csv_entry = ttk.Entry(file_frame, textvariable=self.csv_path_var, font=('Consolas', 9))
        csv_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=5)
        ttk.Button(file_frame, text="浏览文件",
                  command=self.browse_csv_file).grid(row=1, column=2, pady=5)

    def create_output_settings_section(self):
        """创建输出设置区域"""
        output_frame = ttk.LabelFrame(self.main_frame, text="输出设置", padding="10")
        output_frame.grid(row=2, column=0, columnspan=3, pady=10, sticky=(tk.W, tk.E))
        output_frame.columnconfigure(1, weight=1)

        # 输出目录选择
        ttk.Label(output_frame, text="输出目录:", style='Heading.TLabel').grid(
            row=0, column=0, sticky=tk.W, pady=5, padx=(0, 10))
        output_entry = ttk.Entry(output_frame, textvariable=self.output_dir_var, font=('Consolas', 9))
        output_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=5)
        ttk.Button(output_frame, text="选择目录",
                  command=self.browse_output_dir).grid(row=0, column=2, pady=5)

        # 保存格式选择
        format_frame = ttk.Frame(output_frame)
        format_frame.grid(row=1, column=0, columnspan=3, pady=10, sticky=(tk.W, tk.E))

        ttk.Label(format_frame, text="保存格式:", style='Heading.TLabel').pack(side=tk.LEFT, padx=(0, 20))

        ttk.Checkbutton(format_frame, text="保存CSV报告",
                       variable=self.save_csv_var).pack(side=tk.LEFT, padx=10)

        # 文件名预览
        self.filename_var = tk.StringVar()
        filename_label = ttk.Label(output_frame, textvariable=self.filename_var,
                                  font=('Consolas', 8), foreground='#7f8c8d')
        filename_label.grid(row=2, column=0, columnspan=3, pady=(10, 0), sticky=tk.W)

    def create_progress_section(self):
        """创建进度显示区域"""
        progress_frame = ttk.LabelFrame(self.main_frame, text="检查进度", padding="10")
        progress_frame.grid(row=3, column=0, columnspan=3, pady=10, sticky=(tk.W, tk.E))
        progress_frame.columnconfigure(0, weight=1)

        # 当前步骤显示
        step_label = ttk.Label(progress_frame, textvariable=self.current_step_var,
                              font=('Arial', 10, 'bold'))
        step_label.grid(row=0, column=0, pady=(0, 10), sticky=tk.W)

        # 进度条
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                          maximum=100, length=500, mode='determinate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        # 进度百分比和状态
        progress_info_frame = ttk.Frame(progress_frame)
        progress_info_frame.grid(row=2, column=0, sticky=(tk.W, tk.E))
        progress_info_frame.columnconfigure(1, weight=1)

        self.progress_percent_var = tk.StringVar(value="0%")
        ttk.Label(progress_info_frame, textvariable=self.progress_percent_var).pack(side=tk.LEFT)

        ttk.Label(progress_info_frame, textvariable=self.status_var,
                 foreground='#27ae60').pack(side=tk.RIGHT)

    def create_control_buttons_section(self):
        """创建控制按钮区域"""
        button_frame = ttk.Frame(self.main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=20)

        # 主要操作按钮
        self.start_button = ttk.Button(button_frame, text="🚀 开始检查",
                                      command=self.start_check, style='Success.TButton',
                                      width=15)
        self.start_button.pack(side=tk.LEFT, padx=5)

        self.stop_button = ttk.Button(button_frame, text="⏹ 停止检查",
                                     command=self.stop_check, style='Warning.TButton',
                                     width=15, state='disabled')
        self.stop_button.pack(side=tk.LEFT, padx=5)

        # 辅助操作按钮
        ttk.Button(button_frame, text="🗑 清空",
                  command=self.clear_all, width=12).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="📁 打开输出目录",
                  command=self.open_output_dir, width=15).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="❌ 退出",
                  command=self.on_closing, style='Danger.TButton',
                  width=12).pack(side=tk.LEFT, padx=5)

    def create_log_and_results_section(self):
        """创建日志和结果显示区域"""
        # 创建Notebook来分页显示
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.grid(row=5, column=0, columnspan=3, pady=10, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置主框架的行权重，让notebook可以扩展
        self.main_frame.grid_rowconfigure(5, weight=1)

        # 日志页面
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="📋 实时日志")

        self.log_text = scrolledtext.ScrolledText(log_frame, height=25, wrap=tk.WORD,  # 增加高度
                                                 font=('Consolas', 9))
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 结果页面
        result_frame = ttk.Frame(self.notebook)
        self.notebook.add(result_frame, text="📊 检查结果")

        self.result_text = scrolledtext.ScrolledText(result_frame, height=25, wrap=tk.WORD,  # 增加高度
                                                    font=('Consolas', 9))
        self.result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 错误详情页面
        error_frame = ttk.Frame(self.notebook)
        self.notebook.add(error_frame, text="⚠️ 错误详情")

        self.error_text = scrolledtext.ScrolledText(error_frame, height=25, wrap=tk.WORD,  # 增加高度
                                                   font=('Consolas', 9))
        self.error_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def bind_events(self):
        """绑定事件"""
        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 文件路径变化事件
        self.gdb_path_var.trace_add('write', self.update_filename_preview)
        self.csv_path_var.trace_add('write', self.update_filename_preview)
        self.output_dir_var.trace_add('write', self.update_filename_preview)

        # 启动日志处理
        self.process_log_queue()

    def update_filename_preview(self, *_):
        """更新文件名预览"""
        if self.output_dir_var.get():
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            gdb_name = "检查结果"
            if self.gdb_path_var.get():
                gdb_name = os.path.basename(self.gdb_path_var.get()).replace('.gdb', '')

            if self.save_csv_var.get():
                preview = f"将生成文件: 【GDB检查结果{timestamp}】{gdb_name}.csv"
            else:
                preview = "请选择保存CSV报告"

            self.filename_var.set(preview)
        else:
            self.filename_var.set("请选择输出目录")

    def process_log_queue(self):
        """处理日志队列"""
        try:
            while True:
                record = self.log_queue.get_nowait()
                if record:
                    msg = self.queue_handler.format(record)
                    self.log_text.insert(tk.END, msg + '\n')
                    self.log_text.see(tk.END)
        except queue.Empty:
            pass
        finally:
            self.root.after(100, self.process_log_queue)

    def browse_gdb_folder(self):
        """浏览GDB文件夹"""
        folder_path = filedialog.askdirectory(
            title="选择GDB文件夹",
            # initialdir=os.path.expanduser("~")
            initialdir=os.path.dirname(os.path.abspath(__file__))
        )
        if folder_path:
            self.gdb_path_var.set(folder_path)
            self.log_message(f"选择GDB文件夹: {folder_path}")

    def browse_csv_file(self):
        """浏览CSV模板文件"""
        file_path = filedialog.askopenfilename(
            title="选择CSV模板文件",
            filetypes=[
                ("CSV文件", "*.csv"),
                ("Excel文件", "*.xlsx"),
                ("所有文件", "*.*")
            ],
            # initialdir=os.path.expanduser("~")
            initialdir=os.path.dirname(os.path.abspath(__file__))
        )
        if file_path:
            self.csv_path_var.set(file_path)
            self.log_message(f"选择CSV模板: {file_path}")

    def browse_output_dir(self):
        """浏览输出目录"""
        dir_path = filedialog.askdirectory(
            title="选择输出目录",
            # initialdir=os.path.expanduser("~")
            initialdir= os.path.dirname(os.path.abspath(__file__))
        )
        if dir_path:
            self.output_dir_var.set(dir_path)
            self.log_message(f"选择输出目录: {dir_path}")

    def clear_all(self):
        """清空所有内容"""
        self.gdb_path_var.set("")
        self.csv_path_var.set("")
        self.output_dir_var.set("")
        self.progress_var.set(0)
        self.progress_percent_var.set("0%")
        self.status_var.set("就绪")
        self.current_step_var.set("等待开始...")

        # 清空文本框
        self.log_text.delete(1.0, tk.END)
        self.result_text.delete(1.0, tk.END)
        self.error_text.delete(1.0, tk.END)

        self.log_message("已清空所有内容")

    def open_output_dir(self):
        """打开输出目录"""
        output_dir = self.output_dir_var.get()
        if output_dir and os.path.exists(output_dir):
            import subprocess
            import platform

            if platform.system() == "Windows":
                subprocess.Popen(f'explorer "{output_dir}"')
            elif platform.system() == "Darwin":  # macOS
                subprocess.Popen(["open", output_dir])
            else:  # Linux
                subprocess.Popen(["xdg-open", output_dir])
        else:
            messagebox.showwarning("警告", "输出目录不存在或未设置")

    def log_message(self, message, level="INFO"):
        """记录日志消息"""
        if level == "INFO":
            self.logger.info(message)
        elif level == "WARNING":
            self.logger.warning(message)
        elif level == "ERROR":
            self.logger.error(message)

    def update_progress(self, value, step_text=""):
        """更新进度"""
        self.progress_var.set(value)
        self.progress_percent_var.set(f"{value:.1f}%")
        if step_text:
            self.current_step_var.set(step_text)

    def update_status(self, status):
        """更新状态"""
        self.status_var.set(status)

    def validate_inputs(self):
        """验证输入"""
        errors = []

        # 检查GDB路径
        gdb_path = self.gdb_path_var.get().strip()
        if not gdb_path:
            errors.append("请选择GDB文件夹")
        elif not os.path.exists(gdb_path):
            errors.append("GDB文件夹不存在")

        # 检查CSV模板
        csv_path = self.csv_path_var.get().strip()
        if not csv_path:
            errors.append("请选择CSV模板文件")
        elif not os.path.exists(csv_path):
            errors.append("CSV模板文件不存在")

        # 检查输出目录
        output_dir = self.output_dir_var.get().strip()
        if not output_dir:
            errors.append("请选择输出目录")
        elif not os.path.exists(output_dir):
            errors.append("输出目录不存在")

        # 检查保存格式
        if not self.save_csv_var.get():
            errors.append("请选择保存CSV报告")

        return errors

    def start_check(self):
        """开始检查"""
        if self.is_checking:
            return

        # 验证输入
        errors = self.validate_inputs()
        if errors:
            messagebox.showerror("输入错误", "\n".join(errors))
            return

        # 清空之前的结果
        self.result_text.delete(1.0, tk.END)
        self.error_text.delete(1.0, tk.END)

        # 更新UI状态
        self.is_checking = True
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')

        # 启动检查线程
        self.check_thread = threading.Thread(target=self.perform_comprehensive_check)
        self.check_thread.daemon = True
        self.check_thread.start()

        self.log_message("开始GDB数据质量检查")

    def stop_check(self):
        """停止检查"""
        if self.is_checking:
            self.is_checking = False
            self.log_message("用户请求停止检查", "WARNING")
            self.update_status("正在停止...")

    def perform_comprehensive_check(self):
        """执行全面检查"""
        try:
            gdb_path = self.gdb_path_var.get().strip()
            csv_path = self.csv_path_var.get().strip()
            output_dir = self.output_dir_var.get().strip()

            # 步骤1: 确定GDB路径
            self.update_progress(5, "正在分析GDB文件结构...")
            final_gdb_path = self.determine_gdb_path(gdb_path)
            if not final_gdb_path:
                return

            # 步骤2: 执行数据检查
            self.update_progress(20, "正在执行数据质量检查...")
            check_results = self.execute_data_checks(final_gdb_path)
            if not check_results:
                return

            # 步骤3: 生成报告
            self.update_progress(80, "正在生成检查报告...")
            self.generate_reports(check_results, csv_path, output_dir, final_gdb_path)

            # 步骤4: 完成
            self.update_progress(100, "检查完成")
            self.update_status("检查成功完成")
            self.show_completion_summary(check_results)

        except Exception as e:
            self.log_message(f"检查过程发生错误: {str(e)}", "ERROR")
            self.error_text.insert(tk.END, f"错误详情:\n{str(e)}\n")
            messagebox.showerror("检查失败", f"检查过程中发生错误:\n{str(e)}")
        finally:
            # 恢复UI状态
            self.is_checking = False
            self.root.after(0, lambda: self.start_button.config(state='normal'))
            self.root.after(0, lambda: self.stop_button.config(state='disabled'))

    def determine_gdb_path(self, gdb_path):
        """确定最终的GDB路径"""
        if gdb_path.lower().endswith('.gdb'):
            self.log_message(f"直接使用GDB文件: {os.path.basename(gdb_path)}")
            return gdb_path
        else:
            gdb_files = [f for f in os.listdir(gdb_path) if f.lower().endswith('.gdb')]
            if not gdb_files:
                self.log_message("未找到GDB文件", "ERROR")
                messagebox.showerror("错误", "在指定目录中未找到GDB文件")
                return None

            if len(gdb_files) == 1:
                final_path = os.path.join(gdb_path, gdb_files[0])
                self.log_message(f"自动选择GDB文件: {gdb_files[0]}")
                return final_path
            else:
                # 使用第一个GDB文件
                final_path = os.path.join(gdb_path, gdb_files[0])
                self.log_message(f"找到多个GDB文件，使用: {gdb_files[0]}", "WARNING")
                return final_path

    def execute_data_checks(self, gdb_path):
        """执行数据检查"""
        try:
            self.log_message("开始执行数据质量检查")
            self.log_message("使用内置行政区代码映射表进行3701检查")

            check_results = perform_comprehensive_real_checks(gdb_path)

            # 获取详细信息
            global _last_check_details
            self.check_details = _last_check_details if '_last_check_details' in globals() else {}

            # 统计结果
            correct_count = sum(1 for result in check_results.values() if result == "正确")
            error_count = sum(1 for result in check_results.values() if result.startswith("错误"))
            note_count = sum(1 for result in check_results.values() if "备注" in result)

            self.log_message(f"检查完成 - 正确: {correct_count}, 错误: {error_count}, 备注: {note_count}")

            # 如果有4101错误，记录重叠详情
            if '4101' in self.check_details and self.check_details['4101']:
                overlap_count = len(self.check_details['4101'])
                self.log_message(f"发现 {overlap_count} 对重叠图斑，详细信息将包含在报告中")

            return check_results

        except Exception as e:
            self.log_message(f"数据检查失败: {str(e)}", "ERROR")
            raise

    def generate_reports(self, check_results, csv_path, output_dir, gdb_path):
        """生成报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        gdb_name = os.path.basename(gdb_path).replace('.gdb', '')

        generated_files = []
        # 生成CSV报告
        if self.save_csv_var.get():
            csv_output_path = os.path.join(output_dir, f"【GDB检查结果{timestamp}】{gdb_name}.csv")
            try:
                # 获取详细信息
                details = getattr(self, 'check_details', {})
                update_summary_table(csv_path, check_results, details, timestamp, csv_output_path)
                generated_files.append(csv_output_path)
                self.log_message(f"CSV报告已生成: {os.path.basename(csv_output_path)}")
            except Exception as e:
                self.log_message(f"CSV报告生成失败: {str(e)}", "ERROR")
        return generated_files

    def show_completion_summary(self, check_results):
        """显示完成摘要"""
        # 统计结果
        correct_count = sum(1 for result in check_results.values() if result.startswith("正确"))
        error_count = sum(1 for result in check_results.values() if result.startswith("错误"))
        note_count = sum(1 for result in check_results.values() if "备注" in result)
        total_count = len(check_results)

        # 在结果页面显示摘要
        summary = f"""检查完成摘要
{'='*50}
总检查项目: {total_count}
✅ 正确: {correct_count} 项 ({correct_count/total_count*100:.1f}%)
❌ 错误: {error_count} 项 ({error_count/total_count*100:.1f}%)
📝 备注/手动: {note_count} 项 ({note_count/total_count*100:.1f}%)

"""

        if error_count > 0:
            summary += "主要错误项目:\n"
            summary += "-" * 30 + "\n"
            for code, result in check_results.items():
                if result.startswith("错误"):
                    summary += f"{code}: {result}\n"

        self.result_text.insert(1.0, summary)

        # 更新错误详情页面
        self.display_error_details(check_results)

        # 切换到结果页面
        self.notebook.select(1)

        # 显示完成对话框
        message = f"检查完成！\n\n正确: {correct_count} 项\n错误: {error_count} 项\n备注: {note_count} 项"
        if error_count > 0:
            messagebox.showwarning("检查完成", message + f"\n\n发现 {error_count} 个错误，请查看详细结果")
        else:
            messagebox.showinfo("检查完成", message + "\n\n所有检查项目均通过！")

    def display_error_details(self, check_results):
        """显示错误详情（简化版，只显示BSM）"""
        self.error_text.delete(1.0, tk.END)

        # 获取详细信息
        details = getattr(self, 'check_details', {})

        error_content = "错误详情分析\n"
        error_content += "=" * 50 + "\n\n"

        # 统计错误数量
        error_count = sum(1 for result in check_results.values() if result.startswith("错误"))

        if error_count == 0:
            error_content += "恭喜！没有发现任何错误！\n\n"
            error_content += "所有检查项目均通过质量检查标准。\n"
        else:
            error_content += f"发现 {error_count} 个错误项目，详细信息如下：\n\n"

            # 显示错误项目
            for check_code, result in sorted(check_results.items()):
                if result.startswith("错误"):
                    error_content += f"【{check_code}】 {result}\n"

                    # 显示涉及的BSM
                    if check_code in details and details[check_code]:
                        error_content += f"  涉及BSM: {details[check_code]}\n"


            # 添加修复建议
            error_content += "\n修复建议\n"
            error_content += "-" * 30 + "\n"

            for check_code, result in check_results.items():
                if result.startswith("错误"):
                    if check_code == '3301':
                        error_content += "• 代码一致性错误: 请检查字段值是否符合规范要求\n"
                    elif check_code == '3302':
                        error_content += "• BSM格式错误: 请按照'行政区代码+顺序码'格式修正BSM字段\n"
                    elif check_code == '3501':
                        error_content += "• BSM重复值: 请为重复的BSM分配唯一的顺序码\n"
                    elif check_code == '3601':
                        error_content += "• 字段必填性错误: 请填写所有必填字段的值\n"
                    elif check_code == '4101':
                        error_content += "• 拓扑重叠: 请调整图斑边界，消除重叠区域\n"
                    elif check_code == '2101':
                        error_content += "• 坐标系错误: 请将数据转换为CGCS2000坐标系\n"
                    elif check_code.startswith('32'):
                        error_content += f"• 字段结构错误({check_code}): 请按照技术要求调整字段结构\n"

        self.error_text.insert(1.0, error_content)

    def on_closing(self):
        """窗口关闭事件"""
        if self.is_checking:
            if messagebox.askokcancel("确认退出", "检查正在进行中，确定要退出吗？"):
                self.is_checking = False
                self.root.destroy()
        else:
            self.root.destroy()

def main():
    """主函数-采用GUI应用程序的标准架构，检查过程函数在GUI事件处理中"""
    try:
        # 1. 初始化应用程序
        root = tk.Tk()

        # 2. 设置应用程序属性
        root.title("GDB数据质量检查工具 v3.0")

        # 3. 创建GUI应用
        app = GDBCheckerGUI(root)

        # 4. 设置默认输出目录
        # default_output = os.path.join(os.path.expanduser("~"), "Desktop", "GDB检查结果")
        default_output = f"{os.path.dirname(os.path.abspath(__file__))}\\GDB检查结果\\"
        if not os.path.exists(default_output):
            try:
                os.makedirs(default_output)
                app.output_dir_var.set(default_output)
                app.log_message(f"创建默认输出目录: {default_output}")
            except:
                pass
        else:
            app.output_dir_var.set(default_output)

        # 5. 显示欢迎信息
        welcome_msg = """欢迎使用GDB数据质量检查工具 v3.0

功能特点:
1. 全面的数据质量检查
2. 实时进度显示和日志记录
3. 支持CSV报告输出
4. 专业的错误分析和详情展示
5. 内置行政区代码映射表

使用步骤:
1. 选择GDB文件夹和CSV模板
2. 设置输出目录
3. 点击"开始检查"执行检查
4. 查看实时日志和检查结果
"""
        app.log_text.insert(1.0, welcome_msg)

        # 6. 启动GUI主循环
        root.mainloop()

    except Exception as e:
        # 7. 错误处理
        messagebox.showerror("启动失败", f"应用程序启动失败:\n{str(e)}")

    finally:
        # 8. 清理资源
        try:
            root.destroy()
        except:
            pass

if __name__ == "__main__":
    main()
