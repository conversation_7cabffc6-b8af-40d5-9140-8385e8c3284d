"""
Source Apportionment Core Module

This module implements the core pollution source apportionment algorithms
following the latest scientific standards for Environmental Science & Technology journal.

Authors: <AUTHORS>
Version: 3.0 (Refactored)
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from abc import ABC, abstractmethod

from sklearn.decomposition import NMF
from sklearn.metrics import mean_squared_error, r2_score
from scipy.spatial.distance import cdist
from scipy.optimize import minimize
from scipy import stats

from config import ConfigManager, get_config_manager


@dataclass
class SourceApportionmentResult:
    """Results from source apportionment analysis."""
    
    source_profiles: np.ndarray
    source_contributions: np.ndarray
    receptor_fingerprints: np.ndarray
    source_fingerprints: np.ndarray
    similarity_matrix: np.ndarray
    uncertainty_bounds: Optional[Dict[str, np.ndarray]] = None
    model_metrics: Optional[Dict[str, float]] = None
    source_interpretations: Optional[Dict[int, str]] = None
    
    def __post_init__(self):
        """Validate result dimensions."""
        n_samples, n_metals = self.receptor_fingerprints.shape
        n_sources, _ = self.source_fingerprints.shape
        
        if self.source_profiles.shape != (self.source_profiles.shape[0], n_metals):
            raise ValueError("Source profiles dimension mismatch")
        
        if self.source_contributions.shape != (n_samples, self.source_profiles.shape[0]):
            raise ValueError("Source contributions dimension mismatch")


class FingerprintCalculator:
    """Calculate standardized chemical fingerprints for sources and receptors."""
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize fingerprint calculator.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.logger = logging.getLogger(__name__)
    
    def calculate_source_fingerprints(self, sources: pd.DataFrame, 
                                    metals: List[str]) -> np.ndarray:
        """
        Calculate standardized source fingerprints.
        
        Args:
            sources: Source data DataFrame
            metals: List of metal columns
            
        Returns:
            Normalized source fingerprints array
        """
        # Extract metal data
        source_data = sources[metals].apply(pd.to_numeric, errors='coerce')
        
        # Handle missing values with median imputation
        source_data = source_data.fillna(source_data.median())
        
        # Apply log transformation to reduce skewness
        source_data_log = np.log1p(source_data.clip(lower=0))
        
        # Normalize to unit sum (compositional data)
        source_fingerprints = source_data_log.div(
            source_data_log.sum(axis=1), axis=0
        ).fillna(0).values
        
        # Additional normalization to handle edge cases
        row_sums = np.sum(source_fingerprints, axis=1, keepdims=True)
        source_fingerprints = np.divide(
            source_fingerprints, 
            row_sums, 
            out=np.zeros_like(source_fingerprints),
            where=row_sums != 0
        )
        
        self.logger.info(f"Calculated source fingerprints: {source_fingerprints.shape}")
        return source_fingerprints
    
    def calculate_receptor_fingerprints(self, receptors: pd.DataFrame, 
                                      metals: List[str]) -> np.ndarray:
        """
        Calculate standardized receptor fingerprints.
        
        Args:
            receptors: Receptor (soil) data DataFrame
            metals: List of metal columns
            
        Returns:
            Normalized receptor fingerprints array
        """
        # Extract metal data
        receptor_data = receptors[metals].apply(pd.to_numeric, errors='coerce')
        
        # Handle missing values
        receptor_data = receptor_data.fillna(receptor_data.median())
        
        # Apply log transformation
        receptor_data_log = np.log1p(receptor_data.clip(lower=0))
        
        # Normalize to unit sum
        receptor_fingerprints = receptor_data_log.div(
            receptor_data_log.sum(axis=1), axis=0
        ).fillna(0).values
        
        # Additional normalization
        row_sums = np.sum(receptor_fingerprints, axis=1, keepdims=True)
        receptor_fingerprints = np.divide(
            receptor_fingerprints,
            row_sums,
            out=np.zeros_like(receptor_fingerprints),
            where=row_sums != 0
        )
        
        self.logger.info(f"Calculated receptor fingerprints: {receptor_fingerprints.shape}")
        return receptor_fingerprints
    
    def calculate_similarity_matrix(self, source_fingerprints: np.ndarray,
                                  receptor_fingerprints: np.ndarray) -> np.ndarray:
        """
        Calculate similarity matrix between sources and receptors.
        
        Args:
            source_fingerprints: Source fingerprint array
            receptor_fingerprints: Receptor fingerprint array
            
        Returns:
            Similarity matrix (receptors x sources)
        """
        # Cosine similarity
        cos_sim = np.dot(receptor_fingerprints, source_fingerprints.T)
        cos_sim = np.clip(cos_sim, 0, 1)
        
        # Euclidean distance similarity
        euclidean_dist = cdist(receptor_fingerprints, source_fingerprints, 'euclidean')
        max_dist = np.max(euclidean_dist)
        euclidean_sim = 1 - (euclidean_dist / max_dist) if max_dist > 0 else np.ones_like(euclidean_dist)
        
        # Combined similarity (weighted average)
        combined_similarity = 0.7 * cos_sim + 0.3 * euclidean_sim
        
        self.logger.info(f"Calculated similarity matrix: {combined_similarity.shape}")
        return combined_similarity


class PMFAnalyzer:
    """Positive Matrix Factorization for source apportionment."""
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize PMF analyzer.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.logger = logging.getLogger(__name__)
        self.model = None
    
    def fit(self, receptor_data: np.ndarray, 
            n_factors: Optional[int] = None,
            uncertainty: Optional[np.ndarray] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        Fit PMF model to receptor data.
        
        Args:
            receptor_data: Receptor concentration matrix (samples x metals)
            n_factors: Number of factors (sources)
            uncertainty: Uncertainty matrix for weighted PMF
            
        Returns:
            Tuple of (source_contributions, source_profiles)
        """
        if n_factors is None:
            n_factors = self.config.get_model_config().pmf_n_factors
        
        model_config = self.config.get_model_config()
        
        # Initialize NMF model
        self.model = NMF(
            n_components=n_factors,
            init=model_config.pmf_init_method,
            max_iter=model_config.pmf_max_iter,
            tol=model_config.pmf_tolerance,
            random_state=42
        )
        
        # Ensure non-negative data
        receptor_data_clean = np.maximum(receptor_data, 0)
        
        # Fit model
        source_contributions = self.model.fit_transform(receptor_data_clean)
        source_profiles = self.model.components_
        
        # Calculate model metrics
        reconstructed = source_contributions @ source_profiles
        mse = mean_squared_error(receptor_data_clean, reconstructed)
        r2 = r2_score(receptor_data_clean.flatten(), reconstructed.flatten())
        
        self.logger.info(f"PMF fitted: {n_factors} factors, MSE={mse:.4f}, R²={r2:.4f}")
        
        return source_contributions, source_profiles
    
    def interpret_sources(self, source_profiles: np.ndarray, 
                         metals: List[str]) -> Dict[int, str]:
        """
        Interpret source profiles using statistical analysis.
        
        Args:
            source_profiles: Source profile matrix
            metals: List of metal names
            
        Returns:
            Dictionary mapping source index to interpretation
        """
        interpretations = {}
        
        for i, profile in enumerate(source_profiles):
            # Find dominant metals (top 2)
            dominant_indices = np.argsort(profile)[-2:][::-1]
            dominant_metals = [metals[idx] for idx in dominant_indices]
            
            # Calculate profile characteristics
            profile_std = np.std(profile)
            profile_max_ratio = np.max(profile) / np.mean(profile)
            
            # Rule-based interpretation
            if 'Pb' in dominant_metals and 'Zn' in dominant_metals:
                source_type = "Traffic/Industrial"
            elif 'Cd' in dominant_metals:
                source_type = "Agricultural"
            elif 'Cu' in dominant_metals and profile_max_ratio > 3:
                source_type = "Industrial/Mining"
            elif 'Cr' in dominant_metals and 'Ni' in dominant_metals:
                source_type = "Natural/Geogenic"
            elif 'As' in dominant_metals:
                source_type = "Pesticide/Anthropogenic"
            else:
                source_type = f"Mixed ({', '.join(dominant_metals[:2])})"
            
            interpretations[i+1] = source_type
        
        return interpretations
    
    def bootstrap_uncertainty(self, receptor_data: np.ndarray, 
                            n_bootstrap: int = 100) -> Dict[str, np.ndarray]:
        """
        Estimate uncertainty using bootstrap resampling.
        
        Args:
            receptor_data: Receptor concentration matrix
            n_bootstrap: Number of bootstrap iterations
            
        Returns:
            Dictionary with uncertainty bounds
        """
        if self.model is None:
            raise ValueError("Model must be fitted first")
        
        n_samples, n_metals = receptor_data.shape
        n_factors = self.model.n_components
        
        # Storage for bootstrap results
        contributions_bootstrap = []
        profiles_bootstrap = []
        
        for i in range(n_bootstrap):
            # Bootstrap sampling
            indices = np.random.choice(n_samples, size=n_samples, replace=True)
            bootstrap_data = receptor_data[indices]
            
            # Fit model to bootstrap sample
            try:
                bootstrap_model = NMF(
                    n_components=n_factors,
                    init=self.config.get_model_config().pmf_init_method,
                    max_iter=self.config.get_model_config().pmf_max_iter,
                    random_state=i
                )
                
                W_boot = bootstrap_model.fit_transform(np.maximum(bootstrap_data, 0))
                H_boot = bootstrap_model.components_
                
                contributions_bootstrap.append(W_boot)
                profiles_bootstrap.append(H_boot)
                
            except Exception as e:
                self.logger.warning(f"Bootstrap iteration {i} failed: {e}")
                continue
        
        if not contributions_bootstrap:
            raise RuntimeError("All bootstrap iterations failed")
        
        # Calculate percentiles
        contributions_array = np.array(contributions_bootstrap)
        profiles_array = np.array(profiles_bootstrap)
        
        uncertainty_bounds = {
            'contributions_lower': np.percentile(contributions_array, 5, axis=0),
            'contributions_upper': np.percentile(contributions_array, 95, axis=0),
            'profiles_lower': np.percentile(profiles_array, 5, axis=0),
            'profiles_upper': np.percentile(profiles_array, 95, axis=0)
        }
        
        self.logger.info(f"Bootstrap uncertainty calculated with {len(contributions_bootstrap)} iterations")
        return uncertainty_bounds


class SourceApportionmentEngine:
    """Main engine for pollution source apportionment analysis."""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize source apportionment engine.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager or get_config_manager()
        self.logger = logging.getLogger(__name__)
        
        self.fingerprint_calc = FingerprintCalculator(self.config)
        self.pmf_analyzer = PMFAnalyzer(self.config)
    
    def analyze(self, soil_data: pd.DataFrame, 
                source_data: pd.DataFrame,
                include_uncertainty: bool = True) -> SourceApportionmentResult:
        """
        Perform complete source apportionment analysis.
        
        Args:
            soil_data: Soil sample data
            source_data: Source sample data
            include_uncertainty: Whether to include uncertainty analysis
            
        Returns:
            Complete source apportionment results
        """
        metals = self.config.get_data_config().metals
        
        self.logger.info("Starting source apportionment analysis")
        
        # Calculate fingerprints
        source_fingerprints = self.fingerprint_calc.calculate_source_fingerprints(
            source_data, metals
        )
        receptor_fingerprints = self.fingerprint_calc.calculate_receptor_fingerprints(
            soil_data, metals
        )
        
        # Calculate similarity matrix
        similarity_matrix = self.fingerprint_calc.calculate_similarity_matrix(
            source_fingerprints, receptor_fingerprints
        )
        
        # PMF analysis
        receptor_data = soil_data[metals].values
        source_contributions, source_profiles = self.pmf_analyzer.fit(receptor_data)
        
        # Source interpretation
        source_interpretations = self.pmf_analyzer.interpret_sources(
            source_profiles, metals
        )
        
        # Model metrics
        reconstructed = source_contributions @ source_profiles
        model_metrics = {
            'mse': mean_squared_error(receptor_data, reconstructed),
            'r2': r2_score(receptor_data.flatten(), reconstructed.flatten()),
            'explained_variance': np.var(reconstructed) / np.var(receptor_data)
        }
        
        # Uncertainty analysis
        uncertainty_bounds = None
        if include_uncertainty:
            try:
                uncertainty_bounds = self.pmf_analyzer.bootstrap_uncertainty(receptor_data)
            except Exception as e:
                self.logger.warning(f"Uncertainty analysis failed: {e}")
        
        result = SourceApportionmentResult(
            source_profiles=source_profiles,
            source_contributions=source_contributions,
            receptor_fingerprints=receptor_fingerprints,
            source_fingerprints=source_fingerprints,
            similarity_matrix=similarity_matrix,
            uncertainty_bounds=uncertainty_bounds,
            model_metrics=model_metrics,
            source_interpretations=source_interpretations
        )
        
        self.logger.info("Source apportionment analysis completed")
        return result

    def validate_results(self, result: SourceApportionmentResult) -> Dict[str, bool]:
        """
        Validate analysis results for scientific rigor.

        Args:
            result: Source apportionment results

        Returns:
            Dictionary of validation checks
        """
        validations = {}

        # Check mass balance
        mass_balance_error = np.abs(
            np.sum(result.source_contributions, axis=1) -
            np.sum(result.receptor_fingerprints, axis=1)
        )
        validations['mass_balance'] = np.all(mass_balance_error < 0.1)

        # Check non-negativity
        validations['non_negative_contributions'] = np.all(result.source_contributions >= 0)
        validations['non_negative_profiles'] = np.all(result.source_profiles >= 0)

        # Check model fit quality
        if result.model_metrics:
            validations['good_fit'] = result.model_metrics['r2'] > 0.7
        else:
            validations['good_fit'] = False

        # Check source interpretability
        validations['interpretable_sources'] = (
            result.source_interpretations is not None and
            len(result.source_interpretations) > 0
        )

        return validations
