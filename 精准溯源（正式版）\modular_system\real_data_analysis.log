2025-07-30 12:43:07,261 - __main__ - INFO - ================================================================================
2025-07-30 12:43:07,262 - __main__ - INFO - 开始真实数据污染源解析分析
2025-07-30 12:43:07,262 - __main__ - INFO - ================================================================================
2025-07-30 12:43:07,263 - __main__ - INFO - 第一步：加载真实数据
2025-07-30 12:43:07,317 - __main__ - INFO - 成功加载 114 个土壤样本
2025-07-30 12:43:07,470 - __main__ - INFO - 成功加载 213 个污染源
2025-07-30 12:43:07,472 - __main__ - INFO - 真实数据加载完成
2025-07-30 12:43:07,472 - __main__ - INFO - 第二步：PMF源解析分析
2025-07-30 12:43:07,904 - __main__ - INFO - PMF分析完成
2025-07-30 12:43:07,904 - __main__ - INFO - 第三步：高级特征工程
2025-07-30 12:43:09,751 - __main__ - INFO - 特征工程完成，共创建 150 个特征
2025-07-30 12:43:09,752 - __main__ - INFO - 第四步：深度学习分析
2025-07-30 12:43:10,494 - __main__ - INFO - 深度学习分析完成
2025-07-30 12:43:10,495 - __main__ - INFO - 第五步：最优传输模型
2025-07-30 12:43:11,977 - __main__ - INFO - 最优传输分析完成
2025-07-30 12:43:11,978 - __main__ - INFO - 第六步：空间交叉验证
2025-07-30 12:43:17,892 - __main__ - INFO - 空间交叉验证完成
2025-07-30 12:43:17,893 - __main__ - INFO - 第七步：结果整合
2025-07-30 12:43:18,225 - __main__ - INFO - 第八步：生成可视化
2025-07-30 12:43:18,562 - __main__ - ERROR - 分析过程中发生错误: 'AcademicVisualizer' object has no attribute 'visualize_results'
2025-07-30 12:45:06,466 - __main__ - INFO - ================================================================================
2025-07-30 12:45:06,467 - __main__ - INFO - 开始真实数据污染源解析分析
2025-07-30 12:45:06,467 - __main__ - INFO - ================================================================================
2025-07-30 12:45:06,467 - __main__ - INFO - 第一步：加载真实数据
2025-07-30 12:45:06,503 - __main__ - INFO - 成功加载 114 个土壤样本
2025-07-30 12:45:06,556 - __main__ - INFO - 成功加载 213 个污染源
2025-07-30 12:45:06,558 - __main__ - INFO - 真实数据加载完成
2025-07-30 12:45:06,559 - __main__ - INFO - 第二步：PMF源解析分析
2025-07-30 12:45:06,609 - __main__ - INFO - PMF分析完成
2025-07-30 12:45:06,610 - __main__ - INFO - 第三步：高级特征工程
2025-07-30 12:45:08,460 - __main__ - INFO - 特征工程完成，共创建 150 个特征
2025-07-30 12:45:08,461 - __main__ - INFO - 第四步：深度学习分析
2025-07-30 12:45:09,192 - __main__ - INFO - 深度学习分析完成
2025-07-30 12:45:09,193 - __main__ - INFO - 第五步：最优传输模型
2025-07-30 12:45:10,452 - __main__ - INFO - 最优传输分析完成
2025-07-30 12:45:10,452 - __main__ - INFO - 第六步：空间交叉验证
2025-07-30 12:45:16,081 - __main__ - INFO - 空间交叉验证完成
2025-07-30 12:45:16,083 - __main__ - INFO - 第七步：结果整合
2025-07-30 12:45:16,337 - __main__ - INFO - 第八步：生成可视化
2025-07-30 12:45:24,183 - __main__ - ERROR - 分析过程中发生错误: AcademicVisualizer.visualize_wind_influence_analysis() missing 2 required positional arguments: 'pollutants' and 'output_dir'
2025-07-30 12:46:22,952 - __main__ - INFO - ================================================================================
2025-07-30 12:46:22,952 - __main__ - INFO - 开始真实数据污染源解析分析
2025-07-30 12:46:22,953 - __main__ - INFO - ================================================================================
2025-07-30 12:46:22,953 - __main__ - INFO - 第一步：加载真实数据
2025-07-30 12:46:22,986 - __main__ - INFO - 成功加载 114 个土壤样本
2025-07-30 12:46:23,033 - __main__ - INFO - 成功加载 213 个污染源
2025-07-30 12:46:23,035 - __main__ - INFO - 真实数据加载完成
2025-07-30 12:46:23,035 - __main__ - INFO - 第二步：PMF源解析分析
2025-07-30 12:46:23,081 - __main__ - INFO - PMF分析完成
2025-07-30 12:46:23,081 - __main__ - INFO - 第三步：高级特征工程
2025-07-30 12:46:25,000 - __main__ - INFO - 特征工程完成，共创建 150 个特征
2025-07-30 12:46:25,001 - __main__ - INFO - 第四步：深度学习分析
2025-07-30 12:46:25,741 - __main__ - INFO - 深度学习分析完成
2025-07-30 12:46:25,742 - __main__ - INFO - 第五步：最优传输模型
2025-07-30 12:46:27,022 - __main__ - INFO - 最优传输分析完成
2025-07-30 12:46:27,023 - __main__ - INFO - 第六步：空间交叉验证
2025-07-30 12:46:32,584 - __main__ - INFO - 空间交叉验证完成
2025-07-30 12:46:32,585 - __main__ - INFO - 第七步：结果整合
2025-07-30 12:46:32,843 - __main__ - INFO - 第八步：生成可视化
2025-07-30 12:46:38,207 - __main__ - INFO - 可视化图表生成完成
2025-07-30 12:46:38,207 - __main__ - INFO - 第九步：导出结果
2025-07-30 12:46:38,465 - __main__ - INFO - 分析结果导出完成
2025-07-30 12:46:38,465 - __main__ - INFO - ================================================================================
2025-07-30 12:46:38,466 - __main__ - INFO - 真实数据分析完成！
2025-07-30 12:46:38,466 - __main__ - INFO - ================================================================================
2025-07-30 14:53:41,772 - __main__ - INFO - ================================================================================
2025-07-30 14:53:41,773 - __main__ - INFO - 开始真实数据污染源解析分析
2025-07-30 14:53:41,773 - __main__ - INFO - ================================================================================
2025-07-30 14:53:41,773 - __main__ - INFO - 第一步：加载真实数据
2025-07-30 14:53:41,799 - __main__ - INFO - 成功加载 114 个土壤样本
2025-07-30 14:53:41,839 - __main__ - INFO - 成功加载 213 个污染源
2025-07-30 14:53:41,841 - __main__ - INFO - 真实数据加载完成
2025-07-30 14:53:41,841 - __main__ - INFO - 第二步：PMF源解析分析
2025-07-30 14:53:41,889 - __main__ - INFO - PMF分析完成
2025-07-30 14:53:41,889 - __main__ - INFO - 第三步：高级特征工程
2025-07-30 14:53:43,816 - __main__ - INFO - 特征工程完成，共创建 150 个特征
2025-07-30 14:53:43,816 - __main__ - INFO - 第四步：深度学习分析
2025-07-30 14:53:44,489 - __main__ - INFO - 深度学习分析完成
2025-07-30 14:53:44,489 - __main__ - INFO - 第五步：最优传输模型
2025-07-30 14:53:45,656 - __main__ - INFO - 最优传输分析完成
2025-07-30 14:53:45,657 - __main__ - INFO - 第六步：空间交叉验证
2025-07-30 14:53:51,250 - __main__ - INFO - 空间交叉验证完成
2025-07-30 14:53:51,251 - __main__ - INFO - 第七步：结果整合
2025-07-30 14:53:51,502 - __main__ - INFO - 第八步：生成可视化
2025-07-30 14:53:53,137 - __main__ - ERROR - 分析过程中发生错误: 'pollution_cluster'
2025-07-30 14:55:30,169 - __main__ - INFO - ================================================================================
2025-07-30 14:55:30,169 - __main__ - INFO - 开始真实数据污染源解析分析
2025-07-30 14:55:30,170 - __main__ - INFO - ================================================================================
2025-07-30 14:55:30,170 - __main__ - INFO - 第一步：加载真实数据
2025-07-30 14:55:30,200 - __main__ - INFO - 成功加载 114 个土壤样本
2025-07-30 14:55:30,258 - __main__ - INFO - 成功加载 213 个污染源
2025-07-30 14:55:30,260 - __main__ - INFO - 真实数据加载完成
2025-07-30 14:55:30,261 - __main__ - INFO - 第二步：PMF源解析分析
2025-07-30 14:55:30,314 - __main__ - INFO - PMF分析完成
2025-07-30 14:55:30,314 - __main__ - INFO - 第三步：高级特征工程
2025-07-30 14:55:32,306 - __main__ - INFO - 特征工程完成，共创建 150 个特征
2025-07-30 14:55:32,307 - __main__ - INFO - 第四步：深度学习分析
2025-07-30 14:55:33,039 - __main__ - INFO - 深度学习分析完成
2025-07-30 14:55:33,040 - __main__ - INFO - 第五步：最优传输模型
2025-07-30 14:55:34,554 - __main__ - INFO - 最优传输分析完成
2025-07-30 14:55:34,554 - __main__ - INFO - 第六步：空间交叉验证
2025-07-30 14:55:40,142 - __main__ - INFO - 空间交叉验证完成
2025-07-30 14:55:40,143 - __main__ - INFO - 第七步：结果整合
2025-07-30 14:55:40,398 - __main__ - INFO - 第八步：生成可视化
2025-07-30 14:56:02,492 - __main__ - ERROR - 分析过程中发生错误: 'pollution_cluster'
2025-07-30 14:56:55,241 - __main__ - INFO - ================================================================================
2025-07-30 14:56:55,241 - __main__ - INFO - 开始真实数据污染源解析分析
2025-07-30 14:56:55,241 - __main__ - INFO - ================================================================================
2025-07-30 14:56:55,242 - __main__ - INFO - 第一步：加载真实数据
2025-07-30 14:56:55,287 - __main__ - INFO - 成功加载 114 个土壤样本
2025-07-30 14:56:55,350 - __main__ - INFO - 成功加载 213 个污染源
2025-07-30 14:56:55,351 - __main__ - INFO - 真实数据加载完成
2025-07-30 14:56:55,351 - __main__ - INFO - 第二步：PMF源解析分析
2025-07-30 14:56:55,403 - __main__ - INFO - PMF分析完成
2025-07-30 14:56:55,411 - __main__ - INFO - 第三步：高级特征工程
2025-07-30 14:56:57,635 - __main__ - INFO - 特征工程完成，共创建 150 个特征
2025-07-30 14:56:57,636 - __main__ - INFO - 第四步：深度学习分析
2025-07-30 14:56:59,089 - __main__ - INFO - 深度学习分析完成
2025-07-30 14:56:59,090 - __main__ - INFO - 第五步：最优传输模型
2025-07-30 14:57:00,829 - __main__ - INFO - 最优传输分析完成
2025-07-30 14:57:00,830 - __main__ - INFO - 第六步：空间交叉验证
2025-07-30 14:57:07,332 - __main__ - INFO - 空间交叉验证完成
2025-07-30 14:57:07,335 - __main__ - INFO - 第七步：结果整合
2025-07-30 14:57:07,759 - __main__ - INFO - 第八步：生成可视化
2025-07-30 14:57:37,561 - __main__ - INFO - 可视化图表生成完成
2025-07-30 14:57:37,561 - __main__ - INFO - 第九步：导出结果
2025-07-30 14:57:37,813 - __main__ - INFO - 分析结果导出完成
2025-07-30 14:57:37,814 - __main__ - INFO - ================================================================================
2025-07-30 14:57:37,814 - __main__ - INFO - 真实数据分析完成！
2025-07-30 14:57:37,814 - __main__ - INFO - ================================================================================
2025-07-30 15:45:51,542 - __main__ - INFO - ================================================================================
2025-07-30 15:45:51,542 - __main__ - INFO - 开始真实数据污染源解析分析
2025-07-30 15:45:51,542 - __main__ - INFO - ================================================================================
2025-07-30 15:45:51,542 - __main__ - INFO - 第一步：加载真实数据
2025-07-30 15:45:51,564 - __main__ - INFO - 成功加载 114 个土壤样本
2025-07-30 15:45:51,595 - __main__ - INFO - 成功加载 213 个污染源
2025-07-30 15:45:51,596 - __main__ - INFO - 真实数据加载完成
2025-07-30 15:45:51,596 - __main__ - INFO - 第二步：PMF源解析分析
2025-07-30 15:45:51,718 - __main__ - INFO - PMF分析完成
2025-07-30 15:45:51,718 - __main__ - INFO - 第三步：高级特征工程
2025-07-30 15:45:53,725 - __main__ - INFO - 特征工程完成，共创建 150 个特征
2025-07-30 15:45:53,725 - __main__ - INFO - 第四步：深度学习分析
2025-07-30 15:46:50,084 - __main__ - INFO - 深度学习分析完成
2025-07-30 15:46:50,084 - __main__ - INFO - 第五步：最优传输模型
2025-07-30 15:46:51,022 - __main__ - INFO - 最优传输分析完成
2025-07-30 15:46:51,023 - __main__ - INFO - 第六步：空间交叉验证
2025-07-30 15:46:56,955 - __main__ - INFO - 空间交叉验证完成
2025-07-30 15:46:56,956 - __main__ - INFO - 第七步：结果整合
2025-07-30 15:46:57,253 - __main__ - INFO - 第八步：生成可视化
2025-07-30 15:47:17,548 - __main__ - INFO - 可视化图表生成完成
2025-07-30 15:47:17,548 - __main__ - INFO - 第九步：导出结果
2025-07-30 15:47:17,844 - __main__ - INFO - 分析结果导出完成
2025-07-30 15:47:17,844 - __main__ - INFO - ================================================================================
2025-07-30 15:47:17,844 - __main__ - INFO - 真实数据分析完成！
2025-07-30 15:47:17,844 - __main__ - INFO - ================================================================================
