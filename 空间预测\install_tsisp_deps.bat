@echo off
chcp 65001 >nul
echo ================================================================
echo TSISP依赖安装脚本 (Windows)
echo 解决pip命令无法识别的问题
echo ================================================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python命令不可用，尝试使用py命令...
    py --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ 无法找到Python，请确保Python已正确安装
        echo 下载地址: https://www.python.org/downloads/
        pause
        exit /b 1
    ) else (
        echo ✓ 找到Python (py命令)
        set PYTHON_CMD=py
    )
) else (
    echo ✓ 找到Python (python命令)
    set PYTHON_CMD=python
)

echo.
echo 正在检查pip...
%PYTHON_CMD% -m pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ pip不可用
    pause
    exit /b 1
) else (
    echo ✓ pip可用
)

echo.
echo ================================================================
echo 开始安装PyTorch Geometric依赖
echo ================================================================
echo.

echo 1. 检查PyTorch...
%PYTHON_CMD% -c "import torch; print('PyTorch版本:', torch.__version__)" 2>nul
if %errorlevel% neq 0 (
    echo ❌ PyTorch未安装，正在安装...
    %PYTHON_CMD% -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
    if %errorlevel% neq 0 (
        echo ❌ PyTorch安装失败
        pause
        exit /b 1
    )
) else (
    echo ✓ PyTorch已安装
)

echo.
echo 2. 安装PyTorch Geometric...
%PYTHON_CMD% -m pip install torch-geometric
if %errorlevel% neq 0 (
    echo ⚠️ torch-geometric安装失败，但不影响基本功能
)

echo.
echo 3. 安装图构建依赖...
%PYTHON_CMD% -m pip install torch-scatter torch-sparse torch-cluster torch-spline-conv
if %errorlevel% neq 0 (
    echo ⚠️ 部分图构建依赖安装失败，将使用fallback实现
)

echo.
echo ================================================================
echo 运行安装验证脚本
echo ================================================================
echo.

%PYTHON_CMD% install_windows.py

echo.
echo ================================================================
echo 安装完成
echo ================================================================
echo.
echo 现在您可以运行TSISP模型：
echo.
echo 单金属训练：
echo   %PYTHON_CMD% train_tsisp.py --metal Pb --output pb_model
echo.
echo 批量训练：
echo   %PYTHON_CMD% train_tsisp.py --batch --metals Pb Cd Cu --output batch_models
echo.
echo 测试修复效果：
echo   %PYTHON_CMD% test_both_fixes.py
echo.

pause
