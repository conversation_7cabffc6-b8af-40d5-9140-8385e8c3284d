#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
TSISP依赖安装脚本

自动检测和安装PyTorch Geometric相关依赖
"""

import subprocess
import sys
import importlib
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_package(package_name):
    """检查包是否已安装"""
    try:
        importlib.import_module(package_name)
        return True
    except ImportError:
        return False

def install_package(package_name, pip_name=None):
    """安装包"""
    if pip_name is None:
        pip_name = package_name
    
    try:
        print(f"正在安装 {pip_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", pip_name])
        print(f"✓ {pip_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {pip_name} 安装失败: {e}")
        return False

def get_torch_version():
    """获取PyTorch版本"""
    try:
        import torch
        return torch.__version__
    except ImportError:
        return None

def install_torch_geometric_dependencies():
    """安装PyTorch Geometric依赖"""
    print("=" * 60)
    print("TSISP PyTorch Geometric依赖安装")
    print("=" * 60)
    
    # 检查PyTorch
    torch_version = get_torch_version()
    if torch_version is None:
        print("❌ PyTorch未安装，请先安装PyTorch")
        print("访问: https://pytorch.org/get-started/locally/")
        return False
    
    print(f"✓ PyTorch版本: {torch_version}")
    
    # 检查CUDA
    try:
        import torch
        cuda_available = torch.cuda.is_available()
        if cuda_available:
            cuda_version = torch.version.cuda
            print(f"✓ CUDA可用，版本: {cuda_version}")
            device_type = "cuda"
        else:
            print("ℹ️ CUDA不可用，使用CPU版本")
            device_type = "cpu"
    except:
        device_type = "cpu"
    
    # PyTorch Geometric相关包
    packages_to_install = [
        ("torch_geometric", "torch-geometric"),
        ("torch_scatter", "torch-scatter"),
        ("torch_sparse", "torch-sparse"),
        ("torch_cluster", "torch-cluster"),
        ("torch_spline_conv", "torch-spline-conv")
    ]
    
    # 检查已安装的包
    print(f"\n检查已安装的包:")
    installed_packages = []
    missing_packages = []
    
    for package_name, pip_name in packages_to_install:
        if check_package(package_name):
            print(f"✓ {package_name} 已安装")
            installed_packages.append(package_name)
        else:
            print(f"✗ {package_name} 未安装")
            missing_packages.append((package_name, pip_name))
    
    if not missing_packages:
        print(f"\n🎉 所有PyTorch Geometric依赖都已安装！")
        return True
    
    # 安装缺失的包
    print(f"\n开始安装缺失的包...")
    
    # 构建安装URL（针对特定PyTorch版本）
    torch_version_short = torch_version.split('+')[0]  # 移除+cu118等后缀
    
    if device_type == "cuda":
        # 对于CUDA版本，使用特定的wheel URL
        wheel_url = f"https://data.pyg.org/whl/torch-{torch_version_short}+cu118.html"
    else:
        # 对于CPU版本
        wheel_url = f"https://data.pyg.org/whl/torch-{torch_version_short}+cpu.html"
    
    print(f"使用wheel URL: {wheel_url}")
    
    success_count = 0
    for package_name, pip_name in missing_packages:
        try:
            print(f"\n正在安装 {pip_name}...")
            cmd = [sys.executable, "-m", "pip", "install", pip_name, "-f", wheel_url]
            subprocess.check_call(cmd)
            print(f"✓ {pip_name} 安装成功")
            success_count += 1
        except subprocess.CalledProcessError as e:
            print(f"✗ {pip_name} 安装失败: {e}")
            print(f"尝试使用标准pip安装...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", pip_name])
                print(f"✓ {pip_name} 标准安装成功")
                success_count += 1
            except subprocess.CalledProcessError:
                print(f"✗ {pip_name} 标准安装也失败")
    
    print(f"\n安装结果: {success_count}/{len(missing_packages)} 个包安装成功")
    
    return success_count == len(missing_packages)

def verify_installation():
    """验证安装"""
    print(f"\n" + "=" * 60)
    print("验证安装")
    print("=" * 60)
    
    try:
        # 测试基本导入
        import torch_geometric
        print(f"✓ torch_geometric 版本: {torch_geometric.__version__}")
        
        # 测试图构建函数
        try:
            from torch_geometric.utils import knn_graph, radius_graph
            print(f"✓ 从torch_geometric.utils导入图构建函数成功")
            source = "torch_geometric.utils"
        except ImportError:
            try:
                from torch_geometric.nn import knn_graph, radius_graph
                print(f"✓ 从torch_geometric.nn导入图构建函数成功")
                source = "torch_geometric.nn"
            except ImportError:
                try:
                    from torch_cluster import knn_graph, radius_graph
                    print(f"✓ 从torch_cluster导入图构建函数成功")
                    source = "torch_cluster"
                except ImportError:
                    print(f"✗ 无法导入图构建函数")
                    return False
        
        # 测试函数功能
        import torch
        test_coords = torch.randn(10, 3)
        
        try:
            edge_index = knn_graph(test_coords, k=3)
            print(f"✓ knn_graph测试成功: {edge_index.shape}")
        except Exception as e:
            print(f"✗ knn_graph测试失败: {e}")
            return False
        
        try:
            edge_index = radius_graph(test_coords, r=1.0)
            print(f"✓ radius_graph测试成功: {edge_index.shape}")
        except Exception as e:
            print(f"✗ radius_graph测试失败: {e}")
            return False
        
        print(f"\n🎉 PyTorch Geometric安装验证成功！")
        print(f"图构建函数来源: {source}")
        return True
        
    except ImportError as e:
        print(f"✗ PyTorch Geometric导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 验证过程出错: {e}")
        return False

def test_tsisp_integration():
    """测试TSISP集成"""
    print(f"\n" + "=" * 60)
    print("测试TSISP集成")
    print("=" * 60)
    
    try:
        # 测试TSISP导入
        from train_tsisp import TORCH_GEOMETRIC_AVAILABLE, KNN_GRAPH_AVAILABLE, RADIUS_GRAPH_AVAILABLE
        
        print(f"TSISP状态:")
        print(f"  TORCH_GEOMETRIC_AVAILABLE: {TORCH_GEOMETRIC_AVAILABLE}")
        print(f"  KNN_GRAPH_AVAILABLE: {KNN_GRAPH_AVAILABLE}")
        print(f"  RADIUS_GRAPH_AVAILABLE: {RADIUS_GRAPH_AVAILABLE}")
        
        if TORCH_GEOMETRIC_AVAILABLE and KNN_GRAPH_AVAILABLE and RADIUS_GRAPH_AVAILABLE:
            print(f"✓ TSISP将使用PyTorch Geometric实现")
        else:
            print(f"ℹ️ TSISP将使用fallback实现")
        
        # 测试图构建
        from train_tsisp import SpatialGraphBuilder
        
        graph_builder = SpatialGraphBuilder(k_neighbors=3, radius=1.0)
        coords = torch.randn(10, 3)
        features = torch.randn(10, 64)
        
        edge_index, edge_attr = graph_builder.build_spatial_graph(coords, features)
        print(f"✓ TSISP图构建测试成功")
        print(f"  edge_index: {edge_index.shape}")
        print(f"  edge_attr: {edge_attr.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ TSISP集成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("TSISP依赖安装工具")
    print("自动安装PyTorch Geometric相关依赖\n")
    
    # 1. 安装依赖
    install_success = install_torch_geometric_dependencies()
    
    # 2. 验证安装
    verify_success = False
    if install_success:
        verify_success = verify_installation()
    
    # 3. 测试TSISP集成
    tsisp_success = False
    if verify_success:
        tsisp_success = test_tsisp_integration()
    
    # 4. 总结
    print(f"\n" + "=" * 60)
    print("安装结果总结")
    print("=" * 60)
    
    if install_success:
        print("✓ 依赖安装成功")
    else:
        print("✗ 依赖安装失败")
    
    if verify_success:
        print("✓ 安装验证成功")
    else:
        print("✗ 安装验证失败")
    
    if tsisp_success:
        print("✓ TSISP集成测试成功")
    else:
        print("✗ TSISP集成测试失败")
    
    overall_success = install_success and verify_success and tsisp_success
    
    if overall_success:
        print(f"\n🎉 所有依赖安装和验证成功！")
        print("现在可以运行TSISP模型，不会出现PyTorch Geometric警告")
        print("\n使用方法:")
        print("python train_tsisp.py --metal Pb --output test_output")
    else:
        print(f"\n❌ 安装过程中遇到问题")
        print("TSISP仍可使用fallback实现正常运行")
        print("如需PyTorch Geometric功能，请手动安装相关依赖")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
