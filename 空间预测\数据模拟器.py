import pandas as pd
import numpy as np

# 生成模拟数据
sites = [
    {"site_name": "chemical_1", "industry": "chemical", "emission": 120, "area": 5000},
    {"site_name": "metal_1", "industry": "metal_processing", "emission": 350, "area": 20000},
    {"site_name": "pharma_1", "industry": "pharmaceutical", "emission": 80, "area": 8000},
]

points_per_site = 500
data = []

for site in sites:
    base_x = np.random.uniform(0, 100)  # 场地基位置
    base_y = np.random.uniform(0, 100)
    for i in range(points_per_site):
        # 在场地内随机生成采样点
        x = base_x + np.random.normal(0, 10)
        y = base_y + np.random.normal(0, 10)

        # 点级属性（模拟）
        pH = np.random.normal(6.5, 0.5)
        organic = np.random.normal(1.5, 0.3)

        # 重金属浓度（模拟不同场地的污染特征）
        if site["industry"] == "chemical":
            pb = np.random.lognormal(mean=3.5, sigma=0.4)
            cd = np.random.lognormal(mean=-0.5, sigma=0.3)
            as_ = np.random.lognormal(mean=2.0, sigma=0.3)
            cr = np.random.lognormal(mean=2.8, sigma=0.3)
            hg = np.random.lognormal(mean=-1.0, sigma=0.2)
        elif site["industry"] == "metal_processing":
            pb = np.random.lognormal(mean=4.5, sigma=0.5)
            cd = np.random.lognormal(mean=0.8, sigma=0.4)
            as_ = np.random.lognormal(mean=3.0, sigma=0.4)
            cr = np.random.lognormal(mean=3.5, sigma=0.4)
            hg = np.random.lognormal(mean=0.2, sigma=0.2)
        else:  # pharmaceutical
            pb = np.random.lognormal(mean=2.5, sigma=0.3)
            cd = np.random.lognormal(mean=-0.8, sigma=0.2)
            as_ = np.random.lognormal(mean=1.5, sigma=0.2)
            cr = np.random.lognormal(mean=2.0, sigma=0.3)
            hg = np.random.lognormal(mean=-1.5, sigma=0.1)

        data.append({
            "site_name": site["site_name"],
            "x": x,
            "y": y,
            "industry": site["industry"],
            "emission": site["emission"],
            "area": site["area"],
            "pH": pH,
            "organic": organic,
            "concentration_Pb": pb,
            "concentration_Cd": cd,
            "concentration_As": as_,
            "concentration_Cr": cr,
            "concentration_Hg": hg
        })

df = pd.DataFrame(data)
df.to_csv("soil_heavy_metal_data.csv", index=False)
