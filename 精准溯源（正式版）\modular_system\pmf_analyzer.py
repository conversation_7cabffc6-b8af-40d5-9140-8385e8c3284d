"""
PMF源解析模块

该模块包含：
1. 污染源指纹构建
2. 采样点指纹构建
3. PMF源解析（基于NMF）
4. 指纹相似度计算
5. 源成分谱分析


"""

import numpy as np
import pandas as pd
from typing import Tuple, Dict, List, Optional
from sklearn.decomposition import NMF
from sklearn.metrics.pairwise import cosine_similarity
from scipy.spatial.distance import cdist
import warnings

from config import Config, METALS, SOURCE_TYPES, get_config, get_model_config

warnings.filterwarnings('ignore')

# 风向配置 - 基于2.5.py
WIND_CONFIG = {
    'dominant_directions': {
        'SE': 0.25,  # 东南风（主导风向）
        'S': 0.20,   # 南风
        'E': 0.15,   # 东风
        'SW': 0.12,  # 西南风
        'NE': 0.10,  # 东北风
        'W': 0.08,   # 西风
        'N': 0.06,   # 北风
        'NW': 0.04   # 西北风
    },
    'seasonal_variation': {
        'spring': {'SE': 0.30, 'S': 0.25, 'E': 0.20, 'SW': 0.15, 'NE': 0.05, 'W': 0.03, 'N': 0.01, 'NW': 0.01},
        'summer': {'SE': 0.35, 'S': 0.30, 'E': 0.15, 'SW': 0.10, 'NE': 0.05, 'W': 0.03, 'N': 0.01, 'NW': 0.01},
        'autumn': {'SE': 0.20, 'S': 0.15, 'E': 0.15, 'SW': 0.15, 'NE': 0.15, 'W': 0.10, 'N': 0.05, 'NW': 0.05},
        'winter': {'SE': 0.15, 'S': 0.10, 'E': 0.10, 'SW': 0.10, 'NE': 0.20, 'W': 0.15, 'N': 0.10, 'NW': 0.10}
    }
}

def get_wind_direction_category(angle_degrees):
    """根据角度确定风向类别"""
    # 标准化角度到0-360度
    angle = angle_degrees % 360

    if 337.5 <= angle or angle < 22.5:
        return 'N'
    elif 22.5 <= angle < 67.5:
        return 'NE'
    elif 67.5 <= angle < 112.5:
        return 'E'
    elif 112.5 <= angle < 157.5:
        return 'SE'
    elif 157.5 <= angle < 202.5:
        return 'S'
    elif 202.5 <= angle < 247.5:
        return 'SW'
    elif 247.5 <= angle < 292.5:
        return 'W'
    else:  # 292.5 <= angle < 337.5
        return 'NW'

def calculate_wind_influence_factor(source_coords, receptor_coords, source_type='atmosphere', season='annual'):
    """
    计算风向对污染传输的影响因子 - 基于2.5.py

    Args:
        source_coords: 污染源坐标 [lon, lat]
        receptor_coords: 受体坐标 [lon, lat]
        source_type: 污染源类型
        season: 季节 ('spring', 'summer', 'autumn', 'winter', 'annual')

    Returns:
        float: 风向影响因子
    """
    # 计算从污染源到受体的方向角
    dx = receptor_coords[0] - source_coords[0]
    dy = receptor_coords[1] - source_coords[1]
    direction_angle = np.degrees(np.arctan2(dy, dx))

    # 确定风向类别
    wind_direction = get_wind_direction_category(direction_angle)

    # 获取风向频率权重
    if season == 'annual':
        wind_freq = WIND_CONFIG['dominant_directions'].get(wind_direction, 0.03)
    else:
        wind_freq = WIND_CONFIG['seasonal_variation'][season].get(wind_direction, 0.03)

    # 计算距离衰减因子
    distance = np.sqrt((source_coords[0] - receptor_coords[0])**2 +
                      (source_coords[1] - receptor_coords[1])**2)
    distance_factor = np.exp(-distance * 10)  # 距离衰减

    # 风向影响因子计算
    # 主导风向（SE, S）：增强传输效果
    # 其他风向：根据频率和地理位置调整
    if wind_direction in ['SE', 'S']:
        base_factor = 1.5 + wind_freq  # 增强因子
    elif wind_direction in ['E', 'SW']:
        base_factor = 1.0 + wind_freq * 0.5  # 中等影响
    elif wind_direction in ['NE', 'W']:
        base_factor = 0.8 + wind_freq * 0.3  # 较小影响
    else:  # N, NW
        base_factor = 0.5 + wind_freq * 0.2  # 最小影响

    # 结合距离因子
    wind_factor = base_factor * distance_factor

    # 限制在合理范围内
    return np.clip(wind_factor, 0.1, 2.0)

class PMFAnalyzer:
    """PMF源解析分析器"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        初始化PMF分析器
        
        Args:
            config: 配置对象，如果为None则使用默认配置
        """
        self.config = config or get_config()
        self.nmf_config = get_model_config('nmf')
        
        # PMF模型
        self.nmf_model = None
        self.source_profiles = None  # H矩阵：源成分谱
        self.source_contributions = None  # W矩阵：源贡献
        
        # 指纹数据
        self.source_fingerprints = None
        self.receptor_fingerprints = None
        self.similarity_matrix = None
        
    def build_source_fingerprints(self, sources: pd.DataFrame, 
                                 pollutants: List[str]) -> np.ndarray:
        """
        构建标准化污染源指纹
        
        Args:
            sources: 污染源数据
            pollutants: 污染物列表
            
        Returns:
            np.ndarray: 标准化的污染源指纹矩阵
        """
        try:
            # 确保污染物数据是数值类型
            source_data = sources[pollutants].apply(pd.to_numeric, errors='coerce')
            
            # 处理NaN值
            if source_data.isna().sum().sum() > 0:
                source_data = source_data.fillna(source_data.median())
            
            # 对数变换处理浓度值
            source_data = np.log1p(source_data)
            
            # 异常值处理
            q_low = np.percentile(source_data, 10, axis=0)
            q_high = np.percentile(source_data, 90, axis=0)
            clipped_data = source_data.copy()
            for i, col in enumerate(clipped_data.columns):
                clipped_data[col] = clipped_data[col].clip(lower=q_low[i], upper=q_high[i])
            
            # 转换为NumPy数组
            clipped_data_arr = clipped_data.values
            
            # 按比例标准化
            row_sums = clipped_data_arr.sum(axis=1).reshape(-1, 1)
            source_fingerprints = np.divide(clipped_data_arr, row_sums,
                                          out=np.zeros_like(clipped_data_arr),
                                          where=row_sums != 0)
            
            self.source_fingerprints = source_fingerprints
            print(f"构建污染源指纹完成: {source_fingerprints.shape}")
            
            return source_fingerprints
            
        except Exception as e:
            raise Exception(f"构建污染源指纹失败: {str(e)}")
    
    def build_receptor_fingerprints(self, samples: pd.DataFrame, 
                                   pollutants: List[str]) -> np.ndarray:
        """
        构建标准化采样点指纹
        
        Args:
            samples: 采样点数据
            pollutants: 污染物列表
            
        Returns:
            np.ndarray: 标准化的采样点指纹矩阵
        """
        try:
            # 确保污染物数据是数值类型
            sample_data = samples[pollutants].apply(pd.to_numeric, errors='coerce')
            
            # 处理NaN值
            if sample_data.isna().sum().sum() > 0:
                sample_data = sample_data.fillna(sample_data.median())
            
            # 对数变换处理浓度值
            sample_data = np.log1p(sample_data)
            
            # 异常值处理
            q_low = np.percentile(sample_data, 10, axis=0)
            q_high = np.percentile(sample_data, 90, axis=0)
            clipped_data = sample_data.copy()
            for i, col in enumerate(clipped_data.columns):
                clipped_data[col] = clipped_data[col].clip(lower=q_low[i], upper=q_high[i])
            
            # 转换为NumPy数组
            clipped_data_arr = clipped_data.values
            
            # 按比例标准化
            row_sums = clipped_data_arr.sum(axis=1).reshape(-1, 1)
            receptor_fingerprints = np.divide(clipped_data_arr, row_sums,
                                            out=np.zeros_like(clipped_data_arr),
                                            where=row_sums != 0)
            
            self.receptor_fingerprints = receptor_fingerprints
            print(f"构建采样点指纹完成: {receptor_fingerprints.shape}")
            
            return receptor_fingerprints
            
        except Exception as e:
            raise Exception(f"构建采样点指纹失败: {str(e)}")
    
    def calculate_fingerprint_similarity(self, source_fp: np.ndarray, 
                                       receptor_fp: np.ndarray) -> np.ndarray:
        """
        计算污染源与采样点的指纹相似度
        
        Args:
            source_fp: 污染源指纹矩阵
            receptor_fp: 采样点指纹矩阵
            
        Returns:
            np.ndarray: 相似度矩阵
        """
        try:
            # 计算余弦相似度
            cos_sim = cosine_similarity(receptor_fp, source_fp)
            
            # 计算欧氏距离相似度
            euclidean_dist = cdist(receptor_fp, source_fp, 'euclidean')
            max_dist = np.max(euclidean_dist)
            euclidean_sim = 1 - (euclidean_dist / max_dist)
            
            # 组合相似度
            combined_sim = 0.7 * cos_sim + 0.3 * euclidean_sim
            
            self.similarity_matrix = combined_sim
            print(f"指纹相似度计算完成: {combined_sim.shape}")
            
            return combined_sim
            
        except Exception as e:
            raise Exception(f"指纹相似度计算失败: {str(e)}")

    def apply_wind_correction_to_contribution(self, contribution_matrix: np.ndarray,
                                            samples: pd.DataFrame, sources: pd.DataFrame) -> np.ndarray:
        """
        对贡献率矩阵应用风向校正 - 基于2.5.py
        注意：此方法适用于最优传输贡献矩阵，不适用于PMF因子矩阵

        Args:
            contribution_matrix: 贡献率矩阵 (n_samples, n_sources)
            samples: 样本数据
            sources: 源数据

        Returns:
            np.ndarray: 校正后的贡献率矩阵
        """
        try:
            print("应用风向影响校正...")

            # 检查矩阵维度是否匹配
            if contribution_matrix.shape[1] != len(sources):
                print(f"警告: 贡献矩阵维度 {contribution_matrix.shape} 与源数据维度 {len(sources)} 不匹配")
                print("跳过风向校正（此方法仅适用于最优传输贡献矩阵）")
                return contribution_matrix

            corrected_matrix = contribution_matrix.copy()

            # 获取大气污染源的索引
            atmosphere_indices = []
            for i, source_type in enumerate(sources['source_type']):
                if source_type == 'atmosphere':
                    atmosphere_indices.append(i)

            if not atmosphere_indices:
                print("未发现大气污染源，跳过风向校正")
                return corrected_matrix

            # 对每个样本-大气源对应用风向校正
            for i, sample in samples.iterrows():
                sample_coords = [sample['lon'], sample['lat']]

                for j in atmosphere_indices:
                    source_coords = [sources.iloc[j]['lon'], sources.iloc[j]['lat']]

                    # 计算风向影响因子
                    wind_factor = calculate_wind_influence_factor(
                        source_coords, sample_coords, 'atmosphere'
                    )

                    # 应用风向校正
                    corrected_matrix[i, j] *= wind_factor

            # 重新归一化确保每行和为1
            row_sums = corrected_matrix.sum(axis=1)
            row_sums[row_sums == 0] = 1
            corrected_matrix = corrected_matrix / row_sums[:, np.newaxis]

            print(f"已对{len(atmosphere_indices)}个大气污染源应用风向校正")
            return corrected_matrix

        except Exception as e:
            print(f"风向校正失败: {str(e)}")
            return contribution_matrix

    def run_pmf_analysis(self, samples: pd.DataFrame,
                        pollutants: List[str],
                        n_factors: Optional[int] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        运行PMF源解析分析
        
        Args:
            samples: 采样点数据
            pollutants: 污染物列表
            n_factors: 因子数量，如果为None则使用配置中的值
            
        Returns:
            Tuple[np.ndarray, np.ndarray]: (源贡献矩阵W, 源成分谱矩阵H)
        """
        try:
            if n_factors is None:
                n_factors = self.nmf_config.get('n_components', 4)
            
            # 准备数据矩阵
            X = samples[pollutants].values
            
            # 确保数据为正值
            X = np.maximum(X, 1e-10)
            
            # 创建NMF模型
            self.nmf_model = NMF(
                n_components=n_factors,
                init=self.nmf_config.get('init', 'nndsvd'),
                max_iter=self.nmf_config.get('max_iter', 1000),
                random_state=self.nmf_config.get('random_state', 42)
            )
            
            # 拟合模型
            W = self.nmf_model.fit_transform(X)  # 源贡献矩阵
            H = self.nmf_model.components_       # 源成分谱矩阵
            
            # 标准化结果
            W_normalized = W / W.sum(axis=1)[:, np.newaxis]
            H_normalized = H / H.sum(axis=1)[:, np.newaxis]
            
            self.source_contributions = W_normalized
            self.source_profiles = H_normalized
            
            # 计算重构误差
            X_reconstructed = np.dot(W, H)
            reconstruction_error = np.mean((X - X_reconstructed) ** 2) / np.mean(X ** 2)
            
            print(f"PMF源解析完成:")
            print(f"  因子数量: {n_factors}")
            print(f"  源贡献矩阵形状: {W_normalized.shape}")
            print(f"  源成分谱形状: {H_normalized.shape}")
            print(f"  重构误差: {reconstruction_error:.4f}")
            
            return W_normalized, H_normalized
            
        except Exception as e:
            raise Exception(f"PMF源解析失败: {str(e)}")
    
    def interpret_source_profiles(self, pollutants: List[str]) -> Dict[int, str]:
        """
        基于算法计算解释源成分谱，识别污染源类型

        使用聚类算法和统计分析来客观识别源类型，而不依赖预定义标签

        Args:
            pollutants: 污染物列表

        Returns:
            Dict[int, str]: 源编号到源类型的映射
        """
        if self.source_profiles is None:
            raise ValueError("请先运行PMF分析")

        from sklearn.cluster import KMeans
        from sklearn.preprocessing import StandardScaler

        interpretations = {}

        # 标准化源成分谱
        scaler = StandardScaler()
        normalized_profiles = scaler.fit_transform(self.source_profiles)

        # 使用K-means聚类识别源类型模式
        n_clusters = min(4, len(self.source_profiles))  # 最多4种源类型
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(normalized_profiles)

        # 分析每个聚类的特征
        cluster_characteristics = {}
        for cluster_id in range(n_clusters):
            cluster_mask = cluster_labels == cluster_id
            cluster_profiles = self.source_profiles[cluster_mask]

            if len(cluster_profiles) > 0:
                # 计算聚类中心的特征
                cluster_center = np.mean(cluster_profiles, axis=0)

                # 找到特征污染物（高于平均值1.5倍标准差的污染物）
                mean_val = np.mean(cluster_center)
                std_val = np.std(cluster_center)
                threshold = mean_val + 1.5 * std_val

                characteristic_indices = np.where(cluster_center > threshold)[0]
                characteristic_metals = [pollutants[idx] for idx in characteristic_indices]

                # 计算污染物浓度的变异系数
                cv = np.std(cluster_center) / np.mean(cluster_center) if np.mean(cluster_center) > 0 else 0

                # 计算最大值与平均值的比值
                max_ratio = np.max(cluster_center) / np.mean(cluster_center) if np.mean(cluster_center) > 0 else 0

                cluster_characteristics[cluster_id] = {
                    'characteristic_metals': characteristic_metals,
                    'variability': cv,
                    'concentration_ratio': max_ratio,
                    'profile_center': cluster_center
                }

        # 为每个源分配类型
        for i, profile in enumerate(self.source_profiles):
            cluster_id = cluster_labels[i]
            char = cluster_characteristics[cluster_id]

            # 基于算法特征命名源类型
            if char['variability'] < 0.3:  # 低变异性 - 可能是自然源
                if len(char['characteristic_metals']) <= 2:
                    source_type = f"自然源-{cluster_id+1} (低变异性)"
                else:
                    source_type = f"混合自然源-{cluster_id+1}"
            elif char['concentration_ratio'] > 3.0:  # 高浓度比 - 可能是点源
                if len(char['characteristic_metals']) >= 1:
                    main_metals = char['characteristic_metals'][:2]
                    source_type = f"点源-{cluster_id+1} ({','.join(main_metals)}特征)"
                else:
                    source_type = f"点源-{cluster_id+1}"
            else:  # 中等变异性 - 可能是面源
                if len(char['characteristic_metals']) >= 2:
                    main_metals = char['characteristic_metals'][:2]
                    source_type = f"面源-{cluster_id+1} ({','.join(main_metals)}特征)"
                else:
                    source_type = f"面源-{cluster_id+1}"

            interpretations[i+1] = source_type

        return interpretations
    
    def get_source_contributions_dataframe(self, samples: pd.DataFrame) -> pd.DataFrame:
        """
        获取源贡献的DataFrame格式
        
        Args:
            samples: 采样点数据
            
        Returns:
            pd.DataFrame: 包含源贡献的数据框
        """
        if self.source_contributions is None:
            raise ValueError("请先运行PMF分析")
        
        # 创建基础数据框
        result_df = samples[['sample', 'lon', 'lat']].copy()
        
        # 添加源贡献列
        n_factors = self.source_contributions.shape[1]
        for i in range(n_factors):
            result_df[f'source_{i+1}_contribution'] = self.source_contributions[:, i]
        
        return result_df
    
    def calculate_source_statistics(self, pollutants: List[str]) -> Dict[str, any]:
        """
        计算源解析统计信息
        
        Args:
            pollutants: 污染物列表
            
        Returns:
            Dict: 统计信息
        """
        if self.source_profiles is None or self.source_contributions is None:
            raise ValueError("请先运行PMF分析")
        
        stats = {
            'n_factors': self.source_profiles.shape[0],
            'n_samples': self.source_contributions.shape[0],
            'n_pollutants': len(pollutants),
            'source_profiles': {},
            'contribution_stats': {}
        }
        
        # 源成分谱统计
        for i, profile in enumerate(self.source_profiles):
            stats['source_profiles'][f'source_{i+1}'] = {
                'dominant_pollutant': pollutants[np.argmax(profile)],
                'profile_values': dict(zip(pollutants, profile)),
                'concentration_sum': np.sum(profile)
            }
        
        # 贡献统计
        for i in range(self.source_contributions.shape[1]):
            contributions = self.source_contributions[:, i]
            stats['contribution_stats'][f'source_{i+1}'] = {
                'mean_contribution': np.mean(contributions),
                'std_contribution': np.std(contributions),
                'max_contribution': np.max(contributions),
                'min_contribution': np.min(contributions)
            }
        
        return stats
    
    def export_results(self, output_dir: str, samples: pd.DataFrame, 
                      pollutants: List[str]) -> None:
        """
        导出PMF分析结果
        
        Args:
            output_dir: 输出目录
            samples: 采样点数据
            pollutants: 污染物列表
        """
        import os
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 导出源贡献
        if self.source_contributions is not None:
            contrib_df = self.get_source_contributions_dataframe(samples)
            contrib_df.to_csv(os.path.join(output_dir, 'source_contributions.csv'), 
                             index=False, encoding='utf-8')
        
        # 导出源成分谱
        if self.source_profiles is not None:
            profiles_df = pd.DataFrame(
                self.source_profiles.T,
                columns=[f'Source_{i+1}' for i in range(self.source_profiles.shape[0])],
                index=pollutants
            )
            profiles_df.to_csv(os.path.join(output_dir, 'source_profiles.csv'), 
                              encoding='utf-8')
        
        # 导出相似度矩阵
        if self.similarity_matrix is not None:
            similarity_df = pd.DataFrame(self.similarity_matrix)
            similarity_df.to_csv(os.path.join(output_dir, 'similarity_matrix.csv'), 
                                index=False, encoding='utf-8')
        
        # 导出统计信息
        stats = self.calculate_source_statistics(pollutants)
        import json
        with open(os.path.join(output_dir, 'pmf_statistics.json'), 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
        
        print(f"PMF分析结果已导出到: {output_dir}")
