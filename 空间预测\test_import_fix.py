#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速测试导入修复是否有效

这个脚本专门测试PyTorch Geometric导入问题的修复
"""

import sys
import traceback

def test_imports():
    """测试所有关键导入"""
    print("=" * 50)
    print("测试TSISP导入修复")
    print("=" * 50)
    
    # 1. 测试基础导入
    try:
        import torch
        import numpy as np
        import pandas as pd
        print("✓ 基础包导入成功")
    except ImportError as e:
        print(f"✗ 基础包导入失败: {e}")
        return False
    
    # 2. 测试train_tsisp模块导入
    try:
        print("正在导入train_tsisp模块...")
        from train_tsisp import Config, TORCH_GEOMETRIC_AVAILABLE
        print(f"✓ train_tsisp模块导入成功")
        print(f"PyTorch Geometric可用性: {TORCH_GEOMETRIC_AVAILABLE}")
    except ImportError as e:
        print(f"✗ train_tsisp模块导入失败: {e}")
        traceback.print_exc()
        return False
    
    # 3. 测试图构建功能
    try:
        from train_tsisp import SpatialGraphBuilder, build_knn_graph_fallback, build_radius_graph_fallback
        print("✓ 图构建类导入成功")
        
        # 测试fallback函数
        coords = torch.randn(5, 3)
        edge_index_knn = build_knn_graph_fallback(coords, k=2)
        edge_index_radius = build_radius_graph_fallback(coords, radius=1.0)
        print(f"✓ Fallback图构建测试成功 (KNN边数: {edge_index_knn.shape[1]}, 半径图边数: {edge_index_radius.shape[1]})")
        
    except Exception as e:
        print(f"✗ 图构建功能测试失败: {e}")
        traceback.print_exc()
        return False
    
    # 4. 测试GNN模块
    try:
        from train_tsisp import GeologicalGNN, GraphAttentionLayer
        print("✓ GNN模块导入成功")
        
        # 创建测试实例
        gnn = GeologicalGNN(input_dim=32, hidden_dim=64, num_layers=2)
        print("✓ GNN实例创建成功")
        
    except Exception as e:
        print(f"✗ GNN模块测试失败: {e}")
        traceback.print_exc()
        return False
    
    # 5. 测试完整的GeoSpatial-GNN模型
    try:
        from train_tsisp import GeoSpatialGNN
        
        # 创建模型实例
        model = GeoSpatialGNN(num_industries=5)
        print("✓ GeoSpatialGNN模型创建成功")
        
    except Exception as e:
        print(f"✗ GeoSpatialGNN模型测试失败: {e}")
        traceback.print_exc()
        return False
    
    # 6. 测试配置设置
    try:
        Config.architecture = 'geospatial_gnn'
        Config.set_target_metal('Pb')
        print("✓ 配置设置成功")
    except Exception as e:
        print(f"✗ 配置设置失败: {e}")
        return False
    
    return True

def test_training_compatibility():
    """测试训练兼容性"""
    print("\n" + "=" * 50)
    print("测试训练兼容性")
    print("=" * 50)
    
    try:
        from train_tsisp import Config, train_model
        
        # 设置测试配置
        Config.architecture = 'geospatial_gnn'
        Config.epochs = 1  # 只训练1个epoch用于测试
        Config.set_target_metal('Pb')
        
        print("✓ 训练函数导入成功")
        print("注意: 实际训练需要数据文件")
        
        return True
        
    except Exception as e:
        print(f"✗ 训练兼容性测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("TSISP导入修复验证脚本")
    print("此脚本验证PyTorch Geometric导入问题是否已解决\n")
    
    # 测试导入
    import_success = test_imports()
    
    if import_success:
        print("\n🎉 所有导入测试通过！")
        
        # 测试训练兼容性
        training_success = test_training_compatibility()
        
        if training_success:
            print("\n✅ 修复验证成功！")
            print("\n现在可以运行:")
            print("python train_tsisp.py --metal Pb --output test_output")
            print("\n或者运行完整的环境测试:")
            print("python install_and_test.py")
            return True
        else:
            print("\n⚠️ 导入成功但训练兼容性有问题")
            return False
    else:
        print("\n❌ 导入测试失败")
        print("\n可能的解决方案:")
        print("1. 安装PyTorch Geometric: pip install torch_geometric")
        print("2. 检查PyTorch版本兼容性")
        print("3. 运行完整诊断: python install_and_test.py")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
