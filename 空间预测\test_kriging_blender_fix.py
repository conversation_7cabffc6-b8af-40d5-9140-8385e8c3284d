#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
KrigingBlender维度修复验证脚本

专门测试KrigingBlender中uncertainty_fusion层的维度不匹配修复
验证错误: RuntimeError: mat1 and mat2 shapes cannot be multiplied (500x257 and 258x256)
"""

import torch
import numpy as np
import logging
import traceback
import sys

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_kriging_blender():
    """测试增强的KrigingBlender"""
    print("=" * 60)
    print("测试EnhancedKrigingBlender维度修复")
    print("=" * 60)
    
    try:
        from train_tsisp import EnhancedKrigingBlender
        
        # 测试不同的输入维度
        test_cases = [
            {'input_dim': 256, 'batch_size': 16, 'name': '标准测试'},
            {'input_dim': 257, 'batch_size': 500, 'name': '问题场景(500x257)'},
            {'input_dim': 128, 'batch_size': 32, 'name': '小维度测试'},
            {'input_dim': 512, 'batch_size': 8, 'name': '大维度测试'}
        ]
        
        for i, case in enumerate(test_cases):
            print(f"\n测试案例 {i+1}: {case['name']}")
            print(f"  input_dim: {case['input_dim']}")
            print(f"  batch_size: {case['batch_size']}")
            
            try:
                # 创建KrigingBlender
                blender = EnhancedKrigingBlender(case['input_dim'])
                
                # 创建测试数据
                batch_size = case['batch_size']
                input_dim = case['input_dim']
                
                model_features = torch.randn(batch_size, input_dim)
                kriging_results = torch.randn(batch_size, 1)
                uncertainty = torch.randn(batch_size, 1)
                
                print(f"  测试数据形状:")
                print(f"    model_features: {model_features.shape}")
                print(f"    kriging_results: {kriging_results.shape}")
                print(f"    uncertainty: {uncertainty.shape}")
                
                # 测试无不确定性的情况
                print(f"  测试基础融合（无不确定性）...")
                try:
                    result_basic = blender(model_features, kriging_results)
                    print(f"    ✓ 基础融合成功: {result_basic.shape}")
                except Exception as e:
                    print(f"    ✗ 基础融合失败: {e}")
                    return False
                
                # 测试有不确定性的情况
                print(f"  测试不确定性感知融合...")
                try:
                    result_uncertainty = blender(model_features, kriging_results, uncertainty)
                    print(f"    ✓ 不确定性融合成功: {result_uncertainty.shape}")
                except Exception as e:
                    print(f"    ✗ 不确定性融合失败: {e}")
                    return False
                
                # 验证输出形状
                expected_shape = (batch_size, 1)
                if result_basic.shape == expected_shape and result_uncertainty.shape == expected_shape:
                    print(f"    ✓ 输出形状正确: {expected_shape}")
                else:
                    print(f"    ✗ 输出形状错误")
                    print(f"      基础融合: {result_basic.shape}, 期望: {expected_shape}")
                    print(f"      不确定性融合: {result_uncertainty.shape}, 期望: {expected_shape}")
                    return False
                
            except Exception as e:
                print(f"  ✗ 测试案例失败: {e}")
                traceback.print_exc()
                return False
        
        print(f"\n✅ 所有EnhancedKrigingBlender测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ EnhancedKrigingBlender测试失败: {e}")
        traceback.print_exc()
        return False

def test_geospatial_gnn_integration():
    """测试GeoSpatialGNN集成"""
    print("\n" + "=" * 60)
    print("测试GeoSpatialGNN集成")
    print("=" * 60)
    
    try:
        from train_tsisp import Config, GeoSpatialGNN
        
        # 设置配置
        Config.architecture = 'geospatial_gnn'
        Config.learnable_encoding = True
        Config.enable_uncertainty = True
        
        # 创建模型
        num_industries = 5
        model = GeoSpatialGNN(num_industries)
        
        print(f"✓ GeoSpatialGNN模型创建成功")
        
        # 测试问题批次大小（500）
        batch_size = 500
        
        print(f"测试问题批次大小: {batch_size}")
        
        test_batch = {
            'local_coordinates': torch.randn(batch_size, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size, 1)).float(),
                torch.randn(batch_size, len(Config.site_attributes) - 1)
            ], dim=1),
            'point_attributes': torch.randn(batch_size, len(Config.point_attributes)),
            'concentrations': torch.randn(batch_size, 1)
        }
        
        all_points = {
            'local_coordinates': torch.randn(batch_size, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size, 1)).float(),
                torch.randn(batch_size, len(Config.site_attributes) - 1)
            ], dim=1),
            'point_attributes': torch.randn(batch_size, len(Config.point_attributes))
        }
        
        print(f"测试数据形状:")
        for key, value in test_batch.items():
            print(f"  {key}: {value.shape}")
        
        # 测试前向传播
        model.eval()
        with torch.no_grad():
            try:
                final_pred, model_pred, kriging_pred, uncertainty, physics_loss = model(
                    test_batch, all_points, training=False
                )
                
                print(f"✓ 前向传播成功")
                print(f"  final_pred: {final_pred.shape}")
                print(f"  model_pred: {model_pred.shape}")
                print(f"  kriging_pred: {kriging_pred.shape}")
                print(f"  uncertainty: {uncertainty.shape}")
                print(f"  physics_loss: {physics_loss.item():.6f}")
                
                # 验证输出形状
                expected_shape = (batch_size, 1)
                if final_pred.shape == expected_shape:
                    print(f"  ✓ 输出形状正确: {expected_shape}")
                    return True
                else:
                    print(f"  ✗ 输出形状错误: {final_pred.shape}, 期望: {expected_shape}")
                    return False
                
            except RuntimeError as e:
                if "mat1 and mat2 shapes cannot be multiplied" in str(e):
                    print(f"✗ 仍然存在维度不匹配错误: {e}")
                    return False
                else:
                    print(f"✗ 其他运行时错误: {e}")
                    return False
        
    except Exception as e:
        print(f"✗ GeoSpatialGNN集成测试失败: {e}")
        traceback.print_exc()
        return False

def test_training_mode():
    """测试训练模式"""
    print("\n" + "=" * 60)
    print("测试训练模式")
    print("=" * 60)
    
    try:
        from train_tsisp import Config, GeoSpatialGNN
        
        Config.architecture = 'geospatial_gnn'
        Config.learnable_encoding = True
        Config.enable_uncertainty = True
        
        num_industries = 5
        model = GeoSpatialGNN(num_industries)
        
        # 使用较小的批次大小进行训练测试
        batch_size = 16
        
        test_batch = {
            'local_coordinates': torch.randn(batch_size, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size, 1)).float(),
                torch.randn(batch_size, len(Config.site_attributes) - 1)
            ], dim=1),
            'point_attributes': torch.randn(batch_size, len(Config.point_attributes)),
            'concentrations': torch.randn(batch_size, 1)
        }
        
        all_points = {
            'local_coordinates': torch.randn(batch_size * 2, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size * 2, 1)).float(),
                torch.randn(batch_size * 2, len(Config.site_attributes) - 1)
            ], dim=1),
            'point_attributes': torch.randn(batch_size * 2, len(Config.point_attributes))
        }
        
        # 测试训练模式
        model.train()
        try:
            final_pred, model_pred, kriging_pred, uncertainty, physics_loss = model(
                test_batch, all_points, training=True
            )
            
            print(f"✓ 训练模式前向传播成功")
            
            # 测试反向传播
            loss = final_pred.mean() + physics_loss
            loss.backward()
            print(f"✓ 反向传播成功")
            
            return True
            
        except Exception as e:
            print(f"✗ 训练模式测试失败: {e}")
            return False
        
    except Exception as e:
        print(f"训练模式测试失败: {e}")
        return False

def analyze_dimension_flow():
    """分析维度流动"""
    print("\n" + "=" * 60)
    print("分析维度流动")
    print("=" * 60)
    
    try:
        from train_tsisp import Config
        
        # 显示配置
        print(f"当前配置:")
        print(f"  encoding_dim: {Config.encoding_dim}")
        print(f"  embedding_dim: {Config.embedding_dim}")
        print(f"  gnn_hidden_dim: {Config.gnn_hidden_dim}")
        print(f"  site_attributes: {Config.site_attributes}")
        print(f"  point_attributes: {Config.point_attributes}")
        
        # 计算特征维度
        spatial_dim = Config.encoding_dim
        site_attr_dim = Config.embedding_dim + (len(Config.site_attributes) - 1)
        point_attr_dim = len(Config.point_attributes)
        total_feature_dim = spatial_dim + site_attr_dim + point_attr_dim
        
        print(f"\n特征维度计算:")
        print(f"  spatial_dim: {spatial_dim}")
        print(f"  site_attr_dim: {site_attr_dim}")
        print(f"  point_attr_dim: {point_attr_dim}")
        print(f"  total_feature_dim: {total_feature_dim}")
        print(f"  gnn_hidden_dim: {Config.gnn_hidden_dim}")
        
        # KrigingBlender输入分析
        print(f"\nKrigingBlender输入分析:")
        model_features_dim = Config.gnn_hidden_dim  # 来自GNN的输出
        kriging_results_dim = 1
        uncertainty_dim = 1
        
        print(f"  model_features_dim: {model_features_dim}")
        print(f"  kriging_results_dim: {kriging_results_dim}")
        print(f"  uncertainty_dim: {uncertainty_dim}")
        
        basic_input_dim = model_features_dim + kriging_results_dim
        uncertainty_input_dim = model_features_dim + kriging_results_dim + uncertainty_dim
        
        print(f"  basic_input_dim: {basic_input_dim}")
        print(f"  uncertainty_input_dim: {uncertainty_input_dim}")
        
        return True
        
    except Exception as e:
        print(f"维度分析失败: {e}")
        return False

def main():
    """主函数"""
    print("TSISP KrigingBlender维度修复验证工具")
    print("验证 RuntimeError: mat1 and mat2 shapes cannot be multiplied 的修复\n")
    
    # 1. 分析维度流动
    dimension_ok = analyze_dimension_flow()
    
    # 2. 测试EnhancedKrigingBlender
    blender_ok = test_enhanced_kriging_blender()
    
    # 3. 测试GeoSpatialGNN集成
    integration_ok = False
    if blender_ok:
        integration_ok = test_geospatial_gnn_integration()
    
    # 4. 测试训练模式
    training_ok = False
    if integration_ok:
        training_ok = test_training_mode()
    
    # 5. 总结
    print("\n" + "=" * 60)
    print("验证结果总结")
    print("=" * 60)
    
    if dimension_ok:
        print("✓ 维度分析正常")
    else:
        print("✗ 维度分析有问题")
    
    if blender_ok:
        print("✓ KrigingBlender修复成功")
    else:
        print("✗ KrigingBlender修复失败")
    
    if integration_ok:
        print("✓ GeoSpatialGNN集成正常")
    else:
        print("✗ GeoSpatialGNN集成有问题")
    
    if training_ok:
        print("✓ 训练模式正常")
    else:
        print("✗ 训练模式有问题")
    
    success = dimension_ok and blender_ok and integration_ok and training_ok
    
    if success:
        print("\n🎉 KrigingBlender维度修复验证成功！")
        print("RuntimeError: mat1 and mat2 shapes cannot be multiplied 错误已解决")
        print("\n现在可以正常运行:")
        print("python train_tsisp.py --metal Pb --output test_output")
    else:
        print("\n❌ 仍有问题需要解决")
        print("请检查上述错误信息")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
