#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
维度不匹配调试脚本

专门用于诊断和修复 RuntimeError: mat1 and mat2 shapes cannot be multiplied (500x257 and 258x256)
"""

import torch
import numpy as np
import logging
import traceback

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_dimension_calculation():
    """分析维度计算"""
    print("=" * 60)
    print("分析GeoSpatialGNN维度计算")
    print("=" * 60)
    
    try:
        from train_tsisp import Config
        
        # 显示当前配置
        print(f"当前配置:")
        print(f"  encoding_dim: {Config.encoding_dim}")
        print(f"  embedding_dim: {Config.embedding_dim}")
        print(f"  site_attributes: {Config.site_attributes}")
        print(f"  point_attributes: {Config.point_attributes}")
        print(f"  learnable_encoding: {Config.learnable_encoding}")
        
        # 计算各部分维度
        if Config.learnable_encoding:
            spatial_dim = Config.encoding_dim
        else:
            spatial_dim = 3 + 6 * Config.fourier_bands
        
        site_attr_dim = Config.embedding_dim + (len(Config.site_attributes) - 1)
        point_attr_dim = len(Config.point_attributes)
        
        total_feature_dim = spatial_dim + site_attr_dim + point_attr_dim
        
        print(f"\n计算的维度:")
        print(f"  spatial_dim: {spatial_dim}")
        print(f"  site_attr_dim: {site_attr_dim} = {Config.embedding_dim} + {len(Config.site_attributes) - 1}")
        print(f"  point_attr_dim: {point_attr_dim}")
        print(f"  total_feature_dim: {total_feature_dim}")
        
        # 错误分析
        expected_input = 258  # 从错误信息得出
        actual_input = 257    # 从错误信息得出
        difference = expected_input - actual_input
        
        print(f"\n错误分析:")
        print(f"  期望输入维度: {expected_input}")
        print(f"  实际输入维度: {actual_input}")
        print(f"  差异: {difference}")
        
        if total_feature_dim == expected_input:
            print("✓ 理论计算与期望维度匹配")
        elif total_feature_dim == actual_input:
            print("! 理论计算与实际维度匹配，说明某个组件输出维度不正确")
        else:
            print(f"✗ 理论计算({total_feature_dim})与实际维度都不匹配")
        
        return {
            'spatial_dim': spatial_dim,
            'site_attr_dim': site_attr_dim,
            'point_attr_dim': point_attr_dim,
            'total_feature_dim': total_feature_dim,
            'expected_input': expected_input,
            'actual_input': actual_input
        }
        
    except Exception as e:
        print(f"维度分析失败: {e}")
        traceback.print_exc()
        return None

def test_spatial_encoder_output():
    """测试空间编码器输出维度"""
    print("\n" + "=" * 60)
    print("测试空间编码器输出维度")
    print("=" * 60)
    
    try:
        from train_tsisp import Config, LearnableMultiScaleSpatialEncoder
        
        # 创建空间编码器
        encoder = LearnableMultiScaleSpatialEncoder(
            input_dim=3,
            encoding_dim=Config.encoding_dim,
            num_scales=Config.multi_scale_levels
        )
        
        # 测试不同批次大小
        batch_sizes = [1, 16, 500]
        
        for batch_size in batch_sizes:
            coords = torch.randn(batch_size, 3)
            output = encoder(coords)
            
            print(f"批次大小 {batch_size}:")
            print(f"  输入形状: {coords.shape}")
            print(f"  输出形状: {output.shape}")
            print(f"  期望输出维度: {Config.encoding_dim}")
            
            if output.shape[1] == Config.encoding_dim:
                print("  ✓ 输出维度正确")
            else:
                print(f"  ✗ 输出维度错误，期望{Config.encoding_dim}，实际{output.shape[1]}")
                return False
        
        return True
        
    except Exception as e:
        print(f"空间编码器测试失败: {e}")
        traceback.print_exc()
        return False

def test_feature_encoding_pipeline():
    """测试特征编码管道"""
    print("\n" + "=" * 60)
    print("测试特征编码管道")
    print("=" * 60)
    
    try:
        from train_tsisp import Config, GeoSpatialGNN
        
        # 设置配置
        Config.architecture = 'geospatial_gnn'
        Config.learnable_encoding = True
        
        # 创建模型
        num_industries = 5
        model = GeoSpatialGNN(num_industries)
        
        # 创建测试数据
        batch_size = 16
        
        test_batch = {
            'local_coordinates': torch.randn(batch_size, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size, 1)).float(),
                torch.randn(batch_size, len(Config.site_attributes) - 1)
            ], dim=1),
            'point_attributes': torch.randn(batch_size, len(Config.point_attributes))
        }
        
        print(f"测试数据形状:")
        for key, value in test_batch.items():
            print(f"  {key}: {value.shape}")
        
        # 逐步测试特征编码
        coords = test_batch['local_coordinates']
        site_attrs = test_batch['site_attributes']
        point_attrs = test_batch['point_attributes']
        
        print(f"\n逐步特征编码:")
        
        # 1. 空间编码
        spatial_enc = model.spatial_encoder(coords)
        print(f"  1. 空间编码: {coords.shape} -> {spatial_enc.shape}")
        
        # 2. 场地属性编码
        industry_ids = site_attrs[:, 0].long()
        industry_enc = model.industry_embedding(industry_ids)
        other_site_attrs = site_attrs[:, 1:]
        site_enc = torch.cat([industry_enc, other_site_attrs], dim=1)
        print(f"  2. 场地编码: industry({industry_enc.shape}) + other({other_site_attrs.shape}) = {site_enc.shape}")
        
        # 3. 点属性编码
        point_enc = point_attrs
        print(f"  3. 点属性编码: {point_enc.shape}")
        
        # 4. 特征拼接
        raw_features = torch.cat([spatial_enc, site_enc, point_enc], dim=1)
        print(f"  4. 特征拼接: {spatial_enc.shape[1]} + {site_enc.shape[1]} + {point_enc.shape[1]} = {raw_features.shape}")
        
        # 5. 检查与期望维度的匹配
        expected_dim = model.feature_fusion[0].in_features
        actual_dim = raw_features.shape[1]
        
        print(f"\n维度匹配检查:")
        print(f"  期望输入维度: {expected_dim}")
        print(f"  实际输入维度: {actual_dim}")
        print(f"  差异: {expected_dim - actual_dim}")
        
        if actual_dim == expected_dim:
            print("  ✓ 维度匹配正确")
            
            # 测试特征融合
            try:
                fused_features = model.feature_fusion(raw_features)
                print(f"  ✓ 特征融合成功: {raw_features.shape} -> {fused_features.shape}")
                return True
            except Exception as e:
                print(f"  ✗ 特征融合失败: {e}")
                return False
        else:
            print(f"  ✗ 维度不匹配")
            
            # 分析哪个组件的维度有问题
            print(f"\n详细分析:")
            print(f"  空间编码维度: {spatial_enc.shape[1]} (期望: {Config.encoding_dim})")
            print(f"  场地编码维度: {site_enc.shape[1]} (期望: {Config.embedding_dim + len(Config.site_attributes) - 1})")
            print(f"  点属性维度: {point_enc.shape[1]} (期望: {len(Config.point_attributes)})")
            
            return False
        
    except Exception as e:
        print(f"特征编码管道测试失败: {e}")
        traceback.print_exc()
        return False

def suggest_fixes(dimension_info):
    """建议修复方案"""
    print("\n" + "=" * 60)
    print("修复建议")
    print("=" * 60)
    
    if dimension_info is None:
        print("无法提供修复建议，维度分析失败")
        return
    
    expected = dimension_info['expected_input']
    actual = dimension_info['actual_input']
    difference = expected - actual
    
    print(f"维度差异: {difference}")
    
    if difference == 1:
        print("\n可能的原因和修复方案:")
        print("1. 场地属性数量计算错误")
        print("   - 检查 site_attributes 列表长度")
        print("   - 验证 industry embedding 是否正确处理")
        
        print("\n2. 点属性数量不匹配")
        print("   - 检查 point_attributes 列表长度")
        print("   - 验证实际数据中的点属性列数")
        
        print("\n3. 空间编码器输出维度不正确")
        print("   - 检查 LearnableMultiScaleSpatialEncoder 的实际输出")
        print("   - 验证 encoding_dim 配置")
        
        print("\n推荐修复步骤:")
        print("1. 运行 test_spatial_encoder_output() 检查空间编码器")
        print("2. 运行 test_feature_encoding_pipeline() 逐步检查每个组件")
        print("3. 根据测试结果调整相应的配置参数")
        
    elif difference > 1:
        print(f"\n较大的维度差异({difference})可能表明:")
        print("1. 配置参数设置错误")
        print("2. 多个组件的维度都有问题")
        print("3. 数据预处理阶段的问题")
        
    else:
        print(f"\n负的维度差异({difference})表明:")
        print("1. 实际输入维度大于期望")
        print("2. 可能有额外的特征被意外添加")

def main():
    """主函数"""
    print("TSISP维度不匹配调试工具")
    print("诊断 RuntimeError: mat1 and mat2 shapes cannot be multiplied")
    
    # 1. 分析维度计算
    dimension_info = analyze_dimension_calculation()
    
    # 2. 测试空间编码器
    spatial_encoder_ok = test_spatial_encoder_output()
    
    # 3. 测试特征编码管道
    pipeline_ok = test_feature_encoding_pipeline()
    
    # 4. 提供修复建议
    suggest_fixes(dimension_info)
    
    # 5. 总结
    print("\n" + "=" * 60)
    print("调试结果总结")
    print("=" * 60)
    
    if spatial_encoder_ok:
        print("✓ 空间编码器输出维度正确")
    else:
        print("✗ 空间编码器输出维度有问题")
    
    if pipeline_ok:
        print("✓ 特征编码管道正常")
    else:
        print("✗ 特征编码管道有问题")
    
    success = spatial_encoder_ok and pipeline_ok
    
    if success:
        print("\n🎉 维度问题已解决！")
    else:
        print("\n❌ 仍有维度问题需要修复")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
