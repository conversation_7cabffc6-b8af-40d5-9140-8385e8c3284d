#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
TSISP模型预测模块完整版
功能：
1. 从CSV加载场地数据
2. 基于训练好的模型预测污染物浓度
3. 生成点预测和网格预测
4. 创建ArcGIS风格的浓度分布图
5. 导出GeoTIFF和CSV格式结果
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import os
import matplotlib.pyplot as plt
from scipy.spatial import cKDTree
from scipy.interpolate import griddata
import gdal
import osr
import math


# ====================== 模型全局配置 ======================
class Config:
    # 傅里叶编码参数
    fourier_bands = 16
    fourier_max_freq = 10.0
    embedding_dim = 32

    # Transformer参数
    transformer_dim = 256
    transformer_depth = 8
    transformer_heads = 8
    mlp_dim = 512
    dropout = 0.1

    # 目标金属
    target_metals = ['Pb', 'Cd', 'As', 'Cr', 'Hg']
    point_attributes = ['pH', 'organic']
    site_attributes = ['industry', 'emission', 'area']

    # 预测参数
    grid_resolution = 0.5  # 网格分辨率 (单位: 米)
    min_known_points = 2  # 需要的最少已知点数量


# ====================== TSISP模型架构 ======================
# 注意: 此模型定义必须与训练时完全一致
class FourierEncoder(nn.Module):
    """傅里叶位置编码器"""

    def __init__(self, bands=16, max_freq=10.0):
        super().__init__()
        self.bands = bands
        self.max_freq = max_freq

    def forward(self, xy):
        device = xy.device
        freq_bands = torch.linspace(1.0, self.max_freq, self.bands, device=device).unsqueeze(0)
        xy = xy.unsqueeze(-1)
        freqs = xy * freq_bands
        sin = torch.sin(2 * np.pi * freqs)
        cos = torch.cos(2 * np.pi * freqs)
        return torch.cat([xy.squeeze(-1), sin.flatten(start_dim=1), cos.flatten(start_dim=1)], dim=-1)


class DiffusionModule(nn.Module):
    """污染物扩散建模模块"""

    def __init__(self, input_dim):
        super().__init__()
        self.gradient_net = nn.Sequential(
            nn.Linear(input_dim, input_dim * 2),
            nn.ReLU(),
            nn.Linear(input_dim * 2, input_dim)
        )

    def forward(self, context, targets):
        similarities = torch.matmul(targets, context.t())
        weights = F.softmax(similarities, dim=-1)
        context_agg = torch.matmul(weights, context)
        combined = targets + context_agg
        gradients = self.gradient_net(combined)
        return combined + gradients


class KrigingBlender(nn.Module):
    """克里金融合模块"""

    def __init__(self, input_dim, num_metals):
        super().__init__()
        self.fusion_net = nn.Sequential(
            nn.Linear(input_dim * 2 + num_metals, input_dim * 2),
            nn.ReLU(),
            nn.Linear(input_dim * 2, num_metals)
        )

    def forward(self, model_features, kriging_results):
        combined = torch.cat([model_features, kriging_results], dim=-1)
        return self.fusion_net(combined)


class TSISPModel(nn.Module):
    """完整的TSISP模型"""

    def __init__(self, num_industries, num_metals):
        super().__init__()
        self.num_metals = num_metals
        self.fourier_encoder = FourierEncoder(Config.fourier_bands, Config.fourier_max_freq)
        fourier_dim = 2 + 4 * Config.fourier_bands
        site_attr_dim = Config.embedding_dim + (len(Config.site_attributes) - 1)
        point_attr_dim = len(Config.point_attributes)
        total_feature_dim = fourier_dim + site_attr_dim + point_attr_dim

        # 特征融合
        self.feature_fusion = nn.Sequential(
            nn.Linear(total_feature_dim, Config.transformer_dim),
            nn.ReLU(),
            nn.Dropout(Config.dropout)
        )

        # 场地属性编码
        self.industry_embedding = nn.Embedding(num_industries + 1, Config.embedding_dim)

        # Transformer
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=Config.transformer_dim,
            nhead=Config.transformer_heads,
            dim_feedforward=Config.mlp_dim,
            dropout=Config.dropout
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, Config.transformer_depth)

        # 扩散建模
        self.diffusion_model = DiffusionModule(Config.transformer_dim)

        # 多金属预测头
        self.metal_heads = nn.ModuleList([
            nn.Sequential(
                nn.Linear(Config.transformer_dim, 128),
                nn.ReLU(),
                nn.Linear(128, 1)
            ) for _ in range(num_metals)
        ])

        # 克里金融合
        self.kriging_blender = KrigingBlender(Config.transformer_dim, num_metals)

    def forward(self, known_batch, all_points):
        # 编码已知点特征
        known_features = self._encode_features(known_batch)

        # 空间依赖关系
        context = known_features.unsqueeze(0)
        context = self.transformer(context).squeeze(0)

        # 编码所有点特征
        all_features = self._encode_features(all_points)

        # 扩散建模
        diffused_features = self.diffusion_model(context, all_features)

        # 初步模型预测
        model_preds = torch.cat(
            [head(diffused_features).squeeze(-1) for head in self.metal_heads],
            dim=-1
        )

        # 克里金插值
        kriging_preds = self._calculate_kriging(known_batch, all_points)

        # 最终预测融合
        final_pred = self.kriging_blender(diffused_features, kriging_preds)

        return final_pred, model_preds, kriging_preds

    def _encode_features(self, batch):
        """编码输入特征"""
        coords = batch['local_coordinates']
        site_attrs = batch['site_attributes']
        point_attrs = batch['point_attributes']

        # 场地属性处理
        industry_ids = site_attrs[:, 0].long()
        industry_enc = self.industry_embedding(industry_ids)
        other_site_attrs = site_attrs[:, 1:]
        site_enc = torch.cat([industry_enc, other_site_attrs], dim=1)

        # 特征编码
        fourier_enc = self.fourier_encoder(coords)
        raw_features = torch.cat([fourier_enc, site_enc, point_attrs], dim=1)
        fused_features = self.feature_fusion(raw_features)
        return fused_features

    def _calculate_kriging(self, known_batch, all_points):
        """计算克里金插值"""
        known_coords = known_batch['local_coordinates'].cpu().numpy()
        known_concs = known_batch['concentrations'].cpu().numpy()
        pred_coords = all_points['local_coordinates'].cpu().numpy()
        n_points = pred_coords.shape[0]

        kriging_preds = np.zeros((n_points, self.num_metals))

        for i in range(self.num_metals):
            if known_concs[:, i].var() < 1e-6:
                kriging_preds[:, i] = known_concs[:, i].mean()
                continue

            dist = np.sqrt(((known_coords[:, None] - pred_coords[None]) ** 2).sum(axis=2))
            min_dist = dist.min(axis=0)
            mask = min_dist > 1e-6

            weights = 1 / np.maximum(dist, 1e-6)
            weights = weights / weights.sum(axis=0)[None, :]
            kriging_preds[:, i] = np.dot(known_concs[:, i], weights)

            if (~mask).any():
                nearest_indices = np.argmin(dist[:, ~mask], axis=0)
                kriging_preds[~mask, i] = known_concs[nearest_indices, i]

        return torch.tensor(kriging_preds, dtype=torch.float32).to(known_batch['local_coordinates'].device)


# ====================== TSISP预测器 ======================
class TSISPPredictor:
    def __init__(self, model_dir, device=None):
        """初始化预测器"""
        self.device = device or torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"初始化TSISP预测器，使用设备: {self.device}")

        # 加载预处理参数
        scaler_path = os.path.join(model_dir, 'data_scalers.pth')
        if not os.path.exists(scaler_path):
            raise FileNotFoundError(f"预处理参数文件 {scaler_path} 不存在")

        self.scaler_data = torch.load(scaler_path, map_location=self.device)
        print(f"成功加载预处理参数，包含 {len(self.scaler_data['site_mappings'])} 个场地的映射信息")

        # 初始化模型
        num_metals = len(Config.target_metals)
        self.model = TSISPModel(self.scaler_data['num_industries'], num_metals).to(self.device)

        # 加载预训练权重
        model_path = os.path.join(model_dir, 'best_model.pth')
        if not os.path.exists(model_path):
            model_path = os.path.join(model_dir, 'final_model.pth')
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件 {model_path} 不存在")

        self.model.load_state_dict(torch.load(model_path, map_location=self.device))
        self.model.eval()
        print(f"模型加载成功: {model_path}")

    def predict_from_csv(self, csv_path, site_name, output_dir, grid_resolution=None):
        """
        从CSV文件加载场地数据并进行预测
        参数:
          csv_path: CSV文件路径
          site_name: 场地名称
          output_dir: 输出目录
          grid_resolution: 网格分辨率 (可选)
        返回:
          预测结果字典
        """
        # 验证输出目录
        os.makedirs(output_dir, exist_ok=True)
        print(f"输出目录: {output_dir}")

        # 从CSV加载数据
        print(f"加载场地数据: {csv_path}")
        try:
            site_data = pd.read_csv(csv_path)
            print(f"成功加载 {len(site_data)} 个采样点")
        except Exception as e:
            print(f"加载CSV文件失败: {str(e)}")
            return None

        # 检查是否有足够的已知点
        known_count = self._count_known_points(site_data)
        if known_count < Config.min_known_points:
            print(f"警告: 只有 {known_count} 个已知点，需要至少 {Config.min_known_points} 个")

        # 进行点预测
        point_predictions = self.predict_site(site_data, site_name)

        # 保存点预测结果
        point_csv_path = os.path.join(output_dir, f'{site_name}_point_predictions.csv')
        point_predictions.to_csv(point_csv_path, index=False)
        print(f"点预测结果保存至: {point_csv_path}")

        # 进行网格预测
        grid_res = grid_resolution or Config.grid_resolution
        grid_results = self.predict_grid(site_data, site_name, grid_res)

        # 保存网格预测结果
        self._save_grid_results(grid_results, output_dir)

        # 可视化结果
        self._visualize_results(point_predictions, grid_results, output_dir)

        # 导出ArcGIS格式
        self._export_to_arcgis(grid_results, output_dir)

        return {
            'point_predictions': point_predictions,
            'grid_results': grid_results
        }

    def predict_site(self, site_data, site_name):
        """
        预测场地内所有点的浓度
        参数:
          site_data: 场地数据DataFrame
          site_name: 场地名称
        返回:
          包含所有点预测结果的数据框
        """
        # 预处理场地数据
        processed_data = self._preprocess_site(site_data, site_name)

        # 识别已知点
        known_points = self._identify_known_points(processed_data)

        if known_points.empty:
            print(f"警告: {site_name} 没有任何已知浓度的采样点")

            # 设置默认已知点 - 使用数据集中的第一个点
            if processed_data.empty:
                raise ValueError("场地数据为空")

            processed_data.loc[0, [f'concentration_{m}' for m in Config.target_metals]] = processed_data.loc[0, 'x']
            known_points = processed_data.iloc[:1]
            print("已创建默认已知点")

        # 准备输入数据
        known_batch = self._create_known_batch(known_points)
        all_points = self._create_all_points(processed_data)

        # 进行预测
        with torch.no_grad():
            preds, _, _ = self.model(known_batch, all_points)

        # 后处理预测结果
        return self._postprocess_predictions(processed_data.copy(), preds.cpu().numpy())

    def predict_grid(self, site_data, site_name, resolution=None):
        """
        生成场地的高分辨率网格预测
        参数:
          site_data: 场地数据DataFrame
          site_name: 场地名称
          resolution: 网格分辨率 (单位: 米)
        返回:
          网格预测结果字典
        """
        # 设置网格分辨率
        grid_res = resolution or Config.grid_resolution

        # 预处理场地数据
        processed_data = self._preprocess_site(site_data, site_name)

        # 创建网格点
        grid_data = self._create_grid_points(processed_data, grid_res)

        # 识别已知点
        known_points = self._identify_known_points(processed_data)

        # 如果没有已知点，使用整个数据集
        if known_points.empty:
            known_points = processed_data

        # 准备输入数据
        known_batch = self._create_known_batch(known_points)
        all_points = self._create_all_points(grid_data)

        # 进行预测
        with torch.no_grad():
            grid_preds, _, _ = self.model(known_batch, all_points)
            grid_preds = grid_preds.cpu().numpy()

        # 组织网格结果
        min_x = processed_data['local_x'].min()
        max_x = processed_data['local_x'].max()
        min_y = processed_data['local_y'].min()
        max_y = processed_data['local_y'].max()

        # 计算X,Y网格
        x_coords = np.arange(min_x - 0.1 * (max_x - min_x), max_x + 0.1 * (max_x - min_x), grid_res)
        y_coords = np.arange(min_y - 0.1 * (max_y - min_y), max_y + 0.1 * (max_y - min_y), grid_res)
        xx, yy = np.meshgrid(x_coords, y_coords)

        # 全局坐标网格
        global_xx = xx + processed_data['global_x_min'].iloc[0]
        global_yy = yy + processed_data['global_y_min'].iloc[0]

        # 创建预测网格
        conc_shape = (len(y_coords), len(x_coords), len(Config.target_metals))
        concentrations = np.zeros(conc_shape)
        grid_points = grid_data[['local_x', 'local_y']].values

        # 将预测结果映射到网格
        for i in range(len(y_coords)):
            for j in range(len(x_coords)):
                point = np.array([x_coords[j], y_coords[i]])
                idx = np.argmin(np.sum((grid_points - point) ** 2, axis=1))
                concentrations[i, j, :] = grid_preds[idx, :]

        # 返回结果字典
        return {
            'site_name': site_name,
            'local_x_grid': xx,
            'local_y_grid': yy,
            'global_x_grid': global_xx,
            'global_y_grid': global_yy,
            'concentrations': concentrations,
            'resolution': grid_res
        }

    def _count_known_points(self, data):
        """计算已知点的数量"""
        count = 0
        for metal in Config.target_metals:
            col = f'concentration_{metal}'
            if col in data.columns:
                count = max(count, data[col].notnull().sum())
        return count

    def _preprocess_site(self, site_data, site_name):
        """预处理新场地数据"""
        # 深拷贝以避免修改原始数据
        data = site_data.copy()

        # 添加场地名称
        data['site_name'] = site_name

        # 场地ID映射
        if site_name in self.scaler_data['site_mappings']:
            site_id = self.scaler_data['site_mappings'][site_name]
        else:
            max_id = max(self.scaler_data['site_mappings'].values(), default=0) + 1
            site_id = max_id
            print(f"创建新场地ID: {site_name} -> {site_id}")

        data['site_id'] = site_id

        # 行业编码处理
        if 'industry' in data.columns:
            if data['industry'].dtype == object:  # 文本行业
                encoder = self.scaler_data['encoders']['industry']
                known_classes = set(encoder.classes_)

                def encode_industry(ind):
                    if ind in known_classes:
                        return encoder.transform([ind])[0]
                    else:
                        return len(encoder.classes_)  # 未知类别

                data['industry'] = data['industry'].apply(encode_industry)
            else:  # 数值行业
                max_val = self.scaler_data['encoders']['industry'].classes_[-1]
                data['industry'] = data['industry'].apply(lambda x: min(x, max_val))
        else:
            # 如果没有提供行业，使用最常见的行业
            common_industry = np.argmax(np.bincount(
                self.scaler_data['encoders']['industry'].transform(
                    self.scaler_data['encoders']['industry'].classes_
                )
            ))
            data['industry'] = common_industry

        # 数值属性标准化
        for attr in ['emission', 'area']:
            if attr in data.columns and attr in self.scaler_data['scalers']:
                scaler = self.scaler_data['scalers'][attr]
                if hasattr(scaler, 'transform'):
                    data[attr] = scaler.transform(data[[attr]])
            elif attr in self.scaler_data['scalers']:
                # 如果没有提供，使用平均值
                scaler = self.scaler_data['scalers'][attr]
                if hasattr(scaler, 'mean_'):
                    mean_val = scaler.mean_[0]
                    scale_val = scaler.scale_[0]
                    data[attr] = (0.0 - mean_val) / scale_val

        # 点级属性处理
        for attr in Config.point_attributes:
            if attr in data.columns and attr in self.scaler_data['scalers']:
                scaler = self.scaler_data['scalers'][attr]
                if hasattr(scaler, 'transform'):
                    data[attr] = scaler.transform(data[[attr]])
            elif attr in self.scaler_data['scalers']:
                # 如果没有提供，使用平均值
                scaler = self.scaler_data['scalers'][attr]
                if hasattr(scaler, 'mean_'):
                    mean_val = scaler.mean_[0]
                    scale_val = scaler.scale_[0]
                    data[attr] = (0.0 - mean_val) / scale_val

        # 添加全局坐标最小值 (用于恢复原始坐标)
        data['global_x_min'] = data['x'].min()
        data['global_y_min'] = data['y'].min()

        # 计算局部坐标
        data['local_x'] = data['x'] - data['global_x_min']
        data['local_y'] = data['y'] - data['global_y_min']

        # 添加浓度列
        for metal in Config.target_metals:
            col_name = f"concentration_{metal}"
            if col_name not in data.columns:
                data[col_name] = np.nan

        # 删除可能存在的额外索引列
        if 'Unnamed: 0' in data.columns:
            data = data.drop(columns=['Unnamed: 0'])

        return data

    def _identify_known_points(self, data):
        """识别有已知浓度的点"""
        known_cols = [f"concentration_{m}" for m in Config.target_metals]

        # 如果没有任何浓度列，返回空
        if not any(col in data.columns for col in known_cols):
            return pd.DataFrame()

        # 找出有任何已知浓度的点
        known_mask = data[known_cols].notnull().any(axis=1)
        return data[known_mask].copy()

    def _create_known_batch(self, known_points):
        """创建已知点批次"""
        return {
            'local_coordinates': torch.tensor(
                known_points[['local_x', 'local_y']].values,
                dtype=torch.float32, device=self.device
            ),
            'site_attributes': torch.tensor(
                known_points[Config.site_attributes].values,
                dtype=torch.float32, device=self.device
            ),
            'point_attributes': torch.tensor(
                known_points[Config.point_attributes].values,
                dtype=torch.float32, device=self.device
            ),
            'concentrations': torch.tensor(
                known_points[[f"concentration_{m}" for m in Config.target_metals]].values,
                dtype=torch.float32, device=self.device
            ),
            'site_id': torch.tensor(
                known_points['site_id'].values,
                dtype=torch.long, device=self.device
            )
        }

    def _create_all_points(self, all_data):
        """创建所有点批次"""
        return {
            'local_coordinates': torch.tensor(
                all_data[['local_x', 'local_y']].values,
                dtype=torch.float32, device=self.device
            ),
            'site_attributes': torch.tensor(
                all_data[Config.site_attributes].values,
                dtype=torch.float32, device=self.device
            ),
            'point_attributes': torch.tensor(
                all_data[Config.point_attributes].values,
                dtype=torch.float32, device=self.device
            ),
            'concentrations': torch.zeros(
                (len(all_data), len(Config.target_metals)),
                dtype=torch.float32, device=self.device
            ),
            'site_id': torch.tensor(
                all_data['site_id'].values,
                dtype=torch.long, device=self.device
            )
        }

    def _create_grid_points(self, site_data, resolution):
        """创建网格点数据框"""
        # 获取场地边界
        min_x = site_data['local_x'].min()
        max_x = site_data['local_x'].max()
        min_y = site_data['local_y'].min()
        max_y = site_data['local_y'].max()

        # 添加缓冲区 (10%)
        x_buffer = 0.1 * (max_x - min_x)
        y_buffer = 0.1 * (max_y - min_y)
        min_x -= x_buffer
        max_x += x_buffer
        min_y -= y_buffer
        max_y += y_buffer

        # 创建网格点
        x_coords = np.arange(min_x, max_x + resolution, resolution)
        y_coords = np.arange(min_y, max_y + resolution, resolution)
        xx, yy = np.meshgrid(x_coords, y_coords)
        grid_points = np.vstack([xx.ravel(), yy.ravel()]).T

        # 创建数据框
        grid_data = pd.DataFrame(grid_points, columns=['local_x', 'local_y'])
        grid_data['x'] = grid_data['local_x'] + site_data['global_x_min'].iloc[0]
        grid_data['y'] = grid_data['local_y'] + site_data['global_y_min'].iloc[0]

        # 复制场地级属性
        for attr in Config.site_attributes + ['site_name', 'site_id', 'global_x_min', 'global_y_min']:
            if attr in site_data.columns:
                grid_data[attr] = site_data[attr].iloc[0]

        # 添加点级属性 (从最近点插值)
        if site_data.shape[0] > 1 and Config.point_attributes:
            tree = cKDTree(site_data[['local_x', 'local_y']].values)
            _, indices = tree.query(grid_points, k=1)
            for attr in Config.point_attributes:
                if attr in site_data.columns:
                    grid_data[attr] = site_data[attr].iloc[indices].values

        # 添加浓度列
        for metal in Config.target_metals:
            col_name = f"concentration_{metal}"
            if col_name not in grid_data.columns:
                grid_data[col_name] = np.nan

        return grid_data

    def _postprocess_predictions(self, data, predictions):
        """后处理预测结果"""
        # 转换预测值 (从log转换)
        predictions = np.expm1(predictions)

        # 添加到数据框
        for i, metal in enumerate(Config.target_metals):
            col_name = f"predicted_{metal}"
            data[col_name] = predictions[:, i]

        # 恢复全局坐标
        if 'global_x_min' in data.columns and 'global_y_min' in data.columns:
            data['x'] = data['local_x'] + data['global_x_min']
            data['y'] = data['local_y'] + data['global_y_min']

        # 添加预测质量标志
        for metal in Config.target_metals:
            known_col = f"concentration_{metal}"
            pred_col = f"predicted_{metal}"
            if known_col in data.columns:
                data[f'{metal}_prediction_type'] = np.where(
                    data[known_col].notnull(), 'measured', 'predicted'
                )

        # 移除不必要的列
        for col in ['local_x', 'local_y', 'global_x_min', 'global_y_min']:
            if col in data.columns:
                data = data.drop(columns=[col])

        return data

    def _save_grid_results(self, grid_results, output_dir):
        """保存网格预测结果"""
        site_name = grid_results['site_name']

        # 准备网格点
        x = grid_results['global_x_grid'].ravel()
        y = grid_results['global_y_grid'].ravel()
        concentrations = grid_results['concentrations']

        # 创建数据框
        grid_points = []
        for i in range(len(x)):
            point = {
                'x': x[i],
                'y': y[i],
            }
            for j, metal in enumerate(Config.target_metals):
                point[metal] = concentrations[i // grid_results['global_x_grid'].shape[1],
                                              i % grid_results['global_x_grid'].shape[1], j]
            grid_points.append(point)

        grid_df = pd.DataFrame(grid_points)

        # 保存为CSV
        grid_csv_path = os.path.join(output_dir, f'{site_name}_grid_predictions.csv')
        grid_df.to_csv(grid_csv_path, index=False)
        print(f"网格预测结果保存至: {grid_csv_path}")

    def _visualize_results(self, point_predictions, grid_results, output_dir):
        """可视化预测结果"""
        # 点预测可视化
        self._visualize_point_predictions(point_predictions, output_dir)

        # 网格预测可视化
        self._visualize_grid_predictions(grid_results, output_dir)

    def _visualize_point_predictions(self, data, output_dir):
        """可视化点预测结果"""
        # 为每种金属创建单独图像
        for metal in Config.target_metals:
            plt.figure(figsize=(10, 8))

            # 创建子图
            ax = plt.gca()

            # 绘制所有点
            scatter = ax.scatter(
                data['x'], data['y'],
                c=data[f'predicted_{metal}'],
                s=60, cmap=self._get_metal_colormap(metal),
                alpha=0.7, vmin=0
            )

            # 标记已知点
            known_mask = data[f'concentration_{metal}'].notnull()
            if known_mask.any():
                known_points = data[known_mask]
                ax.scatter(
                    known_points['x'], known_points['y'],
                    c=known_points[f'concentration_{metal}'],
                    s=120, edgecolor='black', linewidth=1,
                    cmap=self._get_metal_colormap(metal),
                    vmin=0, marker='s'
                )

            # 添加颜色条
            cbar = plt.colorbar(scatter, ax=ax)
            cbar.set_label(f'{metal} Concentration (mg/kg)', fontsize=12)

            # 设置标题和标签
            plt.title(f'Soil {metal} Concentration - Site: {data["site_name"].iloc[0]}', fontsize=14)
            plt.xlabel('Longitude', fontsize=12)
            plt.ylabel('Latitude', fontsize=12)

            # 添加比例尺
            ax.grid(True, linestyle='--', alpha=0.5)

            # 保存图像
            save_path = os.path.join(output_dir, f'{metal}_point_heatmap.png')
            plt.savefig(save_path, bbox_inches='tight', dpi=300)
            print(f"点预测热力图保存至: {save_path}")
            plt.close()

    def _visualize_grid_predictions(self, grid_results, output_dir):
        """可视化网格预测结果"""
        for i, metal in enumerate(Config.target_metals):
            plt.figure(figsize=(12, 10))

            # 使用网格插值提高渲染质量
            x = grid_results['global_x_grid'].ravel()
            y = grid_results['global_y_grid'].ravel()
            z = grid_results['concentrations'][:, :, i].ravel()

            # 创建更高分辨率的网格用于平滑渲染
            xi = np.linspace(x.min(), x.max(), 1000)
            yi = np.linspace(y.min(), y.max(), 1000)
            xi, yi = np.meshgrid(xi, yi)

            # 插值
            zi = griddata((x, y), z, (xi, yi), method='cubic')

            # 创建等高线填充图
            cs = plt.contourf(xi, yi, zi, levels=50, cmap=self._get_metal_colormap(metal), alpha=0.8)

            # 添加等高线
            plt.contour(xi, yi, zi, levels=10, colors='k', linewidths=0.5)

            # 添加颜色条
            cbar = plt.colorbar(cs)
            cbar.set_label(f'{metal} Concentration (mg/kg)', fontsize=12)

            # 添加标题和标签
            plt.title(f'{metal} Concentration Distribution - Site: {grid_results["site_name"]}', fontsize=14)
            plt.xlabel('Longitude', fontsize=12)
            plt.ylabel('Latitude', fontsize=12)

            # 添加比例尺
            plt.gca().set_aspect('equal')
            plt.grid(True, alpha=0.3)

            # 保存图像
            save_path = os.path.join(output_dir, f'{metal}_grid_heatmap.png')
            plt.savefig(save_path, bbox_inches='tight', dpi=300)
            print(f"网格热力图保存至: {save_path}")
            plt.close()

    def _get_metal_colormap(self, metal):
        """获取金属特定的颜色映射"""
        colormaps = {
            'Pb': 'YlOrBr',  # 铅: 黄橙棕色
            'Cd': 'Greens',  # 镉: 绿色
            'As': 'Oranges',  # 砷: 橙色
            'Cr': 'Blues',  # 铬: 蓝色
            'Hg': 'Purples'  # 汞: 紫色
        }
        return colormaps.get(metal, 'viridis')

    def _export_to_arcgis(self, grid_results, output_dir):
        """将网格预测结果导出为ArcGIS兼容格式"""
        site_name = grid_results['site_name']
        concentrations = grid_results['concentrations']
        x_grid = grid_results['global_x_grid']
        y_grid = grid_results['global_y_grid']
        resolution = grid_results['resolution']

        # 为每种金属保存一个GeoTIFF
        for i, metal in enumerate(Config.target_metals):
            tif_path = os.path.join(output_dir, f'{site_name}_{metal}.tif')

            # 获取行列数
            rows, cols = x_grid.shape

            # 创建GeoTIFF文件
            driver = gdal.GetDriverByName('GTiff')
            dataset = driver.Create(
                tif_path, cols, rows, 1, gdal.GDT_Float32
            )

            # 设置地理变换
            min_x = np.min(x_grid)
            max_y = np.max(y_grid)
            transform = [
                min_x, resolution, 0,  # 左上角X, X方向分辨率, 旋转参数
                max_y, 0, -resolution  # 左上角Y, 旋转参数, Y方向分辨率
            ]
            dataset.SetGeoTransform(transform)

            # 设置坐标系统
            srs = osr.SpatialReference()
            srs.ImportFromEPSG(4326)  # WGS84坐标系统
            dataset.SetProjection(srs.ExportToWkt())

            # 写入数据
            band = dataset.GetRasterBand(1)
            band.WriteArray(concentrations[:, :, i])
            band.SetNoDataValue(-9999)
            band.FlushCache()

            dataset = None
            print(f"保存 {metal} GeoTIFF: {tif_path}")

        # 保存元数据
        meta_path = os.path.join(output_dir, f'{site_name}_metadata.txt')
        with open(meta_path, 'w') as f:
            f.write(f"场地名称: {site_name}\n")
            f.write(f"预测日期: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M')}\n")
            f.write(f"分辨率: {resolution} 度\n")
            f.write(f"坐标系: WGS84 (EPSG:4326)\n")
            f.write(f"预测金属: {', '.join(Config.target_metals)}\n")
            f.write(f"空间范围:\n")
            f.write(f"  最小经度: {np.min(x_grid):.6f}\n")
            f.write(f"  最大经度: {np.max(x_grid):.6f}\n")
            f.write(f"  最小纬度: {np.min(y_grid):.6f}\n")
            f.write(f"  最大纬度: {np.max(y_grid):.6f}\n")

        print(f"元数据保存至: {meta_path}")


# ====================== 使用示例 ======================
if __name__ == "__main__":
    import argparse

    # 设置命令行参数
    parser = argparse.ArgumentParser(description='TSISP模型预测系统')
    parser.add_argument('--model_dir', type=str, required=True, help='训练模型目录')
    parser.add_argument('--data_csv', type=str, required=True, help='输入数据CSV文件')
    parser.add_argument('--site_name', type=str, required=True, help='场地名称')
    parser.add_argument('--output_dir', type=str, required=True, help='输出目录')
    parser.add_argument('--grid_resolution', type=float, default=0.5,
                        help='网格分辨率 (单位: 米), 默认: 0.5')

    args = parser.parse_args()

    # 打印启动信息
    print("\n" + "=" * 50)
    print(f"TSISP模型预测系统启动")
    print(f"模型目录: {args.model_dir}")
    print(f"输入数据: {args.data_csv}")
    print(f"场地名称: {args.site_name}")
    print(f"输出目录: {args.output_dir}")
    print(f"网格分辨率: {args.grid_resolution} 米")
    print("=" * 50 + "\n")

    # 初始化预测器
    predictor = TSISPPredictor(model_dir=args.model_dir)

    # 执行预测
    results = predictor.predict_from_csv(
        csv_path=args.data_csv,
        site_name=args.site_name,
        output_dir=args.output_dir,
        grid_resolution=args.grid_resolution
    )

    if results:
        print("\n" + "=" * 50)
        print("预测完成! 结果已保存到输出目录")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("预测失败")
        print("=" * 50)