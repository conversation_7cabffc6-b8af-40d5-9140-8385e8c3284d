import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 创建图形
fig, ax = plt.subplots(1, 1, figsize=(14, 10))
ax.set_xlim(0, 10)
ax.set_ylim(0, 12)
ax.axis('off')

# 定义颜色
process_color = 'lightblue'
data_color = 'lightcoral'
result_color = 'lightgreen'

# 绘制流程框的函数
def draw_box(ax, x, y, width, height, text, box_type='process', text_size=10):
    if box_type == 'process':
        # 虚线框 - 处理步骤
        box = FancyBboxPatch((x-width/2, y-height/2), width, height,
                           boxstyle="round,pad=0.1", 
                           facecolor=process_color, 
                           edgecolor='blue', 
                           linestyle='--',
                           linewidth=1.5)
    elif box_type == 'data':
        # 实线红框 - 数据/结果
        box = FancyBboxPatch((x-width/2, y-height/2), width, height,
                           boxstyle="round,pad=0.1", 
                           facecolor=data_color, 
                           edgecolor='red', 
                           linestyle='-',
                           linewidth=2)
    else:  # result
        # 实线绿框 - 最终结果
        box = FancyBboxPatch((x-width/2, y-height/2), width, height,
                           boxstyle="round,pad=0.1", 
                           facecolor=result_color, 
                           edgecolor='green', 
                           linestyle='-',
                           linewidth=2)
    
    ax.add_patch(box)
    ax.text(x, y, text, ha='center', va='center', fontsize=text_size, weight='bold')

# 绘制箭头的函数
def draw_arrow(ax, x1, y1, x2, y2, color='blue'):
    ax.annotate('', xy=(x2, y2), xytext=(x1, y1),
                arrowprops=dict(arrowstyle='->', color=color, lw=1.5))

# 绘制流程图
# 第一层：数据加载
draw_box(ax, 2, 11, 1.5, 0.6, '数据加载', 'data')
draw_box(ax, 5, 11, 1.5, 0.6, '土壤数据', 'process')
draw_box(ax, 8, 11, 1.5, 0.6, '污染源数据', 'process')

# 第二层：指纹构建
draw_box(ax, 2, 9.5, 1.8, 0.6, '构建污染源指纹', 'process')
draw_box(ax, 5, 9.5, 1.8, 0.6, '构建采样点指纹', 'process')
draw_box(ax, 8, 9.5, 1.8, 0.6, '计算指纹相似度', 'process')

# 第三层：分析方法
draw_box(ax, 1, 8, 1.2, 0.6, 'PMF源解析', 'process')
draw_box(ax, 3, 8, 1.4, 0.6, '深度聚类分析', 'process')
draw_box(ax, 5.5, 8, 1.6, 0.6, '空间图神经网络', 'process')
draw_box(ax, 8, 8, 1.8, 0.6, '无监督深度学习', 'data')

# 第四层：核心模型
draw_box(ax, 3, 6.5, 1.8, 0.6, '最优传输模型', 'data')
draw_box(ax, 7, 6.5, 1.6, 0.6, '结果组合(1:9)', 'process')

# 第五层：校正
draw_box(ax, 5, 5, 2, 0.6, '随机森林空间校正', 'process')

# 第六层：输出
draw_box(ax, 2, 3.5, 1.4, 0.6, '结果整合', 'process')
draw_box(ax, 5, 3.5, 1.4, 0.6, '可视化结果', 'result')
draw_box(ax, 8, 3.5, 1.4, 0.6, '导出结果', 'result')

# 绘制连接线
# 数据加载到指纹构建
draw_arrow(ax, 2, 10.7, 2, 9.8)
draw_arrow(ax, 5, 10.7, 5, 9.8)
draw_arrow(ax, 8, 10.7, 8, 9.8)

# 指纹构建到分析方法
draw_arrow(ax, 2, 9.2, 1, 8.3)
draw_arrow(ax, 3.5, 9.2, 3, 8.3)
draw_arrow(ax, 5, 9.2, 5.5, 8.3)
draw_arrow(ax, 8, 9.2, 8, 8.3)

# 分析方法到核心模型
draw_arrow(ax, 3, 7.7, 3, 6.8)
draw_arrow(ax, 8, 7.7, 7, 6.8)

# 核心模型到校正
draw_arrow(ax, 5, 6.2, 5, 5.3)

# 校正到输出
draw_arrow(ax, 4, 4.7, 2, 3.8)
draw_arrow(ax, 5, 4.7, 5, 3.8)
draw_arrow(ax, 6, 4.7, 8, 3.8)

# 添加标题
ax.text(5, 11.8, '精准溯源污染源解析流程图', ha='center', va='center', 
        fontsize=16, weight='bold')

# 添加图例
legend_x = 0.5
legend_y = 2
ax.text(legend_x, legend_y, '图例:', fontsize=12, weight='bold')

# 绘制图例框
draw_box(ax, legend_x + 0.8, legend_y - 0.5, 0.8, 0.3, '处理步骤', 'process', 8)
draw_box(ax, legend_x + 2.2, legend_y - 0.5, 0.8, 0.3, '核心模型', 'data', 8)
draw_box(ax, legend_x + 3.6, legend_y - 0.5, 0.8, 0.3, '最终输出', 'result', 8)

plt.tight_layout()
plt.savefig('污染源解析流程图.png', dpi=300, bbox_inches='tight', facecolor='white')
plt.show()