#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
张量维度调试脚本

专门用于调试和修复TSISP GeoSpatial-GNN架构中的张量维度不匹配问题
"""

import torch
import numpy as np
import logging
import traceback

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_spatial_encoder():
    """测试空间编码器的维度"""
    print("=" * 60)
    print("测试LearnableMultiScaleSpatialEncoder")
    print("=" * 60)
    
    try:
        from train_tsisp import Config, LearnableMultiScaleSpatialEncoder
        
        # 设置测试配置
        Config.encoding_dim = 64
        Config.multi_scale_levels = 3
        
        # 创建编码器
        encoder = LearnableMultiScaleSpatialEncoder(
            input_dim=3,
            encoding_dim=Config.encoding_dim,
            num_scales=Config.multi_scale_levels
        )
        
        # 测试不同批次大小
        batch_sizes = [1, 16, 32, 100, 500]
        
        for batch_size in batch_sizes:
            print(f"\n测试批次大小: {batch_size}")
            
            # 创建测试输入
            coords = torch.randn(batch_size, 3)
            print(f"输入坐标形状: {coords.shape}")
            
            # 前向传播
            encoded = encoder(coords)
            print(f"编码输出形状: {encoded.shape}")
            print(f"期望输出形状: [{batch_size}, {Config.encoding_dim}]")
            
            # 验证维度
            assert encoded.shape == (batch_size, Config.encoding_dim), \
                f"维度不匹配: 得到 {encoded.shape}, 期望 ({batch_size}, {Config.encoding_dim})"
            
            print("✓ 空间编码器测试通过")
            
        return True
        
    except Exception as e:
        print(f"✗ 空间编码器测试失败: {e}")
        traceback.print_exc()
        return False

def test_feature_fusion():
    """测试特征融合层的维度"""
    print("\n" + "=" * 60)
    print("测试特征融合层")
    print("=" * 60)
    
    try:
        from train_tsisp import Config, GeoSpatialGNN
        
        # 设置测试配置
        Config.architecture = 'geospatial_gnn'
        Config.learnable_encoding = True
        Config.encoding_dim = 64
        Config.multi_scale_levels = 3
        Config.embedding_dim = 32
        Config.gnn_hidden_dim = 128
        Config.site_attributes = ['industry', 'emission', 'area']  # 3个属性
        Config.point_attributes = ['organic', 'depth']  # 2个属性
        
        # 创建模型
        num_industries = 5
        model = GeoSpatialGNN(num_industries)
        
        # 创建测试数据
        batch_size = 16
        
        # 模拟数据批次
        test_batch = {
            'local_coordinates': torch.randn(batch_size, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size, 1)).float(),  # industry
                torch.randn(batch_size, 2)  # emission, area
            ], dim=1),
            'point_attributes': torch.randn(batch_size, 2)  # organic, depth
        }
        
        print(f"测试数据形状:")
        for key, value in test_batch.items():
            print(f"  {key}: {value.shape}")
        
        # 测试特征编码
        encoded_features = model._encode_features(test_batch)
        print(f"编码特征形状: {encoded_features.shape}")
        print(f"期望形状: [{batch_size}, {Config.gnn_hidden_dim}]")
        
        assert encoded_features.shape == (batch_size, Config.gnn_hidden_dim), \
            f"特征编码维度不匹配: 得到 {encoded_features.shape}, 期望 ({batch_size}, {Config.gnn_hidden_dim})"
        
        print("✓ 特征融合测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 特征融合测试失败: {e}")
        traceback.print_exc()
        return False

def test_full_model():
    """测试完整模型的前向传播"""
    print("\n" + "=" * 60)
    print("测试完整模型前向传播")
    print("=" * 60)
    
    try:
        from train_tsisp import Config, GeoSpatialGNN
        
        # 设置配置
        Config.architecture = 'geospatial_gnn'
        Config.learnable_encoding = True
        Config.enable_uncertainty = True
        
        # 创建模型
        num_industries = 5
        model = GeoSpatialGNN(num_industries)
        
        # 创建测试数据
        batch_size = 16
        
        known_batch = {
            'local_coordinates': torch.randn(batch_size, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size, 1)).float(),
                torch.randn(batch_size, 2)
            ], dim=1),
            'point_attributes': torch.randn(batch_size, 2),
            'concentrations': torch.randn(batch_size, 1)
        }
        
        all_points = {
            'local_coordinates': torch.randn(batch_size * 2, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size * 2, 1)).float(),
                torch.randn(batch_size * 2, 2)
            ], dim=1),
            'point_attributes': torch.randn(batch_size * 2, 2)
        }
        
        print(f"已知点数据形状:")
        for key, value in known_batch.items():
            print(f"  {key}: {value.shape}")
            
        print(f"所有点数据形状:")
        for key, value in all_points.items():
            print(f"  {key}: {value.shape}")
        
        # 前向传播
        model.eval()
        with torch.no_grad():
            final_pred, model_pred, kriging_pred, uncertainty, physics_loss = model(
                known_batch, all_points, training=False
            )
        
        print(f"模型输出形状:")
        print(f"  final_pred: {final_pred.shape}")
        print(f"  model_pred: {model_pred.shape}")
        print(f"  kriging_pred: {kriging_pred.shape}")
        if uncertainty is not None:
            print(f"  uncertainty: {uncertainty.shape}")
        print(f"  physics_loss: {physics_loss}")
        
        print("✓ 完整模型测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 完整模型测试失败: {e}")
        traceback.print_exc()
        return False

def diagnose_dimension_mismatch():
    """诊断具体的维度不匹配问题"""
    print("\n" + "=" * 60)
    print("维度不匹配诊断")
    print("=" * 60)
    
    try:
        from train_tsisp import Config
        
        # 打印当前配置
        print("当前配置:")
        print(f"  encoding_dim: {Config.encoding_dim}")
        print(f"  multi_scale_levels: {Config.multi_scale_levels}")
        print(f"  embedding_dim: {Config.embedding_dim}")
        print(f"  gnn_hidden_dim: {Config.gnn_hidden_dim}")
        print(f"  site_attributes: {Config.site_attributes}")
        print(f"  point_attributes: {Config.point_attributes}")
        
        # 计算期望的维度
        spatial_dim = Config.encoding_dim if Config.learnable_encoding else (3 + 6 * Config.fourier_bands)
        site_attr_dim = Config.embedding_dim + (len(Config.site_attributes) - 1)
        point_attr_dim = len(Config.point_attributes)
        total_feature_dim = spatial_dim + site_attr_dim + point_attr_dim
        
        print(f"\n计算的维度:")
        print(f"  spatial_dim: {spatial_dim}")
        print(f"  site_attr_dim: {site_attr_dim}")
        print(f"  point_attr_dim: {point_attr_dim}")
        print(f"  total_feature_dim: {total_feature_dim}")
        print(f"  target gnn_hidden_dim: {Config.gnn_hidden_dim}")
        
        # 检查多尺度编码器的实际输出维度
        if Config.learnable_encoding:
            freq_dim = Config.encoding_dim // (Config.multi_scale_levels * 2 * 3)  # 3 for x,y,depth
            actual_multiscale_dim = Config.multi_scale_levels * 3 * freq_dim * 2
            
            print(f"\n多尺度编码器详细计算:")
            print(f"  freq_dim: {freq_dim}")
            print(f"  actual_multiscale_dim: {actual_multiscale_dim}")
            print(f"  geological_features_dim: 16")
            print(f"  fusion_input_dim: {actual_multiscale_dim + 16}")
            
            if actual_multiscale_dim + 16 != Config.encoding_dim:
                print(f"⚠️ 警告: 融合输入维度不匹配!")
                print(f"   期望输出: {Config.encoding_dim}")
                print(f"   实际输入: {actual_multiscale_dim + 16}")
        
        return True
        
    except Exception as e:
        print(f"✗ 维度诊断失败: {e}")
        traceback.print_exc()
        return False

def suggest_fixes():
    """建议修复方案"""
    print("\n" + "=" * 60)
    print("修复建议")
    print("=" * 60)
    
    print("1. 确保encoding_dim能被(multi_scale_levels * 2 * 3)整除")
    print("   推荐配置: encoding_dim=72, multi_scale_levels=3 (72 % (3*2*3) = 0)")
    print("   或者: encoding_dim=96, multi_scale_levels=4")
    
    print("\n2. 检查site_attributes和point_attributes的长度")
    print("   确保与实际数据列数匹配")
    
    print("\n3. 调整gnn_hidden_dim以匹配total_feature_dim")
    print("   或者在特征融合层添加适当的投影")
    
    print("\n4. 使用调试模式运行:")
    print("   import logging")
    print("   logging.getLogger('train_tsisp').setLevel(logging.DEBUG)")

def main():
    """主测试函数"""
    print("TSISP张量维度调试工具")
    print("用于诊断和修复GeoSpatial-GNN架构中的维度不匹配问题\n")
    
    # 运行所有测试
    tests = [
        ("空间编码器", test_spatial_encoder),
        ("特征融合", test_feature_fusion),
        ("完整模型", test_full_model),
        ("维度诊断", diagnose_dimension_mismatch)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if not all_passed:
        suggest_fixes()
    else:
        print("\n🎉 所有测试通过！维度匹配正常。")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
