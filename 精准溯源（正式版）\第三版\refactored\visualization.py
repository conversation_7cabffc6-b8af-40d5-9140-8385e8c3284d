"""
Publication-Quality Visualization Module

This module provides comprehensive visualization capabilities for pollution source
apportionment results, following Environmental Science & Technology journal standards.

Authors: <AUTHORS>
Version: 3.0 (Refactored)
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

from config import ConfigManager, get_config_manager
from source_apportionment import SourceApportionmentResult


class PublicationVisualizer:
    """Publication-quality visualization for source apportionment results."""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize publication visualizer.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager or get_config_manager()
        self.logger = logging.getLogger(__name__)
        
        # Setup matplotlib for publication quality
        self._setup_matplotlib()
    
    def _setup_matplotlib(self) -> None:
        """Setup matplotlib for publication-quality figures."""
        viz_config = self.config.get_viz_config()
        
        plt.rcParams.update({
            'figure.dpi': viz_config.figure_dpi,
            'savefig.dpi': viz_config.figure_dpi,
            'font.family': viz_config.font_family,
            'font.size': viz_config.font_size,
            'axes.titlesize': viz_config.title_size,
            'axes.labelsize': viz_config.label_size,
            'xtick.labelsize': viz_config.label_size,
            'ytick.labelsize': viz_config.label_size,
            'legend.fontsize': viz_config.label_size,
            'figure.figsize': viz_config.figure_size,
            'axes.grid': True,
            'grid.alpha': viz_config.grid_alpha,
            'axes.spines.top': False,
            'axes.spines.right': False,
            'axes.linewidth': viz_config.spine_width
        })
        
        if viz_config.use_latex:
            plt.rcParams['text.usetex'] = True
    
    def plot_source_profiles(self, result: SourceApportionmentResult,
                           metals: List[str],
                           output_path: Optional[str] = None) -> None:
        """
        Plot source profiles with uncertainty bounds.
        
        Args:
            result: Source apportionment results
            metals: List of metal names
            output_path: Output file path
        """
        n_sources = result.source_profiles.shape[0]
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        axes = axes.flatten()
        
        colors = self.config.get_viz_config().categorical_colors
        
        for i in range(min(n_sources, 4)):
            ax = axes[i]
            
            # Main profile
            profile = result.source_profiles[i]
            bars = ax.bar(metals, profile, color=colors[i % len(colors)], alpha=0.7)
            
            # Add uncertainty bounds if available
            if result.uncertainty_bounds:
                lower = result.uncertainty_bounds['profiles_lower'][i]
                upper = result.uncertainty_bounds['profiles_upper'][i]
                errors = [profile - lower, upper - profile]
                ax.errorbar(metals, profile, yerr=errors, fmt='none', 
                           color='black', capsize=3, alpha=0.8)
            
            # Formatting
            ax.set_title(f'Source {i+1}', fontweight='bold')
            ax.set_ylabel('Relative Contribution')
            ax.tick_params(axis='x', rotation=45)
            
            # Add interpretation if available
            if result.source_interpretations and (i+1) in result.source_interpretations:
                interpretation = result.source_interpretations[i+1]
                ax.text(0.02, 0.98, interpretation, transform=ax.transAxes,
                       verticalalignment='top', bbox=dict(boxstyle='round', 
                       facecolor='white', alpha=0.8))
        
        # Hide unused subplots
        for i in range(n_sources, 4):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        
        if output_path:
            plt.savefig(output_path, bbox_inches='tight', facecolor='white')
            self.logger.info(f"Source profiles saved to {output_path}")
        
        plt.show()
    
    def plot_contribution_matrix(self, result: SourceApportionmentResult,
                               sample_names: Optional[List[str]] = None,
                               output_path: Optional[str] = None) -> None:
        """
        Plot source contribution matrix as heatmap.
        
        Args:
            result: Source apportionment results
            sample_names: Sample names for y-axis
            output_path: Output file path
        """
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # Prepare data
        contributions = result.source_contributions
        
        # Create labels
        if sample_names is None:
            sample_names = [f'Sample {i+1}' for i in range(contributions.shape[0])]
        
        source_names = []
        for i in range(contributions.shape[1]):
            if result.source_interpretations and (i+1) in result.source_interpretations:
                source_names.append(f'S{i+1}: {result.source_interpretations[i+1]}')
            else:
                source_names.append(f'Source {i+1}')
        
        # Create heatmap
        im = ax.imshow(contributions, cmap='YlOrRd', aspect='auto')
        
        # Set ticks and labels
        ax.set_xticks(range(len(source_names)))
        ax.set_xticklabels(source_names, rotation=45, ha='right')
        
        if len(sample_names) <= 20:  # Only show y-labels if not too many
            ax.set_yticks(range(len(sample_names)))
            ax.set_yticklabels(sample_names)
        else:
            ax.set_ylabel(f'{len(sample_names)} Samples')
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Contribution', rotation=270, labelpad=20)
        
        # Add title
        ax.set_title('Source Contribution Matrix', fontweight='bold', pad=20)
        
        plt.tight_layout()
        
        if output_path:
            plt.savefig(output_path, bbox_inches='tight', facecolor='white')
            self.logger.info(f"Contribution matrix saved to {output_path}")
        
        plt.show()
    
    def plot_similarity_matrix(self, result: SourceApportionmentResult,
                             sample_names: Optional[List[str]] = None,
                             source_names: Optional[List[str]] = None,
                             output_path: Optional[str] = None) -> None:
        """
        Plot fingerprint similarity matrix.
        
        Args:
            result: Source apportionment results
            sample_names: Sample names
            source_names: Source names
            output_path: Output file path
        """
        fig, ax = plt.subplots(figsize=(10, 8))
        
        similarity = result.similarity_matrix
        
        # Create heatmap
        im = ax.imshow(similarity, cmap='viridis', aspect='auto', vmin=0, vmax=1)
        
        # Set labels
        if source_names is None:
            source_names = [f'Source {i+1}' for i in range(similarity.shape[1])]
        
        if sample_names is None:
            sample_names = [f'Sample {i+1}' for i in range(similarity.shape[0])]
        
        ax.set_xticks(range(len(source_names)))
        ax.set_xticklabels(source_names, rotation=45, ha='right')
        
        if len(sample_names) <= 20:
            ax.set_yticks(range(len(sample_names)))
            ax.set_yticklabels(sample_names)
        else:
            ax.set_ylabel(f'{len(sample_names)} Samples')
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Similarity', rotation=270, labelpad=20)
        
        # Add title
        ax.set_title('Source-Receptor Similarity Matrix', fontweight='bold', pad=20)
        
        plt.tight_layout()
        
        if output_path:
            plt.savefig(output_path, bbox_inches='tight', facecolor='white')
            self.logger.info(f"Similarity matrix saved to {output_path}")
        
        plt.show()
    
    def plot_model_diagnostics(self, result: SourceApportionmentResult,
                             receptor_data: np.ndarray,
                             metals: List[str],
                             output_path: Optional[str] = None) -> None:
        """
        Plot model diagnostic plots.
        
        Args:
            result: Source apportionment results
            receptor_data: Original receptor data
            metals: Metal names
            output_path: Output file path
        """
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
        
        # Reconstruct data
        reconstructed = result.source_contributions @ result.source_profiles
        
        # 1. Observed vs Predicted
        ax1.scatter(receptor_data.flatten(), reconstructed.flatten(), alpha=0.6)
        min_val = min(receptor_data.min(), reconstructed.min())
        max_val = max(receptor_data.max(), reconstructed.max())
        ax1.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8)
        ax1.set_xlabel('Observed')
        ax1.set_ylabel('Predicted')
        ax1.set_title('Observed vs Predicted')
        
        # Add R² to plot
        if result.model_metrics:
            r2 = result.model_metrics['r2']
            ax1.text(0.05, 0.95, f'R² = {r2:.3f}', transform=ax1.transAxes,
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        # 2. Residuals
        residuals = receptor_data - reconstructed
        ax2.scatter(reconstructed.flatten(), residuals.flatten(), alpha=0.6)
        ax2.axhline(y=0, color='r', linestyle='--', alpha=0.8)
        ax2.set_xlabel('Predicted')
        ax2.set_ylabel('Residuals')
        ax2.set_title('Residual Plot')
        
        # 3. Q-Q plot of residuals
        from scipy import stats
        stats.probplot(residuals.flatten(), dist="norm", plot=ax3)
        ax3.set_title('Q-Q Plot of Residuals')
        
        # 4. Source contribution distribution
        total_contributions = np.sum(result.source_contributions, axis=0)
        source_labels = [f'S{i+1}' for i in range(len(total_contributions))]
        
        bars = ax4.bar(source_labels, total_contributions, 
                      color=self.config.get_viz_config().categorical_colors[:len(total_contributions)])
        ax4.set_ylabel('Total Contribution')
        ax4.set_title('Source Contribution Distribution')
        
        # Add percentage labels on bars
        total = np.sum(total_contributions)
        for bar, contrib in zip(bars, total_contributions):
            height = bar.get_height()
            percentage = (contrib / total) * 100
            ax4.text(bar.get_x() + bar.get_width()/2., height,
                    f'{percentage:.1f}%', ha='center', va='bottom')
        
        plt.tight_layout()
        
        if output_path:
            plt.savefig(output_path, bbox_inches='tight', facecolor='white')
            self.logger.info(f"Model diagnostics saved to {output_path}")
        
        plt.show()
    
    def create_publication_figure(self, result: SourceApportionmentResult,
                                receptor_data: np.ndarray,
                                metals: List[str],
                                sample_names: Optional[List[str]] = None,
                                output_path: Optional[str] = None) -> None:
        """
        Create comprehensive publication-ready figure.
        
        Args:
            result: Source apportionment results
            receptor_data: Original receptor data
            metals: Metal names
            sample_names: Sample names
            output_path: Output file path
        """
        fig = plt.figure(figsize=(16, 12))
        
        # Create grid layout
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)
        
        # Source profiles (top row)
        n_sources = result.source_profiles.shape[0]
        colors = self.config.get_viz_config().categorical_colors
        
        for i in range(min(n_sources, 3)):
            ax = fig.add_subplot(gs[0, i])
            profile = result.source_profiles[i]
            
            bars = ax.bar(metals, profile, color=colors[i % len(colors)], alpha=0.7)
            
            if result.uncertainty_bounds:
                lower = result.uncertainty_bounds['profiles_lower'][i]
                upper = result.uncertainty_bounds['profiles_upper'][i]
                errors = [profile - lower, upper - profile]
                ax.errorbar(metals, profile, yerr=errors, fmt='none', 
                           color='black', capsize=3, alpha=0.8)
            
            ax.set_title(f'Source {i+1}', fontweight='bold')
            ax.set_ylabel('Relative Contribution')
            ax.tick_params(axis='x', rotation=45)
            
            if result.source_interpretations and (i+1) in result.source_interpretations:
                interpretation = result.source_interpretations[i+1]
                ax.text(0.02, 0.98, interpretation, transform=ax.transAxes,
                       verticalalignment='top', fontsize=8,
                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        # Contribution matrix (middle left)
        ax_contrib = fig.add_subplot(gs[1, :2])
        contributions = result.source_contributions
        
        im = ax_contrib.imshow(contributions, cmap='YlOrRd', aspect='auto')
        
        source_names = [f'S{i+1}' for i in range(contributions.shape[1])]
        ax_contrib.set_xticks(range(len(source_names)))
        ax_contrib.set_xticklabels(source_names)
        
        if sample_names and len(sample_names) <= 20:
            ax_contrib.set_yticks(range(len(sample_names)))
            ax_contrib.set_yticklabels(sample_names)
        else:
            ax_contrib.set_ylabel(f'{contributions.shape[0]} Samples')
        
        ax_contrib.set_title('Source Contributions', fontweight='bold')
        
        # Model fit (middle right)
        ax_fit = fig.add_subplot(gs[1, 2])
        reconstructed = result.source_contributions @ result.source_profiles
        
        ax_fit.scatter(receptor_data.flatten(), reconstructed.flatten(), alpha=0.6)
        min_val = min(receptor_data.min(), reconstructed.min())
        max_val = max(receptor_data.max(), reconstructed.max())
        ax_fit.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8)
        ax_fit.set_xlabel('Observed')
        ax_fit.set_ylabel('Predicted')
        ax_fit.set_title('Model Fit')
        
        if result.model_metrics:
            r2 = result.model_metrics['r2']
            ax_fit.text(0.05, 0.95, f'R² = {r2:.3f}', transform=ax_fit.transAxes,
                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        # Source distribution (bottom)
        ax_dist = fig.add_subplot(gs[2, :])
        total_contributions = np.sum(result.source_contributions, axis=0)
        source_labels = []
        
        for i in range(len(total_contributions)):
            if result.source_interpretations and (i+1) in result.source_interpretations:
                source_labels.append(f'S{i+1}: {result.source_interpretations[i+1]}')
            else:
                source_labels.append(f'Source {i+1}')
        
        bars = ax_dist.bar(source_labels, total_contributions, 
                          color=colors[:len(total_contributions)])
        ax_dist.set_ylabel('Total Contribution')
        ax_dist.set_title('Overall Source Distribution', fontweight='bold')
        ax_dist.tick_params(axis='x', rotation=45)
        
        # Add percentage labels
        total = np.sum(total_contributions)
        for bar, contrib in zip(bars, total_contributions):
            height = bar.get_height()
            percentage = (contrib / total) * 100
            ax_dist.text(bar.get_x() + bar.get_width()/2., height,
                        f'{percentage:.1f}%', ha='center', va='bottom')
        
        # Add main title
        fig.suptitle('Heavy Metal Pollution Source Apportionment Analysis', 
                    fontsize=16, fontweight='bold', y=0.98)
        
        if output_path:
            plt.savefig(output_path, bbox_inches='tight', facecolor='white')
            self.logger.info(f"Publication figure saved to {output_path}")
        
        plt.show()
