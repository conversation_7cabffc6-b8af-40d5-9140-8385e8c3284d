#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
NaN值检测和修复脚本

专门用于诊断和修复TSISP模型中的NaN值问题
解决ValueError: Input X contains NaN错误
"""

import pandas as pd
import numpy as np
import torch
import logging
import traceback
import sys
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def diagnose_csv_data(csv_path):
    """诊断CSV数据中的NaN值"""
    print("=" * 60)
    print("诊断CSV数据中的NaN值")
    print("=" * 60)
    
    try:
        # 读取数据
        data = pd.read_csv(csv_path)
        print(f"数据形状: {data.shape}")
        print(f"列名: {list(data.columns)}")
        
        # 检查每列的NaN值
        nan_summary = {}
        total_rows = len(data)
        
        print(f"\nNaN值统计:")
        print("-" * 40)
        
        for col in data.columns:
            nan_count = data[col].isnull().sum()
            nan_percentage = (nan_count / total_rows) * 100
            nan_summary[col] = {'count': nan_count, 'percentage': nan_percentage}
            
            if nan_count > 0:
                print(f"{col:15s}: {nan_count:6d} ({nan_percentage:5.1f}%)")
        
        # 检查关键列
        critical_columns = ['x', 'y', 'depth']
        print(f"\n关键坐标列检查:")
        print("-" * 40)
        
        for col in critical_columns:
            if col in data.columns:
                nan_count = data[col].isnull().sum()
                inf_count = np.isinf(data[col]).sum()
                print(f"{col:10s}: NaN={nan_count:4d}, Inf={inf_count:4d}")
                
                if nan_count > 0:
                    print(f"  NaN值位置: {data[data[col].isnull()].index.tolist()[:10]}...")
        
        # 检查数值范围
        print(f"\n数值范围检查:")
        print("-" * 40)
        
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if col in critical_columns:
                min_val = data[col].min()
                max_val = data[col].max()
                std_val = data[col].std()
                print(f"{col:10s}: [{min_val:8.3f}, {max_val:8.3f}], std={std_val:8.3f}")
        
        return nan_summary
        
    except Exception as e:
        print(f"CSV诊断失败: {e}")
        traceback.print_exc()
        return None

def fix_csv_data(csv_path, output_path=None):
    """修复CSV数据中的NaN值"""
    print("\n" + "=" * 60)
    print("修复CSV数据中的NaN值")
    print("=" * 60)
    
    try:
        # 读取数据
        data = pd.read_csv(csv_path)
        original_shape = data.shape
        
        print(f"原始数据形状: {original_shape}")
        
        # 修复关键坐标列
        critical_columns = ['x', 'y', 'depth']
        
        for col in critical_columns:
            if col in data.columns:
                nan_count = data[col].isnull().sum()
                if nan_count > 0:
                    print(f"修复列 '{col}' 的 {nan_count} 个NaN值...")
                    
                    if col in ['x', 'y']:
                        # 坐标用均值填充
                        mean_val = data[col].mean()
                        data[col].fillna(mean_val, inplace=True)
                        print(f"  用均值 {mean_val:.3f} 填充")
                    elif col == 'depth':
                        # 深度用中位数填充
                        median_val = data[col].median()
                        data[col].fillna(median_val, inplace=True)
                        print(f"  用中位数 {median_val:.3f} 填充")
        
        # 修复浓度列
        concentration_cols = [col for col in data.columns if col.startswith('concentration_')]
        for col in concentration_cols:
            nan_count = data[col].isnull().sum()
            if nan_count > 0:
                print(f"修复浓度列 '{col}' 的 {nan_count} 个NaN值...")
                # 浓度用0填充（表示未检出）
                data[col].fillna(0.0, inplace=True)
                print(f"  用0填充（未检出）")
        
        # 修复其他数值列
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if col not in critical_columns and not col.startswith('concentration_'):
                nan_count = data[col].isnull().sum()
                if nan_count > 0:
                    print(f"修复数值列 '{col}' 的 {nan_count} 个NaN值...")
                    
                    if col in ['organic']:
                        # 有机质用均值填充
                        mean_val = data[col].mean()
                        data[col].fillna(mean_val, inplace=True)
                        print(f"  用均值 {mean_val:.3f} 填充")
                    else:
                        # 其他列用中位数填充
                        median_val = data[col].median()
                        data[col].fillna(median_val, inplace=True)
                        print(f"  用中位数 {median_val:.3f} 填充")
        
        # 处理无穷大值
        print(f"\n处理无穷大值...")
        for col in numeric_cols:
            inf_count = np.isinf(data[col]).sum()
            if inf_count > 0:
                print(f"列 '{col}' 有 {inf_count} 个无穷大值")
                # 将无穷大值替换为合理的大值
                data[col] = data[col].replace([np.inf, -np.inf], [1e6, -1e6])
        
        # 最终验证
        print(f"\n最终验证:")
        total_nan = data.isnull().sum().sum()
        total_inf = np.isinf(data.select_dtypes(include=[np.number])).sum().sum()
        
        print(f"剩余NaN值: {total_nan}")
        print(f"剩余无穷大值: {total_inf}")
        
        if total_nan == 0 and total_inf == 0:
            print("✓ 所有NaN和无穷大值已修复")
        else:
            print("⚠️ 仍有未修复的值")
        
        # 保存修复后的数据
        if output_path is None:
            output_path = csv_path.replace('.csv', '_fixed.csv')
        
        data.to_csv(output_path, index=False)
        print(f"修复后的数据已保存到: {output_path}")
        
        return output_path
        
    except Exception as e:
        print(f"CSV修复失败: {e}")
        traceback.print_exc()
        return None

def test_preprocessing_pipeline(csv_path):
    """测试预处理管道中的NaN处理"""
    print("\n" + "=" * 60)
    print("测试预处理管道")
    print("=" * 60)
    
    try:
        from train_tsisp import Config, SoilDataPreprocessor
        
        # 设置配置
        Config.set_target_metal('Pb')
        
        # 创建预处理器
        preprocessor = SoilDataPreprocessor(csv_path)
        
        print("✓ 预处理器创建成功")
        
        # 获取预处理后的数据
        processed_data = preprocessor.get_preprocessed_data()
        
        print(f"预处理后数据形状: {processed_data.shape}")
        
        # 检查关键列的NaN值
        critical_columns = ['local_x', 'local_y', 'rel_depth', 'depth']
        target_col = Config.get_target_metal_column()
        critical_columns.append(target_col)
        
        print(f"\n预处理后NaN检查:")
        print("-" * 30)
        
        total_nan = 0
        for col in critical_columns:
            if col in processed_data.columns:
                nan_count = processed_data[col].isnull().sum()
                inf_count = np.isinf(processed_data[col]).sum()
                total_nan += nan_count
                print(f"{col:15s}: NaN={nan_count:4d}, Inf={inf_count:4d}")
        
        if total_nan == 0:
            print("✓ 预处理管道未产生NaN值")
            return True
        else:
            print(f"✗ 预处理管道产生了 {total_nan} 个NaN值")
            return False
            
    except Exception as e:
        print(f"预处理测试失败: {e}")
        traceback.print_exc()
        return False

def test_graph_construction():
    """测试图构建中的NaN处理"""
    print("\n" + "=" * 60)
    print("测试图构建NaN处理")
    print("=" * 60)
    
    try:
        from train_tsisp import build_knn_graph_fallback, build_radius_graph_fallback, validate_coordinates
        
        # 创建包含NaN的测试数据
        test_coords = torch.tensor([
            [1.0, 2.0, 3.0],
            [4.0, 5.0, 6.0],
            [float('nan'), 8.0, 9.0],  # 包含NaN
            [10.0, 11.0, 12.0],
            [13.0, float('nan'), 15.0],  # 包含NaN
            [16.0, 17.0, 18.0]
        ], dtype=torch.float32)
        
        print(f"测试坐标形状: {test_coords.shape}")
        print(f"包含NaN的行数: {torch.isnan(test_coords).any(dim=1).sum()}")
        
        # 测试坐标验证
        clean_coords, valid_mask = validate_coordinates(test_coords, "test")
        print(f"清理后坐标形状: {clean_coords.shape}")
        print(f"有效坐标数: {valid_mask.sum()}")
        
        # 测试KNN图构建
        try:
            edge_index_knn = build_knn_graph_fallback(test_coords, k=2)
            print(f"✓ KNN图构建成功，边数: {edge_index_knn.shape[1]}")
        except Exception as e:
            print(f"✗ KNN图构建失败: {e}")
        
        # 测试半径图构建
        try:
            edge_index_radius = build_radius_graph_fallback(test_coords, radius=10.0)
            print(f"✓ 半径图构建成功，边数: {edge_index_radius.shape[1]}")
        except Exception as e:
            print(f"✗ 半径图构建失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"图构建测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("TSISP NaN值诊断和修复工具")
    print("解决ValueError: Input X contains NaN错误\n")
    
    # 默认数据路径
    default_csv = "空间预测/soil_heavy_metal_data.csv"
    
    if len(sys.argv) > 1:
        csv_path = sys.argv[1]
    else:
        csv_path = default_csv
    
    if not os.path.exists(csv_path):
        print(f"数据文件不存在: {csv_path}")
        print("请提供正确的CSV文件路径")
        return False
    
    print(f"分析数据文件: {csv_path}\n")
    
    # 1. 诊断CSV数据
    nan_summary = diagnose_csv_data(csv_path)
    
    if nan_summary is None:
        return False
    
    # 检查是否需要修复
    total_nan = sum(item['count'] for item in nan_summary.values())
    
    if total_nan > 0:
        print(f"\n发现 {total_nan} 个NaN值，开始修复...")
        
        # 2. 修复CSV数据
        fixed_csv = fix_csv_data(csv_path)
        
        if fixed_csv:
            print(f"\n使用修复后的数据进行测试: {fixed_csv}")
            csv_path = fixed_csv
    else:
        print("\n✓ CSV数据中没有NaN值")
    
    # 3. 测试预处理管道
    preprocessing_ok = test_preprocessing_pipeline(csv_path)
    
    # 4. 测试图构建
    graph_construction_ok = test_graph_construction()
    
    # 总结
    print("\n" + "=" * 60)
    print("修复结果总结")
    print("=" * 60)
    
    if total_nan == 0:
        print("✓ 原始数据无NaN值")
    else:
        print(f"✓ 修复了 {total_nan} 个NaN值")
    
    if preprocessing_ok:
        print("✓ 预处理管道正常")
    else:
        print("✗ 预处理管道有问题")
    
    if graph_construction_ok:
        print("✓ 图构建NaN处理正常")
    else:
        print("✗ 图构建NaN处理有问题")
    
    success = preprocessing_ok and graph_construction_ok
    
    if success:
        print("\n🎉 NaN值问题已解决！")
        print("\n现在可以运行:")
        print(f"python train_tsisp.py --metal Pb --output test_output")
    else:
        print("\n❌ 仍有问题需要解决")
        print("请检查上述错误信息")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
