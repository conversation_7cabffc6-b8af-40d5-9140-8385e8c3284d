"""
深度学习模块

该模块包含：
1. 增强自编码器
2. 空间图神经网络(GNN)
3. 污染源贡献率预测网络
4. 蒙特卡洛Dropout网络
5. 图数据构建
6. 端到端预测模型

"""

import numpy as np
import pandas as pd
from typing import Tuple, Optional, List
import warnings

# 深度学习库
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch_geometric.data import Data
from torch_geometric.nn import GCNConv

# 空间分析
from pyproj import Proj
from sklearn.neighbors import kneighbors_graph, radius_neighbors_graph
from sklearn.preprocessing import StandardScaler
from scipy.spatial.distance import cdist

from config import Config, get_config

warnings.filterwarnings('ignore')

# 设置PyTorch线程数
torch.set_num_threads(1)

class EnhancedAutoencoder(nn.Module):
    """增强自编码器 - 带批归一化和残差连接"""
    
    def __init__(self, input_dim: int, hidden_dims: List[int] = [64, 32, 16], 
                 dropout_rate: float = 0.2):
        super().__init__()
        self.encoder = nn.ModuleList()
        self.decoder = nn.ModuleList()
        
        # 编码器 - 添加批归一化和残差连接
        dims = [input_dim] + hidden_dims
        for i in range(len(dims)-1):
            self.encoder.append(nn.Sequential(
                nn.Linear(dims[i], dims[i+1]),
                nn.BatchNorm1d(dims[i+1]),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            ))
        
        # 解码器
        dims.reverse()
        for i in range(len(dims)-1):
            self.decoder.append(nn.Sequential(
                nn.Linear(dims[i], dims[i+1]),
                nn.BatchNorm1d(dims[i+1]) if i < len(dims)-2 else nn.Identity(),
                nn.ReLU() if i < len(dims)-2 else nn.Sigmoid()
            ))
    
    def encode(self, x):
        """编码器前向传播"""
        for layer in self.encoder:
            x = layer(x)
        return x
    
    def forward(self, x):
        """完整的前向传播"""
        encoded = self.encode(x)
        for layer in self.decoder:
            encoded = layer(encoded)
        return encoded

class SpatialGNN(nn.Module):
    """空间图神经网络"""
    
    def __init__(self, input_dim: int, hidden_dim: int = 64, num_layers: int = 3):
        super().__init__()
        self.convs = nn.ModuleList()
        self.batch_norms = nn.ModuleList()
        
        # 多层GCN with 残差连接
        self.convs.append(GCNConv(input_dim, hidden_dim))
        self.batch_norms.append(nn.BatchNorm1d(hidden_dim))
        
        for _ in range(num_layers - 2):
            self.convs.append(GCNConv(hidden_dim, hidden_dim))
            self.batch_norms.append(nn.BatchNorm1d(hidden_dim))
        
        self.convs.append(GCNConv(hidden_dim, input_dim))
        self.dropout = nn.Dropout(0.2)
        
    def forward(self, x, edge_index):
        """前向传播"""
        residual = x
        
        for i, (conv, bn) in enumerate(zip(self.convs[:-1], self.batch_norms)):
            x = conv(x, edge_index)
            x = bn(x)
            x = F.relu(x)
            x = self.dropout(x)
            
            # 残差连接
            if i == 0:
                residual = x
            elif x.shape == residual.shape:
                x = x + residual
                residual = x
        
        x = self.convs[-1](x, edge_index)
        return x

class UnsupervisedContributionNet(nn.Module):
    """无监督深度学习贡献率预测网络"""
    
    def __init__(self, input_dim: int, num_sources: int):
        super().__init__()
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, num_sources)
        )
        
        self.decoder = nn.Sequential(
            nn.Linear(num_sources, 32),
            nn.ReLU(),
            nn.Linear(32, 64),
            nn.ReLU(),
            nn.Linear(64, input_dim)
        )
    
    def forward(self, x):
        """前向传播"""
        encoded = self.encoder(x)
        contrib = F.softmax(encoded, dim=1)  # 确保贡献率和为1
        reconstructed = self.decoder(encoded)
        return contrib, reconstructed

class MCDropoutNet(nn.Module):
    """蒙特卡洛Dropout网络"""
    
    def __init__(self, input_dim: int):
        super().__init__()
        self.layers = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(32, 1)
        )

    def forward(self, x):
        """前向传播"""
        return self.layers(x)

class DeepLearningProcessor:
    """深度学习处理器"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        初始化深度学习处理器
        
        Args:
            config: 配置对象
        """
        self.config = config or get_config()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
    def train_autoencoder(self, receptor_fp: np.ndarray, 
                         hidden_dims: List[int] = [8], 
                         epochs: int = 200) -> np.ndarray:
        """
        训练自编码器获取低维特征表示
        
        Args:
            receptor_fp: 受体指纹矩阵
            hidden_dims: 隐藏层维度
            epochs: 训练轮数
            
        Returns:
            np.ndarray: 编码后的特征
        """
        try:
            input_dim = receptor_fp.shape[1]
            model = EnhancedAutoencoder(input_dim, hidden_dims=hidden_dims)
            model = model.to(self.device)
            
            criterion = nn.MSELoss()
            optimizer = optim.Adam(model.parameters(), lr=0.001)
            
            tensor_data = torch.tensor(receptor_fp, dtype=torch.float32).to(self.device)
            
            model.train()
            for epoch in range(epochs):
                optimizer.zero_grad()
                reconstructed = model(tensor_data)
                loss = criterion(reconstructed, tensor_data)
                loss.backward()
                optimizer.step()
                
                if epoch % 50 == 0:
                    print(f"Autoencoder Epoch {epoch}, Loss: {loss.item():.6f}")
            
            model.eval()
            with torch.no_grad():
                encoded_features = model.encode(tensor_data)
            
            return encoded_features.cpu().numpy()
            
        except Exception as e:
            raise Exception(f"自编码器训练失败: {str(e)}")
    
    def build_graph_data(self, samples: pd.DataFrame, features: np.ndarray) -> Data:
        """
        构建图神经网络所需数据结构
        
        Args:
            samples: 样本数据
            features: 特征矩阵
            
        Returns:
            Data: PyTorch Geometric数据对象
        """
        try:
            # 节点特征
            x = torch.tensor(features, dtype=torch.float)
            
            # 构建空间位置
            coords = samples[['lon', 'lat']].values
            
            # 计算距离 (使用平面投影)
            p = Proj(proj='aeqd', ellps='WGS84', 
                    lat_0=coords[:, 1].mean(), lon_0=coords[:, 0].mean())
            x_proj, y_proj = p(coords[:, 0], coords[:, 1])
            projected_coords = np.column_stack([x_proj, y_proj])
            
            # 创建K近邻图
            edge_index = kneighbors_graph(
                projected_coords, n_neighbors=8, mode='connectivity'
            ).tocoo()
            
            edge_index = torch.tensor(
                np.array([edge_index.row, edge_index.col]), dtype=torch.long
            )
            
            return Data(
                x=x, 
                edge_index=edge_index, 
                pos=torch.tensor(projected_coords, dtype=torch.float)
            )
            
        except Exception as e:
            raise Exception(f"图数据构建失败: {str(e)}")
    
    def train_gnn(self, data: Data, hidden_dim: int = 64, 
                 num_layers: int = 3, epochs: int = 100) -> np.ndarray:
        """
        训练GNN获取空间增强特征
        
        Args:
            data: 图数据
            hidden_dim: 隐藏层维度
            num_layers: 层数
            epochs: 训练轮数
            
        Returns:
            np.ndarray: GNN嵌入特征
        """
        try:
            model = SpatialGNN(
                input_dim=data.x.size(1), 
                hidden_dim=hidden_dim, 
                num_layers=num_layers
            )
            model = model.to(self.device)
            data = data.to(self.device)
            
            optimizer = optim.Adam(model.parameters(), lr=0.01)
            
            model.train()
            for epoch in range(epochs):
                optimizer.zero_grad()
                out = model(data.x, data.edge_index)
                neighbor_sim = F.cosine_similarity(
                    out[data.edge_index[0]], out[data.edge_index[1]]
                )
                loss = 1 - torch.mean(neighbor_sim)
                loss.backward()
                optimizer.step()
                
                if epoch % 25 == 0:
                    print(f"GNN Epoch {epoch}, Loss: {loss.item():.6f}")
            
            model.eval()
            with torch.no_grad():
                embeddings = model(data.x, data.edge_index)
            
            return embeddings.cpu().detach().numpy()
            
        except Exception as e:
            raise Exception(f"GNN训练失败: {str(e)}")
    
    def train_unsupervised_contribution_model(self, receptor_fp: np.ndarray, 
                                            source_fp: np.ndarray,
                                            samples: pd.DataFrame, 
                                            sources: pd.DataFrame) -> np.ndarray:
        """
        训练无监督深度学习贡献率模型
        
        Args:
            receptor_fp: 受体指纹
            source_fp: 源指纹
            samples: 样本数据
            sources: 源数据
            
        Returns:
            np.ndarray: 贡献率矩阵
        """
        try:
            print("训练无监督深度学习模型...")
            
            # 准备输入特征：化学指纹 + 空间特征
            sample_coords = samples[['lon', 'lat']].values
            source_coords = sources[['lon', 'lat']].values
            
            # 计算每个样本到所有源的距离
            distances = cdist(sample_coords, source_coords)
            
            # 组合特征：化学指纹 + 距离特征
            features = []
            for i in range(len(receptor_fp)):
                combined = np.concatenate([receptor_fp[i], distances[i]])
                features.append(combined)
            
            X = np.array(features)
            
            # 标准化
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            # 创建模型
            input_dim = X_scaled.shape[1]
            num_sources = len(sources)
            model = UnsupervisedContributionNet(input_dim, num_sources)
            model = model.to(self.device)
            
            optimizer = optim.Adam(model.parameters(), lr=0.001)
            X_tensor = torch.tensor(X_scaled, dtype=torch.float32).to(self.device)
            
            # 训练
            model.train()
            for epoch in range(100):
                optimizer.zero_grad()
                contrib, reconstructed = model(X_tensor)
                recon_loss = F.mse_loss(reconstructed, X_tensor)
                sum_loss = torch.mean(torch.abs(torch.sum(contrib, dim=1) - 1.0))
                total_loss = recon_loss + 0.1 * sum_loss
                total_loss.backward()
                optimizer.step()
                
                if epoch % 25 == 0:
                    print(f"Contribution Net Epoch {epoch}, Loss: {total_loss.item():.6f}")
            
            # 预测贡献率
            model.eval()
            with torch.no_grad():
                dl_contrib, _ = model(X_tensor)
                dl_contrib = dl_contrib.cpu().numpy()
            
            print("无监督深度学习模型训练完成")
            return dl_contrib
            
        except Exception as e:
            raise Exception(f"无监督贡献率模型训练失败: {str(e)}")

class SourceContributionPredictor(nn.Module):
    """
    端到端污染源贡献率预测模型

    该模型能够仅基于污染源位置和特征，直接预测任意位置的土壤污染源贡献率
    """

    def __init__(self, source_feature_dim: int, spatial_dim: int = 2,
                 hidden_dims: List[int] = [128, 64, 32],
                 n_source_types: int = 4, dropout_rate: float = 0.3):
        super().__init__()

        self.source_feature_dim = source_feature_dim
        self.spatial_dim = spatial_dim
        self.n_source_types = n_source_types

        # 污染源特征编码器
        self.source_encoder = nn.Sequential(
            nn.Linear(source_feature_dim, hidden_dims[0]),
            nn.BatchNorm1d(hidden_dims[0]),
            nn.ReLU(),
            nn.Dropout(dropout_rate),

            nn.Linear(hidden_dims[0], hidden_dims[1]),
            nn.BatchNorm1d(hidden_dims[1]),
            nn.ReLU(),
            nn.Dropout(dropout_rate),

            nn.Linear(hidden_dims[1], hidden_dims[2]),
            nn.BatchNorm1d(hidden_dims[2]),
            nn.ReLU()
        )

        # 空间关系编码器
        self.spatial_encoder = nn.Sequential(
            nn.Linear(spatial_dim, 32),
            nn.ReLU(),
            nn.Linear(32, 16),
            nn.ReLU()
        )

        # 注意力机制 - 计算每个污染源对目标位置的影响权重
        self.attention = nn.Sequential(
            nn.Linear(hidden_dims[2] + 16, 64),
            nn.ReLU(),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )

        # 贡献率预测器
        self.contribution_predictor = nn.Sequential(
            nn.Linear(hidden_dims[2], 64),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, n_source_types),
            nn.Softmax(dim=-1)  # 确保贡献率和为1
        )

    def forward(self, source_features: torch.Tensor,
                spatial_distances: torch.Tensor,
                target_location: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            source_features: [n_sources, feature_dim] 污染源特征
            spatial_distances: [n_sources, spatial_dim] 到目标位置的空间距离
            target_location: [spatial_dim] 目标位置坐标

        Returns:
            torch.Tensor: [n_source_types] 各源类型的贡献率
        """
        # 编码污染源特征
        encoded_sources = self.source_encoder(source_features)  # [n_sources, hidden_dim]

        # 编码空间关系
        encoded_spatial = self.spatial_encoder(spatial_distances)  # [n_sources, 16]

        # 组合特征
        combined_features = torch.cat([encoded_sources, encoded_spatial], dim=-1)

        # 计算注意力权重
        attention_weights = self.attention(combined_features)  # [n_sources, 1]

        # 加权聚合污染源特征
        weighted_sources = encoded_sources * attention_weights  # [n_sources, hidden_dim]
        aggregated_features = torch.sum(weighted_sources, dim=0)  # [hidden_dim]

        # 预测贡献率
        contributions = self.contribution_predictor(aggregated_features)  # [n_source_types]

        return contributions, attention_weights.squeeze()

class DeepLearningPredictor:
    """
    深度学习预测器 - 训练和使用端到端模型
    """

    def __init__(self, config: Optional[Config] = None):
        self.config = config or get_config()
        self.model = None
        self.scaler_features = StandardScaler()
        self.scaler_spatial = StandardScaler()
        self.is_trained = False

    def prepare_training_data(self, samples: pd.DataFrame, sources: pd.DataFrame,
                            pmf_contributions: np.ndarray):
        """
        准备训练数据

        Args:
            samples: 土壤样本数据
            sources: 污染源数据
            pmf_contributions: PMF分析得到的贡献率 [n_samples, n_sources]

        Returns:
            训练数据张量
        """
        # 提取污染源特征（重金属浓度）
        metal_columns = [col for col in sources.columns if col in ['Cu', 'Cr', 'Ni', 'Zn', 'Pb', 'Cd', 'As']]
        source_features = sources[metal_columns].values
        source_features = self.scaler_features.fit_transform(source_features)

        # 提取空间坐标
        source_coords = sources[['lon', 'lat']].values
        sample_coords = samples[['lon', 'lat']].values

        training_data = []
        training_labels = []

        for i, sample_coord in enumerate(sample_coords):
            # 计算到所有污染源的距离
            distances = cdist([sample_coord], source_coords)[0]
            spatial_features = np.column_stack([distances, np.full(len(distances), sample_coord[0])])
            spatial_features = self.scaler_spatial.fit_transform(spatial_features)

            # 聚合各源类型的贡献率
            source_type_contributions = np.zeros(4)  # [atmosphere, irrigation, pesticide, manure]
            for j, contrib in enumerate(pmf_contributions[i]):
                if j < len(sources):
                    source_type = sources.iloc[j]['source_type']
                    if source_type == 'atmosphere':
                        source_type_contributions[0] += contrib
                    elif source_type == 'irrigation':
                        source_type_contributions[1] += contrib
                    elif source_type == 'pesticide':
                        source_type_contributions[2] += contrib
                    elif source_type == 'manure':
                        source_type_contributions[3] += contrib

            training_data.append({
                'source_features': source_features,
                'spatial_features': spatial_features,
                'target_location': sample_coord
            })
            training_labels.append(source_type_contributions)

        return training_data, np.array(training_labels)

    def train_model(self, samples: pd.DataFrame, sources: pd.DataFrame,
                   pmf_contributions: np.ndarray, epochs: int = 200) -> None:
        """
        训练端到端预测模型
        """
        try:
            print("准备训练数据...")
            training_data, training_labels = self.prepare_training_data(samples, sources, pmf_contributions)

            # 初始化模型
            source_feature_dim = len([col for col in sources.columns if col in ['Cu', 'Cr', 'Ni', 'Zn', 'Pb', 'Cd', 'As']])
            self.model = SourceContributionPredictor(
                source_feature_dim=source_feature_dim,
                spatial_dim=2,  # distance, lon
                hidden_dims=[128, 64, 32],
                n_source_types=4
            )

            optimizer = optim.Adam(self.model.parameters(), lr=0.001, weight_decay=1e-5)
            criterion = nn.MSELoss()
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=20, factor=0.5)

            print(f"开始训练模型，共{epochs}轮...")

            best_loss = float('inf')
            patience_counter = 0

            for epoch in range(epochs):
                total_loss = 0
                self.model.train()

                for i, (data, label) in enumerate(zip(training_data, training_labels)):
                    optimizer.zero_grad()

                    # 转换为张量
                    source_features = torch.FloatTensor(data['source_features'])
                    spatial_features = torch.FloatTensor(data['spatial_features'])
                    target_location = torch.FloatTensor(data['target_location'])
                    label_tensor = torch.FloatTensor(label)

                    # 前向传播
                    predictions, attention_weights = self.model(source_features, spatial_features, target_location)

                    # 计算损失
                    loss = criterion(predictions, label_tensor)

                    # 反向传播
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                    optimizer.step()

                    total_loss += loss.item()

                avg_loss = total_loss / len(training_data)
                scheduler.step(avg_loss)

                if avg_loss < best_loss:
                    best_loss = avg_loss
                    patience_counter = 0
                else:
                    patience_counter += 1

                if epoch % 20 == 0:
                    print(f"Epoch {epoch}, Loss: {avg_loss:.6f}, Best Loss: {best_loss:.6f}")

                # 早停
                if patience_counter >= 50:
                    print(f"早停于第{epoch}轮")
                    break

            self.is_trained = True
            print(f"模型训练完成，最佳损失: {best_loss:.6f}")

        except Exception as e:
            print(f"模型训练失败: {str(e)}")
            raise

    def predict_contributions(self, target_location: np.ndarray, sources: pd.DataFrame):
        """
        预测指定位置的污染源贡献率

        Args:
            target_location: [lon, lat] 目标位置坐标
            sources: 污染源数据

        Returns:
            np.ndarray: [atmosphere, irrigation, pesticide, manure] 各源类型贡献率
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练，请先调用train_model方法")

        self.model.eval()

        with torch.no_grad():
            # 准备污染源特征
            metal_columns = [col for col in sources.columns if col in ['Cu', 'Cr', 'Ni', 'Zn', 'Pb', 'Cd', 'As']]
            source_features = sources[metal_columns].values
            source_features = self.scaler_features.transform(source_features)

            # 计算空间距离
            source_coords = sources[['lon', 'lat']].values
            distances = cdist([target_location], source_coords)[0]
            spatial_features = np.column_stack([distances, np.full(len(distances), target_location[0])])
            spatial_features = self.scaler_spatial.transform(spatial_features)

            # 转换为张量
            source_features_tensor = torch.FloatTensor(source_features)
            spatial_features_tensor = torch.FloatTensor(spatial_features)
            target_location_tensor = torch.FloatTensor(target_location)

            # 预测
            predictions, attention_weights = self.model(source_features_tensor, spatial_features_tensor, target_location_tensor)

            return predictions.numpy(), attention_weights.numpy()
