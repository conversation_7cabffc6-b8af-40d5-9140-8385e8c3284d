#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速NaN修复验证脚本

快速测试NaN值修复是否有效
"""

import torch
import numpy as np
import logging
import sys
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_nan_handling():
    """测试NaN处理功能"""
    print("=" * 50)
    print("测试NaN处理功能")
    print("=" * 50)
    
    try:
        # 导入修复后的函数
        from train_tsisp import validate_coordinates, build_knn_graph_fallback, build_radius_graph_fallback
        
        # 创建包含NaN的测试数据
        test_cases = [
            # 案例1：部分NaN
            torch.tensor([
                [1.0, 2.0, 3.0],
                [4.0, 5.0, 6.0],
                [float('nan'), 8.0, 9.0],
                [10.0, 11.0, 12.0]
            ], dtype=torch.float32),
            
            # 案例2：全部NaN
            torch.tensor([
                [float('nan'), float('nan'), float('nan')],
                [float('nan'), float('nan'), float('nan')]
            ], dtype=torch.float32),
            
            # 案例3：无NaN
            torch.tensor([
                [1.0, 2.0, 3.0],
                [4.0, 5.0, 6.0],
                [7.0, 8.0, 9.0]
            ], dtype=torch.float32),
            
            # 案例4：包含无穷大
            torch.tensor([
                [1.0, 2.0, 3.0],
                [float('inf'), 5.0, 6.0],
                [7.0, 8.0, float('-inf')]
            ], dtype=torch.float32)
        ]
        
        for i, coords in enumerate(test_cases):
            print(f"\n测试案例 {i+1}:")
            print(f"输入形状: {coords.shape}")
            print(f"NaN数量: {torch.isnan(coords).sum().item()}")
            print(f"Inf数量: {torch.isinf(coords).sum().item()}")
            
            try:
                # 测试坐标验证
                clean_coords, valid_mask = validate_coordinates(coords, f"case_{i+1}")
                print(f"清理后形状: {clean_coords.shape}")
                print(f"有效点数: {valid_mask.sum().item()}")
                
                # 测试图构建
                if clean_coords.shape[0] >= 2:
                    edge_knn = build_knn_graph_fallback(coords, k=1)
                    edge_radius = build_radius_graph_fallback(coords, radius=10.0)
                    print(f"KNN边数: {edge_knn.shape[1]}")
                    print(f"半径图边数: {edge_radius.shape[1]}")
                    print("✓ 图构建成功")
                else:
                    print("! 有效点数不足，跳过图构建")
                
            except Exception as e:
                print(f"✗ 测试失败: {e}")
                return False
        
        print("\n✅ 所有NaN处理测试通过")
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_model_integration():
    """测试模型集成"""
    print("\n" + "=" * 50)
    print("测试模型集成")
    print("=" * 50)
    
    try:
        from train_tsisp import Config, GeoSpatialGNN
        
        # 设置配置
        Config.architecture = 'geospatial_gnn'
        Config.learnable_encoding = True
        Config.enable_uncertainty = True
        
        # 创建模型
        num_industries = 5
        model = GeoSpatialGNN(num_industries)
        
        # 创建包含潜在NaN的测试数据
        batch_size = 8
        
        # 模拟可能产生NaN的数据
        coords = torch.randn(batch_size, 3)
        # 人为添加一些极值，可能在变换中产生NaN
        coords[0, 0] = 1e10  # 极大值
        coords[1, 1] = -1e10  # 极小值
        
        test_batch = {
            'local_coordinates': coords,
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size, 1)).float(),
                torch.randn(batch_size, 2)
            ], dim=1),
            'point_attributes': torch.randn(batch_size, 2),
            'concentrations': torch.randn(batch_size, 1)
        }
        
        all_points = {
            'local_coordinates': torch.randn(batch_size * 2, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size * 2, 1)).float(),
                torch.randn(batch_size * 2, 2)
            ], dim=1),
            'point_attributes': torch.randn(batch_size * 2, 2)
        }
        
        print(f"测试数据创建成功")
        print(f"坐标范围: [{coords.min().item():.2e}, {coords.max().item():.2e}]")
        
        # 测试前向传播
        model.eval()
        with torch.no_grad():
            try:
                final_pred, model_pred, kriging_pred, uncertainty, physics_loss = model(
                    test_batch, all_points, training=False
                )
                
                print(f"✓ 前向传播成功")
                print(f"输出形状: {final_pred.shape}")
                print(f"输出范围: [{final_pred.min().item():.3f}, {final_pred.max().item():.3f}]")
                
                # 检查输出是否包含NaN
                if torch.isnan(final_pred).any():
                    print("✗ 输出包含NaN值")
                    return False
                else:
                    print("✓ 输出无NaN值")
                
                return True
                
            except ValueError as e:
                if "Input X contains NaN" in str(e):
                    print(f"✗ 仍然存在NaN错误: {e}")
                    return False
                else:
                    print(f"✗ 其他ValueError: {e}")
                    return False
            except Exception as e:
                print(f"✗ 前向传播失败: {e}")
                return False
        
    except Exception as e:
        print(f"✗ 模型集成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("TSISP NaN修复快速验证")
    print("验证ValueError: Input X contains NaN的修复效果\n")
    
    # 测试NaN处理
    nan_test_ok = test_nan_handling()
    
    # 测试模型集成
    model_test_ok = test_model_integration()
    
    # 总结
    print("\n" + "=" * 50)
    print("验证结果")
    print("=" * 50)
    
    if nan_test_ok:
        print("✓ NaN处理功能正常")
    else:
        print("✗ NaN处理功能有问题")
    
    if model_test_ok:
        print("✓ 模型集成正常")
    else:
        print("✗ 模型集成有问题")
    
    success = nan_test_ok and model_test_ok
    
    if success:
        print("\n🎉 NaN修复验证成功！")
        print("现在可以安全运行TSISP模型")
        print("\n使用方法:")
        print("python train_tsisp.py --metal Pb --output test_output")
    else:
        print("\n❌ 验证失败，需要进一步调试")
        print("建议运行: python fix_nan_values.py")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
