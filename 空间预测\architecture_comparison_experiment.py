#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
架构对比实验脚本

本脚本用于系统性对比传统Transformer架构与创新GeoSpatial-GNN架构的性能
包括预测精度、计算效率、不确定性量化等多个维度的评估

使用方法：
python architecture_comparison_experiment.py --data data.csv --metal Pb --output comparison_results
"""

import os
import time
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple
import torch
import psutil
import logging

# 导入训练模块
from train_tsisp import Config, train_model, train_multiple_metals

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ArchitectureComparison:
    """架构对比实验类"""
    
    def __init__(self, data_path: str, output_dir: str):
        self.data_path = data_path
        self.output_dir = output_dir
        self.results = {}
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'models'), exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'plots'), exist_ok=True)
        
    def run_architecture_comparison(self, metal: str, num_runs: int = 3):
        """
        运行架构对比实验
        
        Args:
            metal: 目标金属
            num_runs: 重复实验次数
        """
        logger.info(f"开始架构对比实验 - 目标金属: {metal}")
        
        architectures = ['transformer', 'geospatial_gnn']
        
        for arch in architectures:
            logger.info(f"测试架构: {arch}")
            arch_results = []
            
            for run in range(num_runs):
                logger.info(f"运行 {run + 1}/{num_runs}")
                
                # 设置架构
                Config.architecture = arch
                Config.set_target_metal(metal)
                
                # 运行实验
                result = self._run_single_experiment(arch, run)
                arch_results.append(result)
                
            self.results[arch] = arch_results
            
        # 生成对比报告
        self._generate_comparison_report(metal)
        
    def _run_single_experiment(self, architecture: str, run_id: int) -> Dict:
        """运行单次实验"""
        
        # 记录开始时间和内存
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        # 创建实验输出目录
        exp_output_dir = os.path.join(self.output_dir, 'models', f'{architecture}_run_{run_id}')
        
        try:
            # 训练模型
            model, best_epoch = train_model(
                csv_path=self.data_path,
                output_dir=exp_output_dir,
                random_seed=42 + run_id
            )
            
            # 记录结束时间和内存
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            
            # 读取训练历史
            history_path = os.path.join(exp_output_dir, 'training_history_final.csv')
            if os.path.exists(history_path):
                history_df = pd.read_csv(history_path)
                
                # 提取最终性能指标
                final_metrics = {
                    'val_r2': history_df[f'val_{Config.target_metal}_r2'].iloc[-10:].mean(),
                    'val_rmse': history_df[f'val_{Config.target_metal}_rmse'].iloc[-10:].mean(),
                    'val_mae': history_df[f'val_{Config.target_metal}_mae'].iloc[-10:].mean(),
                    'train_r2': history_df[f'train_{Config.target_metal}_r2'].iloc[-10:].mean(),
                    'train_rmse': history_df[f'train_{Config.target_metal}_rmse'].iloc[-10:].mean(),
                    'convergence_epoch': best_epoch
                }
            else:
                final_metrics = {}
            
            # 计算模型参数数量
            total_params = sum(p.numel() for p in model.parameters())
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            
            result = {
                'architecture': architecture,
                'run_id': run_id,
                'success': True,
                'training_time': end_time - start_time,
                'memory_usage': end_memory - start_memory,
                'total_params': total_params,
                'trainable_params': trainable_params,
                'convergence_epoch': best_epoch,
                **final_metrics
            }
            
            logger.info(f"实验成功完成 - {architecture} run {run_id}")
            
        except Exception as e:
            logger.error(f"实验失败 - {architecture} run {run_id}: {e}")
            result = {
                'architecture': architecture,
                'run_id': run_id,
                'success': False,
                'error': str(e)
            }
            
        return result
    
    def _generate_comparison_report(self, metal: str):
        """生成对比报告"""
        
        logger.info("生成架构对比报告")
        
        # 创建对比数据框
        comparison_data = []
        for arch, results in self.results.items():
            for result in results:
                if result['success']:
                    comparison_data.append(result)
        
        if not comparison_data:
            logger.error("没有成功的实验结果")
            return
            
        df = pd.DataFrame(comparison_data)
        
        # 保存详细结果
        df.to_csv(os.path.join(self.output_dir, 'detailed_results.csv'), index=False)
        
        # 生成统计摘要
        summary = self._create_summary_statistics(df)
        
        # 保存摘要
        with open(os.path.join(self.output_dir, 'summary_statistics.json'), 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        # 生成可视化
        self._create_comparison_plots(df, metal)
        
        # 生成文本报告
        self._create_text_report(summary, metal)
        
    def _create_summary_statistics(self, df: pd.DataFrame) -> Dict:
        """创建统计摘要"""
        
        summary = {}
        
        for arch in df['architecture'].unique():
            arch_data = df[df['architecture'] == arch]
            
            summary[arch] = {
                'performance': {
                    'val_r2_mean': arch_data['val_r2'].mean(),
                    'val_r2_std': arch_data['val_r2'].std(),
                    'val_rmse_mean': arch_data['val_rmse'].mean(),
                    'val_rmse_std': arch_data['val_rmse'].std(),
                    'val_mae_mean': arch_data['val_mae'].mean(),
                    'val_mae_std': arch_data['val_mae'].std()
                },
                'efficiency': {
                    'training_time_mean': arch_data['training_time'].mean(),
                    'training_time_std': arch_data['training_time'].std(),
                    'memory_usage_mean': arch_data['memory_usage'].mean(),
                    'memory_usage_std': arch_data['memory_usage'].std(),
                    'convergence_epoch_mean': arch_data['convergence_epoch'].mean(),
                    'convergence_epoch_std': arch_data['convergence_epoch'].std()
                },
                'model_complexity': {
                    'total_params_mean': arch_data['total_params'].mean(),
                    'trainable_params_mean': arch_data['trainable_params'].mean()
                }
            }
            
        return summary
    
    def _create_comparison_plots(self, df: pd.DataFrame, metal: str):
        """创建对比可视化图表"""
        
        # 设置绘图风格
        plt.style.use('default')
        sns.set_palette("husl")
        
        # 创建综合对比图
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'架构对比分析 - {metal}', fontsize=16, fontweight='bold')
        
        # 1. R²对比
        sns.boxplot(data=df, x='architecture', y='val_r2', ax=axes[0, 0])
        axes[0, 0].set_title('验证R²分布', fontweight='bold')
        axes[0, 0].set_ylabel('R² Score')
        
        # 2. RMSE对比
        sns.boxplot(data=df, x='architecture', y='val_rmse', ax=axes[0, 1])
        axes[0, 1].set_title('验证RMSE分布', fontweight='bold')
        axes[0, 1].set_ylabel('RMSE (mg/kg)')
        
        # 3. 训练时间对比
        sns.boxplot(data=df, x='architecture', y='training_time', ax=axes[0, 2])
        axes[0, 2].set_title('训练时间分布', fontweight='bold')
        axes[0, 2].set_ylabel('时间 (秒)')
        
        # 4. 内存使用对比
        sns.boxplot(data=df, x='architecture', y='memory_usage', ax=axes[1, 0])
        axes[1, 0].set_title('内存使用分布', fontweight='bold')
        axes[1, 0].set_ylabel('内存 (MB)')
        
        # 5. 收敛速度对比
        sns.boxplot(data=df, x='architecture', y='convergence_epoch', ax=axes[1, 1])
        axes[1, 1].set_title('收敛速度分布', fontweight='bold')
        axes[1, 1].set_ylabel('收敛轮次')
        
        # 6. 参数数量对比
        sns.barplot(data=df, x='architecture', y='total_params', ax=axes[1, 2])
        axes[1, 2].set_title('模型参数数量', fontweight='bold')
        axes[1, 2].set_ylabel('参数数量')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'plots', 'architecture_comparison.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        # 创建性能雷达图
        self._create_radar_chart(df, metal)
        
    def _create_radar_chart(self, df: pd.DataFrame, metal: str):
        """创建性能雷达图"""
        
        # 计算归一化指标
        metrics = ['val_r2', 'val_rmse', 'training_time', 'memory_usage', 'convergence_epoch']
        
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
        
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形
        
        for arch in df['architecture'].unique():
            arch_data = df[df['architecture'] == arch]
            
            # 计算平均值并归一化
            values = []
            for metric in metrics:
                if metric in ['val_r2']:  # 越高越好
                    values.append(arch_data[metric].mean())
                else:  # 越低越好，需要反转
                    max_val = df[metric].max()
                    min_val = df[metric].min()
                    normalized = 1 - (arch_data[metric].mean() - min_val) / (max_val - min_val + 1e-8)
                    values.append(normalized)
            
            values += values[:1]  # 闭合图形
            
            ax.plot(angles, values, 'o-', linewidth=2, label=arch)
            ax.fill(angles, values, alpha=0.25)
        
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(['R²', 'RMSE', '训练时间', '内存使用', '收敛速度'])
        ax.set_ylim(0, 1)
        ax.set_title(f'架构性能雷达图 - {metal}', fontsize=14, fontweight='bold', pad=20)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        
        plt.savefig(os.path.join(self.output_dir, 'plots', 'performance_radar.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
    def _create_text_report(self, summary: Dict, metal: str):
        """创建文本报告"""
        
        report = f"""
# 架构对比实验报告

## 实验概述
- 目标金属: {metal}
- 对比架构: {list(summary.keys())}
- 实验时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}

## 性能对比

### 预测精度
"""
        
        for arch, data in summary.items():
            perf = data['performance']
            report += f"""
#### {arch.upper()}
- 验证R²: {perf['val_r2_mean']:.4f} ± {perf['val_r2_std']:.4f}
- 验证RMSE: {perf['val_rmse_mean']:.4f} ± {perf['val_rmse_std']:.4f}
- 验证MAE: {perf['val_mae_mean']:.4f} ± {perf['val_mae_std']:.4f}
"""
        
        report += "\n### 计算效率\n"
        
        for arch, data in summary.items():
            eff = data['efficiency']
            report += f"""
#### {arch.upper()}
- 训练时间: {eff['training_time_mean']:.2f} ± {eff['training_time_std']:.2f} 秒
- 内存使用: {eff['memory_usage_mean']:.2f} ± {eff['memory_usage_std']:.2f} MB
- 收敛轮次: {eff['convergence_epoch_mean']:.1f} ± {eff['convergence_epoch_std']:.1f}
"""
        
        report += "\n### 模型复杂度\n"
        
        for arch, data in summary.items():
            comp = data['model_complexity']
            report += f"""
#### {arch.upper()}
- 总参数数: {comp['total_params_mean']:,.0f}
- 可训练参数: {comp['trainable_params_mean']:,.0f}
"""
        
        # 添加结论
        if len(summary) == 2:
            archs = list(summary.keys())
            arch1, arch2 = archs[0], archs[1]
            
            r2_diff = summary[arch2]['performance']['val_r2_mean'] - summary[arch1]['performance']['val_r2_mean']
            time_diff = summary[arch1]['efficiency']['training_time_mean'] - summary[arch2]['efficiency']['training_time_mean']
            
            report += f"""
## 结论

### 性能提升
- {arch2.upper()} 相比 {arch1.upper()} 的R²提升: {r2_diff:+.4f}
- 训练时间差异: {time_diff:+.2f} 秒

### 推荐建议
"""
            if r2_diff > 0.01:
                report += f"- {arch2.upper()} 在预测精度上显著优于 {arch1.upper()}\n"
            if time_diff > 0:
                report += f"- {arch2.upper()} 在计算效率上优于 {arch1.upper()}\n"
        
        # 保存报告
        with open(os.path.join(self.output_dir, 'comparison_report.md'), 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info("对比报告生成完成")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='架构对比实验')
    parser.add_argument('--data', type=str, required=True, help='数据文件路径')
    parser.add_argument('--metal', type=str, default='Pb', help='目标金属')
    parser.add_argument('--output', type=str, default='comparison_results', help='输出目录')
    parser.add_argument('--runs', type=int, default=3, help='重复实验次数')
    
    args = parser.parse_args()
    
    # 创建对比实验实例
    comparison = ArchitectureComparison(args.data, args.output)
    
    # 运行对比实验
    comparison.run_architecture_comparison(args.metal, args.runs)
    
    logger.info(f"架构对比实验完成，结果保存在: {args.output}")


if __name__ == "__main__":
    main()
