#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
张量维度修复验证脚本

快速测试维度不匹配修复是否有效
验证 RuntimeError: mat1 and mat2 shapes cannot be multiplied (500x257 and 258x256)
"""

import torch
import numpy as np
import logging
import sys
import traceback

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dimension_fix():
    """测试维度修复"""
    print("=" * 50)
    print("测试维度修复效果")
    print("=" * 50)
    
    try:
        from train_tsisp import Config, GeoSpatialGNN
        
        # 设置配置
        Config.architecture = 'geospatial_gnn'
        Config.learnable_encoding = True
        Config.enable_uncertainty = True
        
        # 显示当前配置
        print(f"当前配置:")
        print(f"  encoding_dim: {Config.encoding_dim}")
        print(f"  embedding_dim: {Config.embedding_dim}")
        print(f"  site_attributes: {Config.site_attributes}")
        print(f"  point_attributes: {Config.point_attributes}")
        
        # 创建模型
        num_industries = 5
        model = GeoSpatialGNN(num_industries)
        
        print(f"✓ 模型创建成功")
        
        # 创建测试数据
        batch_size = 16
        
        # 确保数据维度与配置匹配
        test_batch = {
            'local_coordinates': torch.randn(batch_size, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size, 1)).float(),  # industry
                torch.randn(batch_size, len(Config.site_attributes) - 1)    # other site attrs
            ], dim=1),
            'point_attributes': torch.randn(batch_size, len(Config.point_attributes))
        }
        
        all_points = {
            'local_coordinates': torch.randn(batch_size * 2, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size * 2, 1)).float(),
                torch.randn(batch_size * 2, len(Config.site_attributes) - 1)
            ], dim=1),
            'point_attributes': torch.randn(batch_size * 2, len(Config.point_attributes))
        }
        
        # 添加浓度数据
        test_batch['concentrations'] = torch.randn(batch_size, 1)
        
        print(f"测试数据形状:")
        for key, value in test_batch.items():
            print(f"  {key}: {value.shape}")
        
        # 测试特征编码
        print(f"\n测试特征编码...")
        try:
            encoded_features = model._encode_features(test_batch)
            print(f"✓ 特征编码成功: {encoded_features.shape}")
        except Exception as e:
            print(f"✗ 特征编码失败: {e}")
            return False
        
        # 测试完整前向传播
        print(f"\n测试完整前向传播...")
        model.eval()
        with torch.no_grad():
            try:
                final_pred, model_pred, kriging_pred, uncertainty, physics_loss = model(
                    test_batch, all_points, training=False
                )
                
                print(f"✓ 前向传播成功")
                print(f"  final_pred: {final_pred.shape}")
                print(f"  model_pred: {model_pred.shape}")
                print(f"  kriging_pred: {kriging_pred.shape}")
                if uncertainty is not None:
                    print(f"  uncertainty: {uncertainty.shape}")
                print(f"  physics_loss: {physics_loss.item():.6f}")
                
                return True
                
            except RuntimeError as e:
                if "mat1 and mat2 shapes cannot be multiplied" in str(e):
                    print(f"✗ 仍然存在维度不匹配错误: {e}")
                    return False
                else:
                    print(f"✗ 其他运行时错误: {e}")
                    return False
            except Exception as e:
                print(f"✗ 前向传播失败: {e}")
                traceback.print_exc()
                return False
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        traceback.print_exc()
        return False

def test_problematic_batch_size():
    """测试问题批次大小（500）"""
    print("\n" + "=" * 50)
    print("测试问题批次大小（500）")
    print("=" * 50)
    
    try:
        from train_tsisp import Config, GeoSpatialGNN
        
        Config.architecture = 'geospatial_gnn'
        Config.learnable_encoding = True
        
        num_industries = 5
        model = GeoSpatialGNN(num_industries)
        
        # 测试原始错误中的批次大小
        batch_size = 500
        
        print(f"测试批次大小: {batch_size}")
        
        test_batch = {
            'local_coordinates': torch.randn(batch_size, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size, 1)).float(),
                torch.randn(batch_size, len(Config.site_attributes) - 1)
            ], dim=1),
            'point_attributes': torch.randn(batch_size, len(Config.point_attributes)),
            'concentrations': torch.randn(batch_size, 1)
        }
        
        all_points = {
            'local_coordinates': torch.randn(batch_size, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size, 1)).float(),
                torch.randn(batch_size, len(Config.site_attributes) - 1)
            ], dim=1),
            'point_attributes': torch.randn(batch_size, len(Config.point_attributes))
        }
        
        print(f"数据形状验证:")
        print(f"  coordinates: {test_batch['local_coordinates'].shape}")
        print(f"  site_attributes: {test_batch['site_attributes'].shape}")
        print(f"  point_attributes: {test_batch['point_attributes'].shape}")
        
        try:
            model.eval()
            with torch.no_grad():
                final_pred, _, _, _, _ = model(test_batch, all_points, training=False)
                print(f"✓ 批次大小 {batch_size} 测试成功: {final_pred.shape}")
                return True
        except RuntimeError as e:
            if "mat1 and mat2 shapes cannot be multiplied" in str(e):
                print(f"✗ 批次大小 {batch_size} 仍有维度错误: {e}")
                return False
            else:
                print(f"✗ 批次大小 {batch_size} 其他错误: {e}")
                return False
        except Exception as e:
            print(f"✗ 批次大小 {batch_size} 失败: {e}")
            return False
        
    except Exception as e:
        print(f"批次大小测试失败: {e}")
        return False

def test_training_mode():
    """测试训练模式"""
    print("\n" + "=" * 50)
    print("测试训练模式")
    print("=" * 50)
    
    try:
        from train_tsisp import Config, GeoSpatialGNN
        
        Config.architecture = 'geospatial_gnn'
        Config.learnable_encoding = True
        
        num_industries = 5
        model = GeoSpatialGNN(num_industries)
        
        batch_size = 16
        
        test_batch = {
            'local_coordinates': torch.randn(batch_size, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size, 1)).float(),
                torch.randn(batch_size, len(Config.site_attributes) - 1)
            ], dim=1),
            'point_attributes': torch.randn(batch_size, len(Config.point_attributes)),
            'concentrations': torch.randn(batch_size, 1)
        }
        
        all_points = {
            'local_coordinates': torch.randn(batch_size * 2, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size * 2, 1)).float(),
                torch.randn(batch_size * 2, len(Config.site_attributes) - 1)
            ], dim=1),
            'point_attributes': torch.randn(batch_size * 2, len(Config.point_attributes))
        }
        
        # 测试训练模式
        model.train()
        try:
            final_pred, model_pred, kriging_pred, uncertainty, physics_loss = model(
                test_batch, all_points, training=True
            )
            
            print(f"✓ 训练模式前向传播成功")
            
            # 测试反向传播
            loss = final_pred.mean()
            loss.backward()
            print(f"✓ 反向传播成功")
            
            return True
            
        except Exception as e:
            print(f"✗ 训练模式测试失败: {e}")
            return False
        
    except Exception as e:
        print(f"训练模式测试失败: {e}")
        return False

def main():
    """主函数"""
    print("TSISP张量维度修复验证工具")
    print("验证 RuntimeError: mat1 and mat2 shapes cannot be multiplied 的修复\n")
    
    # 测试基本功能
    basic_test_ok = test_dimension_fix()
    
    # 测试问题批次大小
    batch_test_ok = False
    if basic_test_ok:
        batch_test_ok = test_problematic_batch_size()
    
    # 测试训练模式
    training_test_ok = False
    if basic_test_ok:
        training_test_ok = test_training_mode()
    
    # 总结结果
    print("\n" + "=" * 50)
    print("验证结果总结")
    print("=" * 50)
    
    if basic_test_ok:
        print("✓ 基本维度修复成功")
    else:
        print("✗ 基本维度修复失败")
    
    if batch_test_ok:
        print("✓ 问题批次大小（500）测试通过")
    else:
        print("✗ 问题批次大小（500）测试失败")
    
    if training_test_ok:
        print("✓ 训练模式测试通过")
    else:
        print("✗ 训练模式测试失败")
    
    success = basic_test_ok and batch_test_ok and training_test_ok
    
    if success:
        print("\n🎉 张量维度修复验证成功！")
        print("RuntimeError: mat1 and mat2 shapes cannot be multiplied 错误已解决")
        print("\n现在可以正常运行:")
        print("python train_tsisp.py --metal Pb --output test_output")
    else:
        print("\n❌ 仍有问题需要解决")
        print("建议运行详细调试: python debug_dimension_mismatch.py")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
