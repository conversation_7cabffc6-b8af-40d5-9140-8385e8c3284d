"""
Data Processing Module for Heavy Metal Pollution Source Apportionment

This module provides robust data loading, validation, and preprocessing capabilities
for pollution source apportionment analysis, following EST journal standards.

Authors: <AUTHORS>
Version: 3.0 (Refactored)
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from scipy import stats
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.impute import KNNImputer

from config import ConfigManager, get_config_manager


@dataclass
class DataQualityReport:
    """Data quality assessment report."""
    
    missing_values: Dict[str, float]
    outliers: Dict[str, int]
    data_range: Dict[str, Tuple[float, float]]
    correlation_issues: List[str]
    recommendations: List[str]
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for serialization."""
        def convert_numpy_types(obj):
            """Convert numpy types to native Python types."""
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            elif isinstance(obj, tuple):
                return tuple(convert_numpy_types(item) for item in obj)
            return obj

        return convert_numpy_types({
            'missing_values': self.missing_values,
            'outliers': self.outliers,
            'data_range': self.data_range,
            'correlation_issues': self.correlation_issues,
            'recommendations': self.recommendations
        })


class DataValidator:
    """Data validation and quality control for environmental data."""
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize data validator.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.logger = logging.getLogger(__name__)
        
        # Environmental concentration limits (mg/kg) based on literature
        self.concentration_limits = {
            'Cu': (1.0, 1000.0),
            'Cr': (1.0, 1000.0),
            'Ni': (1.0, 500.0),
            'Zn': (10.0, 2000.0),
            'Pb': (1.0, 1000.0),
            'Cd': (0.1, 100.0),
            'As': (0.5, 200.0)
        }
    
    def validate_dataset(self, df: pd.DataFrame, dataset_name: str) -> DataQualityReport:
        """
        Comprehensive data validation.
        
        Args:
            df: DataFrame to validate
            dataset_name: Name of the dataset for logging
            
        Returns:
            DataQualityReport: Comprehensive quality assessment
        """
        self.logger.info(f"Validating dataset: {dataset_name}")
        
        metals = self.config.get_data_config().metals
        
        # Check missing values
        missing_values = {}
        for col in metals:
            if col in df.columns:
                missing_pct = (df[col].isna().sum() / len(df)) * 100
                missing_values[col] = missing_pct
        
        # Detect outliers using IQR method
        outliers = {}
        for col in metals:
            if col in df.columns:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                outlier_count = ((df[col] < lower_bound) | (df[col] > upper_bound)).sum()
                outliers[col] = outlier_count
        
        # Check data ranges
        data_range = {}
        for col in metals:
            if col in df.columns:
                data_range[col] = (df[col].min(), df[col].max())
        
        # Check for correlation issues
        correlation_issues = []
        if len([col for col in metals if col in df.columns]) > 1:
            corr_matrix = df[metals].corr()
            high_corr_pairs = []
            for i in range(len(corr_matrix.columns)):
                for j in range(i+1, len(corr_matrix.columns)):
                    if abs(corr_matrix.iloc[i, j]) > 0.95:
                        high_corr_pairs.append(
                            f"{corr_matrix.columns[i]}-{corr_matrix.columns[j]}: {corr_matrix.iloc[i, j]:.3f}"
                        )
            correlation_issues = high_corr_pairs
        
        # Generate recommendations
        recommendations = []
        
        # Missing value recommendations
        high_missing = [col for col, pct in missing_values.items() if pct > 20]
        if high_missing:
            recommendations.append(f"High missing values in: {', '.join(high_missing)}")
        
        # Outlier recommendations
        high_outliers = [col for col, count in outliers.items() if count > len(df) * 0.1]
        if high_outliers:
            recommendations.append(f"High outlier count in: {', '.join(high_outliers)}")
        
        # Range recommendations
        for col, (min_val, max_val) in data_range.items():
            if col in self.concentration_limits:
                expected_min, expected_max = self.concentration_limits[col]
                if min_val < expected_min or max_val > expected_max:
                    recommendations.append(
                        f"{col} values outside expected range [{expected_min}, {expected_max}]"
                    )
        
        return DataQualityReport(
            missing_values=missing_values,
            outliers=outliers,
            data_range=data_range,
            correlation_issues=correlation_issues,
            recommendations=recommendations
        )
    
    def detect_anomalies(self, df: pd.DataFrame, method: str = 'isolation_forest') -> np.ndarray:
        """
        Detect anomalous samples using various methods.
        
        Args:
            df: DataFrame with metal concentrations
            method: Anomaly detection method
            
        Returns:
            Boolean array indicating anomalous samples
        """
        metals = self.config.get_data_config().metals
        data = df[metals].dropna()
        
        if method == 'isolation_forest':
            from sklearn.ensemble import IsolationForest
            detector = IsolationForest(contamination=0.1, random_state=42)
            anomalies = detector.fit_predict(data) == -1
        
        elif method == 'local_outlier_factor':
            from sklearn.neighbors import LocalOutlierFactor
            detector = LocalOutlierFactor(contamination=0.1)
            anomalies = detector.fit_predict(data) == -1
        
        elif method == 'statistical':
            # Mahalanobis distance based detection
            mean = np.mean(data, axis=0)
            cov = np.cov(data.T)
            try:
                inv_cov = np.linalg.inv(cov)
                mahal_dist = np.array([
                    np.sqrt((x - mean).T @ inv_cov @ (x - mean))
                    for x in data.values
                ])
                threshold = np.percentile(mahal_dist, 90)
                anomalies = mahal_dist > threshold
            except np.linalg.LinAlgError:
                # Fallback to z-score method
                z_scores = np.abs(stats.zscore(data))
                anomalies = (z_scores > 3).any(axis=1)
        
        else:
            raise ValueError(f"Unknown anomaly detection method: {method}")
        
        return anomalies


class DataProcessor:
    """Main data processing class for pollution source apportionment."""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize data processor.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager or get_config_manager()
        self.validator = DataValidator(self.config)
        self.logger = logging.getLogger(__name__)
        
        self.scaler = None
        self.imputer = None
        self.quality_reports = {}
    
    def load_data(self, data_dir: Optional[str] = None) -> Dict[str, pd.DataFrame]:
        """
        Load all datasets with comprehensive validation.
        
        Args:
            data_dir: Data directory path (optional)
            
        Returns:
            Dictionary of loaded and validated datasets
        """
        if data_dir is None:
            data_dir = self.config.get_data_config().data_dir
        
        data_config = self.config.get_data_config()
        datasets = {}
        
        # Define file mappings
        file_mappings = {
            'soil': data_config.soil_file,
            'atmosphere': data_config.atmosphere_file,
            'irrigation': data_config.irrigation_file,
            'pesticide': data_config.pesticide_file,
            'manure': data_config.manure_file,
            'industry': data_config.industry_file
        }
        
        for dataset_name, filename in file_mappings.items():
            file_path = Path(data_dir) / filename
            
            if file_path.exists():
                try:
                    df = pd.read_csv(file_path, encoding='utf-8')
                    
                    # Basic validation
                    if df.empty:
                        self.logger.warning(f"Empty dataset: {dataset_name}")
                        continue
                    
                    # Validate and clean data
                    df_cleaned = self._clean_dataset(df, dataset_name)
                    
                    # Quality assessment
                    quality_report = self.validator.validate_dataset(df_cleaned, dataset_name)
                    self.quality_reports[dataset_name] = quality_report
                    
                    # Log quality issues
                    if quality_report.recommendations:
                        self.logger.warning(f"Data quality issues in {dataset_name}:")
                        for rec in quality_report.recommendations:
                            self.logger.warning(f"  - {rec}")
                    
                    datasets[dataset_name] = df_cleaned
                    self.logger.info(f"Loaded {dataset_name}: {df_cleaned.shape}")
                    
                except Exception as e:
                    self.logger.error(f"Failed to load {dataset_name}: {e}")
            else:
                self.logger.warning(f"File not found: {file_path}")
        
        return datasets
    
    def _clean_dataset(self, df: pd.DataFrame, dataset_name: str) -> pd.DataFrame:
        """
        Clean and preprocess individual dataset.
        
        Args:
            df: Raw DataFrame
            dataset_name: Name of the dataset
            
        Returns:
            Cleaned DataFrame
        """
        df_clean = df.copy()
        metals = self.config.get_data_config().metals
        
        # Convert metal columns to numeric
        for metal in metals:
            if metal in df_clean.columns:
                df_clean[metal] = pd.to_numeric(df_clean[metal], errors='coerce')
        
        # Handle negative values (set to NaN)
        for metal in metals:
            if metal in df_clean.columns:
                df_clean.loc[df_clean[metal] < 0, metal] = np.nan
        
        # Remove rows with all NaN metal values
        metal_cols = [col for col in metals if col in df_clean.columns]
        if metal_cols:
            df_clean = df_clean.dropna(subset=metal_cols, how='all')
        
        # Validate coordinates if present
        coord_cols = self.config.get_data_config().coord_columns
        for coord_col in coord_cols:
            if coord_col in df_clean.columns:
                df_clean[coord_col] = pd.to_numeric(df_clean[coord_col], errors='coerce')
        
        return df_clean
    
    def preprocess_data(self, datasets: Dict[str, pd.DataFrame], 
                       impute_missing: bool = True,
                       scale_data: bool = True) -> Dict[str, pd.DataFrame]:
        """
        Preprocess datasets for analysis.
        
        Args:
            datasets: Dictionary of datasets
            impute_missing: Whether to impute missing values
            scale_data: Whether to scale the data
            
        Returns:
            Preprocessed datasets
        """
        processed_datasets = {}
        metals = self.config.get_data_config().metals
        
        for name, df in datasets.items():
            df_processed = df.copy()
            
            # Get metal columns that exist in this dataset
            available_metals = [col for col in metals if col in df_processed.columns]
            
            if available_metals:
                # Impute missing values
                if impute_missing:
                    if self.imputer is None:
                        self.imputer = KNNImputer(n_neighbors=5)
                        # Fit on all available data
                        all_metal_data = []
                        for dataset in datasets.values():
                            dataset_metals = [col for col in metals if col in dataset.columns]
                            if dataset_metals:
                                all_metal_data.append(dataset[dataset_metals])
                        
                        if all_metal_data:
                            combined_data = pd.concat(all_metal_data, ignore_index=True)
                            self.imputer.fit(combined_data)
                    
                    # Transform current dataset
                    imputed_data = self.imputer.transform(df_processed[available_metals])
                    df_processed[available_metals] = imputed_data
                
                # Scale data
                if scale_data:
                    if self.scaler is None:
                        self.scaler = RobustScaler()  # More robust to outliers
                        # Fit on all available data
                        all_metal_data = []
                        for dataset in datasets.values():
                            dataset_metals = [col for col in metals if col in dataset.columns]
                            if dataset_metals:
                                all_metal_data.append(dataset[dataset_metals])
                        
                        if all_metal_data:
                            combined_data = pd.concat(all_metal_data, ignore_index=True)
                            self.scaler.fit(combined_data)
                    
                    # Transform current dataset
                    scaled_data = self.scaler.transform(df_processed[available_metals])
                    df_processed[available_metals] = scaled_data
            
            processed_datasets[name] = df_processed
            self.logger.info(f"Preprocessed {name}: {df_processed.shape}")
        
        return processed_datasets
    
    def get_quality_report(self, dataset_name: str) -> Optional[DataQualityReport]:
        """Get quality report for a specific dataset."""
        return self.quality_reports.get(dataset_name)
    
    def export_quality_reports(self, output_dir: Optional[str] = None) -> None:
        """Export all quality reports to JSON files."""
        if output_dir is None:
            output_dir = self.config.get_data_config().output_dir
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        for dataset_name, report in self.quality_reports.items():
            report_file = output_path / f"{dataset_name}_quality_report.json"
            
            import json
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report.to_dict(), f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Exported quality report: {report_file}")
