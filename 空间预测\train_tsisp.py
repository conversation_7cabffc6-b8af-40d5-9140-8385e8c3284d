#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
TSISP Single-Metal Spatial Prediction Training Module

This module implements a flexible single-metal training approach for the TSISP
(Transformer-based Spatial Interpolation for Soil Pollution) model, supporting
3D spatial prediction with depth information.

Key Features:
- Single-metal training for improved model focus and performance
- Flexible metal selection via configuration or command-line
- Batch training support for sequential multi-metal model training
- 3D spatial prediction (x, y, depth) with transformer architecture
- Publication-quality visualizations and comprehensive error handling
- Robust data preprocessing and validation

Usage Examples:

1. Single Metal Training (Programmatic):
    ```python
    from train_tsisp import Config, train_model

    Config.set_target_metal('Pb')
    model, best_epoch = train_model('data.csv', 'output_dir')
    ```

2. Batch Training (Programmatic):
    ```python
    from train_tsisp import train_multiple_metals

    models = train_multiple_metals('data.csv', 'batch_output', ['Pb', 'Cd', 'Cu'])
    ```

3. Command Line Usage:
    ```bash
    # Single metal
    python train_tsisp.py --metal Pb --output pb_model

    # Batch training
    python train_tsisp.py --batch --metals Pb Cd Cu --output batch_models
    ```

Author: TSISP Development Team
Version: 2.0 (Single-Metal Refactor)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import GroupShuffleSplit
import pandas as pd
import numpy as np
import os
import warnings
import matplotlib.pyplot as plt
from scipy.spatial import cKDTree
from torch.utils.data.dataset import ConcatDataset
import logging
import traceback
from typing import Dict, List, Tuple, Optional, Union
from scipy.spatial.distance import pdist, squareform
from sklearn.neighbors import NearestNeighbors
# Configure logging for better error tracking
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tsisp_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# PyTorch Geometric imports with enhanced compatibility
TORCH_GEOMETRIC_AVAILABLE = False
KNN_GRAPH_AVAILABLE = False
RADIUS_GRAPH_AVAILABLE = False
knn_graph = None
radius_graph = None

try:
    import torch_geometric
    from torch_geometric.nn import GCNConv, GATConv, global_mean_pool
    from torch_geometric.data import Data, Batch
    TORCH_GEOMETRIC_AVAILABLE = True
    logger.info(f"PyTorch Geometric {torch_geometric.__version__} detected")

    # 尝试导入图构建函数 - 多种方法
    import_success = False

    # 方法1: 从torch_geometric.utils导入
    if not import_success:
        try:
            from torch_geometric.utils import knn_graph, radius_graph
            KNN_GRAPH_AVAILABLE = True
            RADIUS_GRAPH_AVAILABLE = True
            import_success = True
            logger.info("✓ 图构建函数从torch_geometric.utils导入成功")
        except ImportError:
            logger.debug("无法从torch_geometric.utils导入图构建函数")

    # 方法2: 从torch_geometric.nn导入
    if not import_success:
        try:
            from torch_geometric.nn import knn_graph, radius_graph
            KNN_GRAPH_AVAILABLE = True
            RADIUS_GRAPH_AVAILABLE = True
            import_success = True
            logger.info("✓ 图构建函数从torch_geometric.nn导入成功")
        except ImportError:
            logger.debug("无法从torch_geometric.nn导入图构建函数")

    # 方法3: 尝试从torch_cluster导入（这是警告的根源）
    if not import_success:
        try:
            import torch_cluster
            from torch_cluster import knn_graph, radius_graph
            KNN_GRAPH_AVAILABLE = True
            RADIUS_GRAPH_AVAILABLE = True
            import_success = True
            logger.info("✓ 图构建函数从torch_cluster导入成功")
        except ImportError:
            logger.warning("torch_cluster不可用 - 这是'knn_graph requires torch-cluster'警告的原因")
            logger.info("建议安装: pip install torch-cluster")

    # 如果所有导入都失败，禁用PyTorch Geometric功能
    if not import_success:
        logger.warning("所有PyTorch Geometric图构建函数导入失败，使用fallback实现")
        TORCH_GEOMETRIC_AVAILABLE = False
    else:
        # 测试函数可用性
        try:
            test_coords = torch.randn(5, 3)
            test_edge_index = knn_graph(test_coords, k=2)
            test_edge_index = radius_graph(test_coords, r=1.0)
            logger.info("✓ PyTorch Geometric图构建函数测试通过")
        except Exception as e:
            logger.warning(f"PyTorch Geometric图构建函数测试失败: {e}")
            logger.warning("将使用fallback实现")
            KNN_GRAPH_AVAILABLE = False
            RADIUS_GRAPH_AVAILABLE = False

except ImportError:
    TORCH_GEOMETRIC_AVAILABLE = False
    logger.warning("PyTorch Geometric未安装，使用fallback图构建实现")

# 状态报告
if TORCH_GEOMETRIC_AVAILABLE and KNN_GRAPH_AVAILABLE and RADIUS_GRAPH_AVAILABLE:
    logger.info("🚀 PyTorch Geometric图构建功能完全可用")
elif TORCH_GEOMETRIC_AVAILABLE:
    logger.warning("⚠️ PyTorch Geometric已安装但图构建函数不可用")
    logger.info("💡 建议安装torch-cluster: pip install torch-cluster")
    logger.info("📦 将使用fallback实现（功能完整）")
else:
    logger.info("📦 使用fallback图构建实现（功能完整）")

warnings.filterwarnings('ignore')  # 忽略警告信息

# ====================== Reproducibility Setup ======================
def set_random_seeds(seed: int = 42):
    """Set random seeds for reproducible results across all libraries."""
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    logger.info(f"Random seeds set to {seed} for reproducibility")


# ====================== 模型全局配置 ======================
class Config:
    # 优化的核心参数 - 基于数据集大小调整
    mask_ratio = 0.7  # 使用70%的点作为已知点（更保守）
    known_points_min = 10  # 降低最小点数要求（适应小场地）

    # 优化的空间编码 - 减少过拟合风险
    fourier_bands = 16  # 适中的频带数量
    fourier_max_freq = 10.0  # 适中的最大频率
    embedding_dim = 32  # 减少嵌入维度

    # 优化的Transformer架构 - 适合空间数据
    transformer_dim = 256  # 减少特征维度
    transformer_depth = 6  # 减少层数（避免梯度消失）
    transformer_heads = 8  # 减少注意力头数
    mlp_dim = 512  # 减少前馈网络维度
    dropout = 0.1  # 适中的正则化

    # 优化的训练参数
    batch_size = 16  # 实际按场地处理，此参数用于内存管理
    learning_rate = 1e-4  # 提高学习率（适合较浅模型）
    epochs = 150  # 减少训练轮次（防止过拟合）
    warmup_epochs = 15  # 添加学习率预热

    # 早停参数
    patience = 20  # 早停耐心值
    min_delta = 1e-4  # 最小改进阈值

    # 优化的损失函数权重
    hotspot_threshold = 0.85  # 降低热点阈值（更多样本）
    alpha = 1.5  # 热点损失权重
    beta = 0.8  # 梯度损失权重
    gamma = 0.3  # 克里金损失权重

    # ====================== SINGLE METAL CONFIGURATION ======================
    # Available metals in the dataset
    available_metals = ['Pb', 'Cd', 'Cu', 'Ni', 'Cr', 'Hg', 'As', 'Zn']

    # Target metal for single-metal training (can be set via set_target_metal())
    target_metal = 'Pb'  # Default metal

    # Other attributes remain the same
    point_attributes = ['organic', 'depth']
    site_attributes = ['industry', 'emission', 'area']

    # 数据增强参数
    noise_std = 0.01  # 坐标噪声标准差
    augment_prob = 0.3  # 数据增强概率

    # ====================== 创新架构配置 ======================
    # 架构选择：'transformer' 或 'geospatial_gnn'
    architecture = 'geospatial_gnn'  # 默认使用创新架构

    # GeoSpatial-GNN 参数
    gnn_hidden_dim = 256  # GNN隐藏层维度（增大以容纳所有特征）
    gnn_num_layers = 4  # GNN层数
    gnn_heads = 4  # 图注意力头数
    graph_k_neighbors = 8  # K近邻图构建参数
    graph_radius = 50.0  # 半径图构建参数（米）

    # 多尺度卷积参数
    conv_channels = [64, 128, 256]  # 多尺度卷积通道数
    conv_kernel_sizes = [3, 5, 7]  # 不同尺度的卷积核

    # 物理约束参数
    physics_weight = 0.1  # 物理约束损失权重
    diffusion_coeff = 1e-6  # 扩散系数（m²/s）

    # 不确定性量化参数
    enable_uncertainty = True  # 是否启用不确定性量化
    mc_dropout_samples = 10  # Monte Carlo Dropout采样次数

    # 学习式空间编码参数（确保维度兼容性）
    learnable_encoding = True  # 是否使用学习式编码
    encoding_dim = 72  # 学习式编码维度（72 = 3*2*3*4，确保能被多尺度参数整除）
    multi_scale_levels = 3  # 多尺度级别数

    @classmethod
    def set_target_metal(cls, metal: str):
        """
        Set the target metal for single-metal training.

        Args:
            metal: Metal symbol (e.g., 'Pb', 'Cd', 'Cu', etc.)

        Raises:
            ValueError: If metal is not in available_metals list
        """
        if metal not in cls.available_metals:
            raise ValueError(f"Metal '{metal}' not in available metals: {cls.available_metals}")
        cls.target_metal = metal
        logger.info(f"Target metal set to: {metal}")

    @classmethod
    def get_target_metal_column(cls):
        """Get the column name for the target metal concentration."""
        return f"concentration_{cls.target_metal}"

    @classmethod
    def get_available_metals_in_data(cls, data_columns):
        """
        Get list of metals that are actually present in the dataset.

        Args:
            data_columns: List of column names from the dataset

        Returns:
            List of metal symbols that have concentration columns in the data
        """
        present_metals = []
        for metal in cls.available_metals:
            if f"concentration_{metal}" in data_columns:
                present_metals.append(metal)
        return present_metals


# ====================== 数据预处理 ======================
class SoilDataPreprocessor:
    def __init__(self, csv_path: str):
        """
        Initialize the soil data preprocessor with robust error handling.

        Args:
            csv_path: Path to the CSV file containing soil data

        Raises:
            FileNotFoundError: If CSV file doesn't exist
            ValueError: If required columns are missing
            pd.errors.EmptyDataError: If CSV file is empty
        """
        try:
            if not os.path.exists(csv_path):
                raise FileNotFoundError(f"CSV file not found: {csv_path}")

            self.data = pd.read_csv(csv_path)
            logger.info(f"Successfully loaded data from {csv_path}")
            logger.info(f"Data shape: {self.data.shape}")

        except pd.errors.EmptyDataError:
            raise ValueError(f"CSV file is empty: {csv_path}")
        except Exception as e:
            logger.error(f"Error loading CSV file: {e}")
            raise

        self.scalers = {}
        self.encoders = {}
        self.site_mappings = {}
        self.site_coord_origins = {}

        # Validate required columns
        self._validate_data_structure()

        # 记录原始深度值，因为需要保存反标准化
        self.original_depths = self.data['depth'].copy()

        # 为了支持不同深度的采样点，我们需要一个唯一的位置ID
        self.data['position_id'] = self.data['x'].astype(str) + '_' + self.data['y'].astype(str)

        # 对每个位置计算深度范围（用于后续深度标准化）
        try:
            depth_stats = self.data.groupby('position_id')['depth'].agg(['min', 'max'])
            self.data = self.data.merge(depth_stats, on='position_id', suffixes=('', '_stats'))
        except Exception as e:
            logger.error(f"Error processing depth statistics: {e}")
            raise ValueError(f"Failed to process depth data: {e}")

    def _validate_data_structure(self):
        """Validate that the data contains all required columns and reasonable values."""
        required_columns = ['site_name', 'x', 'y', 'depth', 'industry', 'emission', 'area']
        required_columns.extend(Config.point_attributes)

        # Check for target metal column
        target_metal_col = Config.get_target_metal_column()
        required_columns.append(target_metal_col)

        missing_columns = [col for col in required_columns if col not in self.data.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")

        # Log available metals in dataset
        available_metals = Config.get_available_metals_in_data(self.data.columns)
        logger.info(f"Available metals in dataset: {available_metals}")
        logger.info(f"Training target metal: {Config.target_metal}")

        # 检查和处理NaN值
        self._check_and_handle_nan_values()

        # Validate data ranges
        if self.data['depth'].isnull().any():
            logger.warning("Found null values in depth column")

        if (self.data['depth'] < 0).any():
            raise ValueError("Depth values cannot be negative")

        # Check for reasonable coordinate ranges
        if self.data['x'].isnull().any() or self.data['y'].isnull().any():
            raise ValueError("Coordinate columns cannot contain null values")

        # Validate target metal concentration
        if target_metal_col in self.data.columns:
            if (self.data[target_metal_col] < 0).any():
                logger.warning(f"Found negative concentrations for {Config.target_metal}")

            # Check for sufficient non-zero values
            non_zero_count = (self.data[target_metal_col] > 0).sum()
            total_count = len(self.data)
            logger.info(f"Target metal {Config.target_metal}: {non_zero_count}/{total_count} non-zero values")

            if non_zero_count < total_count * 0.1:
                logger.warning(f"Very few non-zero values for {Config.target_metal} ({non_zero_count}/{total_count})")

        logger.info("Data structure validation passed")

    def _check_and_handle_nan_values(self):
        """增强的NaN检查和处理方法"""
        logger.info("开始全面的NaN值检查和处理...")

        # 第一步：全面的NaN诊断
        self._diagnose_nan_values()

        # 第二步：处理关键列的NaN值
        self._handle_critical_columns_nan()

        # 第三步：处理其他列的NaN值
        self._handle_other_columns_nan()

        # 第四步：最终验证和强制清理
        self._final_nan_validation_and_cleanup()

        logger.info("NaN值处理完成")

    def _diagnose_nan_values(self):
        """详细诊断数据中的NaN值分布"""
        logger.info("=" * 50)
        logger.info("NaN值诊断报告")
        logger.info("=" * 50)

        total_rows = len(self.data)
        total_cells = self.data.size
        total_nan = self.data.isnull().sum().sum()

        logger.info(f"数据概况: {total_rows} 行, {len(self.data.columns)} 列, {total_cells} 个单元格")
        logger.info(f"总NaN数量: {total_nan} ({(total_nan/total_cells)*100:.2f}%)")

        # 按列统计NaN
        nan_by_column = {}
        for col in self.data.columns:
            nan_count = self.data[col].isnull().sum()
            if nan_count > 0:
                nan_percentage = (nan_count / total_rows) * 100
                nan_by_column[col] = {'count': nan_count, 'percentage': nan_percentage}
                logger.warning(f"列 '{col}': {nan_count} 个NaN ({nan_percentage:.2f}%)")

        # 按行统计NaN
        rows_with_nan = self.data.isnull().any(axis=1).sum()
        if rows_with_nan > 0:
            logger.warning(f"包含NaN的行数: {rows_with_nan} ({(rows_with_nan/total_rows)*100:.2f}%)")

            # 显示前几个包含NaN的行索引
            nan_row_indices = self.data[self.data.isnull().any(axis=1)].index.tolist()[:10]
            logger.info(f"包含NaN的行索引示例: {nan_row_indices}")

        return nan_by_column

    def _handle_critical_columns_nan(self):
        """处理关键列的NaN值"""
        logger.info("处理关键列的NaN值...")

        # 定义关键列
        critical_columns = ['x', 'y', 'depth']
        target_metal_col = Config.get_target_metal_column()
        if target_metal_col:
            critical_columns.append(target_metal_col)

        for col in critical_columns:
            if col not in self.data.columns:
                logger.warning(f"关键列 '{col}' 不存在于数据中")
                continue

            nan_count = self.data[col].isnull().sum()
            if nan_count == 0:
                continue

            logger.info(f"处理列 '{col}' 的 {nan_count} 个NaN值...")

            # 获取非NaN值用于统计
            non_nan_values = self.data[col].dropna()

            if len(non_nan_values) == 0:
                # 整列都是NaN的极端情况
                logger.error(f"列 '{col}' 完全为NaN值！")
                if col in ['x', 'y']:
                    # 坐标列使用默认值
                    default_value = 0.0
                    self.data[col] = default_value
                    logger.warning(f"列 '{col}' 使用默认值 {default_value} 填充")
                elif col == 'depth':
                    # 深度列使用默认值
                    default_value = 1.0
                    self.data[col] = default_value
                    logger.warning(f"列 '{col}' 使用默认值 {default_value} 填充")
                elif col == target_metal_col:
                    # 浓度列使用0
                    self.data[col] = 0.0
                    logger.warning(f"列 '{col}' 使用0填充（未检出）")
                continue

            # 根据列类型选择填充策略
            if col in ['x', 'y']:
                # 坐标列：优先使用均值，如果均值为NaN则使用中位数
                fill_value = non_nan_values.mean()
                if pd.isna(fill_value):
                    fill_value = non_nan_values.median()
                if pd.isna(fill_value):
                    fill_value = 0.0  # 最后的fallback

                self.data[col].fillna(fill_value, inplace=True)
                logger.info(f"列 '{col}' 用均值 {fill_value:.3f} 填充")

            elif col == 'depth':
                # 深度列：优先使用中位数
                fill_value = non_nan_values.median()
                if pd.isna(fill_value):
                    fill_value = non_nan_values.mean()
                if pd.isna(fill_value):
                    fill_value = 1.0  # 默认深度

                self.data[col].fillna(fill_value, inplace=True)
                logger.info(f"列 '{col}' 用中位数 {fill_value:.3f} 填充")

            elif col == target_metal_col:
                # 浓度列：使用0（未检出）
                self.data[col].fillna(0.0, inplace=True)
                logger.info(f"列 '{col}' 用0填充（未检出）")

            # 验证填充结果
            remaining_nan = self.data[col].isnull().sum()
            if remaining_nan > 0:
                logger.error(f"列 '{col}' 填充后仍有 {remaining_nan} 个NaN值！")
                # 强制填充
                if col in ['x', 'y']:
                    self.data[col] = self.data[col].fillna(0.0)
                elif col == 'depth':
                    self.data[col] = self.data[col].fillna(1.0)
                else:
                    self.data[col] = self.data[col].fillna(0.0)
                logger.warning(f"列 '{col}' 强制填充完成")

    def _handle_other_columns_nan(self):
        """处理其他列的NaN值"""
        logger.info("处理其他列的NaN值...")

        # 获取所有数值列
        numeric_columns = self.data.select_dtypes(include=[np.number]).columns
        critical_columns = ['x', 'y', 'depth', Config.get_target_metal_column()]

        for col in numeric_columns:
            if col in critical_columns:
                continue  # 关键列已经处理过

            nan_count = self.data[col].isnull().sum()
            if nan_count == 0:
                continue

            logger.info(f"处理数值列 '{col}' 的 {nan_count} 个NaN值...")

            non_nan_values = self.data[col].dropna()
            if len(non_nan_values) == 0:
                # 整列都是NaN
                self.data[col] = 0.0
                logger.warning(f"列 '{col}' 完全为NaN，使用0填充")
                continue

            # 根据列名选择填充策略
            if col in ['organic']:
                # 有机质：使用均值
                fill_value = non_nan_values.mean()
                if pd.isna(fill_value):
                    fill_value = 0.0
            elif col in ['emission', 'area']:
                # 场地属性：使用中位数
                fill_value = non_nan_values.median()
                if pd.isna(fill_value):
                    fill_value = non_nan_values.mean()
                if pd.isna(fill_value):
                    fill_value = 0.0
            else:
                # 其他列：使用中位数
                fill_value = non_nan_values.median()
                if pd.isna(fill_value):
                    fill_value = 0.0

            self.data[col].fillna(fill_value, inplace=True)
            logger.info(f"列 '{col}' 用 {fill_value:.3f} 填充")

        # 处理非数值列的NaN
        non_numeric_columns = self.data.select_dtypes(exclude=[np.number]).columns
        for col in non_numeric_columns:
            nan_count = self.data[col].isnull().sum()
            if nan_count > 0:
                logger.info(f"处理非数值列 '{col}' 的 {nan_count} 个NaN值...")

                if col == 'industry':
                    # 行业列：使用最频繁的值
                    mode_value = self.data[col].mode()
                    if len(mode_value) > 0:
                        fill_value = mode_value[0]
                    else:
                        fill_value = 'unknown'
                    self.data[col].fillna(fill_value, inplace=True)
                    logger.info(f"列 '{col}' 用最频值 '{fill_value}' 填充")
                else:
                    # 其他非数值列：使用'unknown'
                    self.data[col].fillna('unknown', inplace=True)
                    logger.info(f"列 '{col}' 用 'unknown' 填充")

    def _final_nan_validation_and_cleanup(self):
        """最终的NaN验证和强制清理"""
        logger.info("执行最终NaN验证...")

        # 检查所有列的NaN情况
        total_nan = self.data.isnull().sum().sum()

        if total_nan > 0:
            logger.warning(f"发现 {total_nan} 个残留的NaN值，执行强制清理...")

            # 详细报告哪些列还有NaN
            for col in self.data.columns:
                nan_count = self.data[col].isnull().sum()
                if nan_count > 0:
                    logger.error(f"列 '{col}' 仍有 {nan_count} 个NaN值")

                    # 强制清理策略
                    if self.data[col].dtype in ['float64', 'int64', 'float32', 'int32']:
                        # 数值列用0填充
                        self.data[col] = self.data[col].fillna(0.0)
                        logger.warning(f"强制用0填充列 '{col}'")
                    else:
                        # 非数值列用字符串填充
                        self.data[col] = self.data[col].fillna('unknown')
                        logger.warning(f"强制用'unknown'填充列 '{col}'")

        # 最终验证
        final_nan_count = self.data.isnull().sum().sum()

        if final_nan_count > 0:
            # 如果还有NaN，提供详细的调试信息
            logger.error("=" * 50)
            logger.error("最终NaN清理失败！详细信息：")
            logger.error("=" * 50)

            for col in self.data.columns:
                nan_count = self.data[col].isnull().sum()
                if nan_count > 0:
                    logger.error(f"列 '{col}': {nan_count} 个NaN值")
                    # 显示NaN值的位置
                    nan_indices = self.data[self.data[col].isnull()].index.tolist()[:5]
                    logger.error(f"  NaN位置示例: {nan_indices}")

                    # 显示列的数据类型和统计信息
                    logger.error(f"  数据类型: {self.data[col].dtype}")
                    logger.error(f"  非NaN值数量: {self.data[col].count()}")

            # 最后的强制措施：删除包含NaN的行
            original_rows = len(self.data)
            self.data = self.data.dropna()
            dropped_rows = original_rows - len(self.data)

            if dropped_rows > 0:
                logger.warning(f"删除了 {dropped_rows} 行包含NaN的数据")
                logger.warning(f"剩余数据: {len(self.data)} 行")

                if len(self.data) == 0:
                    raise ValueError("所有数据行都包含NaN值，无法继续处理")
            else:
                raise ValueError(f"无法清理的NaN值: {final_nan_count} 个")

        logger.info(f"✅ NaN值清理完成，数据形状: {self.data.shape}")
        logger.info(f"✅ 最终验证: 0 个NaN值")

    def preprocess(self):
        # 场地名称映射
        unique_sites = self.data['site_name'].unique()
        self.site_mappings = {site: idx for idx, site in enumerate(unique_sites)}
        self.data['site_id'] = self.data['site_name'].map(self.site_mappings)

        # 创建独立空间坐标系 - 关键步骤（添加NaN安全检查）
        logger.info("开始局部坐标系转换...")

        for site_id in self.data['site_id'].unique():
            if pd.isna(site_id):
                logger.warning("发现NaN的site_id，跳过")
                continue

            site_mask = self.data['site_id'] == site_id
            site_data = self.data.loc[site_mask]

            # 计算场地最小坐标作为原点（NaN安全）
            min_x = site_data['x'].min()
            min_y = site_data['y'].min()

            # 检查计算结果是否为NaN
            if pd.isna(min_x) or pd.isna(min_y):
                logger.warning(f"场地 {site_id} 的坐标计算产生NaN值")
                logger.warning(f"  min_x: {min_x}, min_y: {min_y}")

                # 使用非NaN值重新计算
                valid_x = site_data['x'].dropna()
                valid_y = site_data['y'].dropna()

                min_x = valid_x.min() if len(valid_x) > 0 else 0.0
                min_y = valid_y.min() if len(valid_y) > 0 else 0.0

                if pd.isna(min_x):
                    min_x = 0.0
                if pd.isna(min_y):
                    min_y = 0.0

                logger.info(f"  修正后 min_x: {min_x}, min_y: {min_y}")

            # 存储原点坐标
            self.site_coord_origins[site_id] = (min_x, min_y)

            # 转换全局坐标为场区局部坐标（NaN安全）
            try:
                local_x = site_data['x'] - min_x
                local_y = site_data['y'] - min_y

                # 检查转换结果
                if local_x.isnull().any() or local_y.isnull().any():
                    logger.warning(f"场地 {site_id} 的局部坐标转换产生NaN值")
                    local_x = local_x.fillna(0.0)
                    local_y = local_y.fillna(0.0)

                self.data.loc[site_mask, 'local_x'] = local_x
                self.data.loc[site_mask, 'local_y'] = local_y

            except Exception as e:
                logger.error(f"场地 {site_id} 坐标转换失败: {e}")
                # 使用原坐标作为局部坐标
                self.data.loc[site_mask, 'local_x'] = site_data['x'].fillna(0.0)
                self.data.loc[site_mask, 'local_y'] = site_data['y'].fillna(0.0)

        # 验证局部坐标转换结果
        local_x_nan = self.data['local_x'].isnull().sum()
        local_y_nan = self.data['local_y'].isnull().sum()

        if local_x_nan > 0 or local_y_nan > 0:
            logger.error(f"局部坐标转换后仍有NaN: local_x({local_x_nan}), local_y({local_y_nan})")
            # 强制填充
            self.data['local_x'] = self.data['local_x'].fillna(0.0)
            self.data['local_y'] = self.data['local_y'].fillna(0.0)
            logger.warning("强制用0填充局部坐标的NaN值")

        logger.info("局部坐标系转换完成")

        # 添加位置编码和深度统计（之前缺失的步骤）
        logger.info("添加位置编码...")
        try:
            # 使用局部坐标进行位置编码
            self.data['position_id'] = self.data.groupby(['local_x', 'local_y']).ngroup()
        except Exception as e:
            logger.error(f"位置编码失败: {e}")
            # 使用行索引作为fallback
            self.data['position_id'] = range(len(self.data))
            logger.warning("使用行索引作为position_id")

        # 保存原始深度值
        self.original_depths = self.data['depth'].copy()

        # 计算每个位置的深度统计
        logger.info("计算深度统计...")
        try:
            depth_stats = self.data.groupby('position_id')['depth'].agg(['min', 'max']).reset_index()
            self.data = self.data.merge(depth_stats, on='position_id', how='left')

            # 检查合并后是否产生NaN
            min_nan = self.data['min'].isnull().sum()
            max_nan = self.data['max'].isnull().sum()

            if min_nan > 0 or max_nan > 0:
                logger.warning(f"深度统计合并产生NaN: min({min_nan}), max({max_nan})")
                # 用当前深度填充
                self.data['min'] = self.data['min'].fillna(self.data['depth'])
                self.data['max'] = self.data['max'].fillna(self.data['depth'])

        except Exception as e:
            logger.error(f"深度统计计算失败: {e}")
            # 手动添加min/max列
            self.data['min'] = self.data['depth']
            self.data['max'] = self.data['depth']
            logger.warning("使用当前深度作为min/max值")

        # 深度标准化（使用位置相关的深度统计）- 添加NaN检测
        # 检查深度相关列的NaN值
        depth_related_cols = ['depth', 'min', 'max']
        for col in depth_related_cols:
            if col in self.data.columns:
                nan_count = self.data[col].isnull().sum()
                if nan_count > 0:
                    logger.warning(f"深度相关列 '{col}' 包含 {nan_count} 个NaN值")
                    if col == 'depth':
                        # 深度列用中位数填充
                        median_depth = self.data[col].median()
                        self.data[col].fillna(median_depth, inplace=True)
                        logger.info(f"用中位数 {median_depth:.3f} 填充深度NaN值")
                    else:
                        # min/max列用相应的统计值填充
                        self.data[col].fillna(self.data[col].median(), inplace=True)

        # 相对深度：将深度映射到0-1范围内，0表示最浅，1表示最深
        depth_range = self.data['max'] - self.data['min']

        # 检查depth_range中的NaN和无效值
        if np.isnan(depth_range).any():
            logger.warning("深度范围计算产生NaN值")
            depth_range = np.nan_to_num(depth_range, nan=1.0)

        # Robust handling of zero depth range
        depth_range = np.where(depth_range < 1e-6, 1.0, depth_range)

        rel_depth = (self.data['depth'] - self.data['min']) / depth_range

        # 检查相对深度计算结果
        if np.isnan(rel_depth).any():
            logger.warning("相对深度计算产生NaN值")
            rel_depth = np.nan_to_num(rel_depth, nan=0.0)

        self.data['rel_depth'] = rel_depth

        # 绝对深度标准化 - 添加NaN检测
        try:
            self.scalers['depth'] = StandardScaler()
            depth_values = self.data[['depth']].values

            # 检查标准化前的数据
            if np.isnan(depth_values).any():
                logger.error("深度标准化前发现NaN值")
                depth_values = np.nan_to_num(depth_values, nan=0.0)

            scaled_depth = self.scalers['depth'].fit_transform(depth_values)

            # 检查标准化后的数据
            if np.isnan(scaled_depth).any():
                logger.error("深度标准化产生NaN值")
                scaled_depth = np.nan_to_num(scaled_depth, nan=0.0)

            self.data['depth'] = scaled_depth.flatten()

        except Exception as e:
            logger.error(f"深度标准化失败: {e}")
            logger.warning("使用原始深度值")
            # 如果标准化失败，使用简单的归一化
            min_depth = self.data['depth'].min()
            max_depth = self.data['depth'].max()
            if max_depth > min_depth:
                self.data['depth'] = (self.data['depth'] - min_depth) / (max_depth - min_depth)
            else:
                self.data['depth'] = 0.0

        # 使用标准化后的深度替换原始的depth列
        self.data['depth_std'] = self.data['depth']
        self.data['depth'] = self.original_depths  # 保留原始深度值

        # 行业类别编码
        self.encoders['industry'] = LabelEncoder()
        self.data['industry'] = self.encoders['industry'].fit_transform(self.data['industry'])
        unique_industries = self.data['industry'].unique()
        self.num_industries = len(unique_industries) + 1  # +1为未知类别预留

        # 数值属性标准化
        for attr in ['emission', 'area']:
            if attr in self.data.columns:
                self.scalers[attr] = StandardScaler()
                self.data[attr] = self.scalers[attr].fit_transform(self.data[[attr]])

        # 点级属性标准化（除深度外）
        for attr in Config.point_attributes:
            if attr != 'depth' and attr in self.data.columns:
                self.scalers[attr] = StandardScaler()
                self.data[attr] = self.scalers[attr].fit_transform(self.data[[attr]])

        # 目标金属转换 (single metal)
        target_metal_col = Config.get_target_metal_column()
        if target_metal_col in self.data.columns:
            # 添加小常数避免log(0)
            original_values = self.data[target_metal_col].copy()
            self.data[target_metal_col] = np.log1p(self.data[target_metal_col])
            logger.info(f"Applied log1p transformation to {Config.target_metal}")
            logger.info(f"Concentration range: {original_values.min():.3f} - {original_values.max():.3f}")
            logger.info(f"Transformed range: {self.data[target_metal_col].min():.3f} - {self.data[target_metal_col].max():.3f}")
        else:
            raise ValueError(f"Target metal column {target_metal_col} not found in data")

    def get_preprocessed_data(self):
        return self.data

    def save_scalers(self, path):
        # 保存额外的深度相关信息
        depth_info = {
            'original_depths': self.original_depths.values if hasattr(self.original_depths,
                                                                      'values') else self.original_depths,
            'depth_stats': self.data.groupby('position_id')['depth'].agg(['min', 'max']).to_dict(orient='index')
        }

        torch.save({
            'scalers': self.scalers,
            'encoders': self.encoders,
            'site_mappings': self.site_mappings,
            'site_coord_origins': self.site_coord_origins,
            'num_industries': self.num_industries,
            'depth_info': depth_info  # 保存深度相关信息
        }, path)


# ====================== 数据集类 ======================
class SoilMultiSiteDataset(Dataset):
    """代表一个完整场地的数据集（包含不同深度的采样点）"""

    def __init__(self, site_data, device=torch.device('cpu')):
        """
        site_data: DataFrame, 包含一个场地的所有点（包括不同深度）
        """
        self.data = site_data
        self.device = device

        # 使用局部坐标和深度
        self.coords = self.data[['local_x', 'local_y', 'rel_depth']].values.astype(np.float32)
        self.site_attrs = self.data[Config.site_attributes].values.astype(np.float32)

        # 点级属性包含标准化后的深度和原始深度
        self.point_attrs = self.data[Config.point_attributes].values.astype(np.float32)

        # Single metal concentration (reshape to maintain 2D structure)
        target_metal_col = Config.get_target_metal_column()
        self.metal_concs = self.data[target_metal_col].values.astype(np.float32).reshape(-1, 1)
        self.site_ids = self.data['site_id'].values

    def __len__(self):
        return 1  # 每个对象代表一个场地

    def __getitem__(self, idx):
        # 返回整个场地的数据（包括不同深度的点）
        coords = torch.tensor(self.coords, dtype=torch.float32).to(self.device)
        site_attrs = torch.tensor(self.site_attrs, dtype=torch.float32).to(self.device)
        point_attrs = torch.tensor(self.point_attrs, dtype=torch.float32).to(self.device)
        concs = torch.tensor(self.metal_concs, dtype=torch.float32).to(self.device)
        site_id = torch.tensor(self.site_ids, dtype=torch.long).to(self.device)

        return {
            'local_coordinates': coords,  # 现在包含3D信息：[x, y, 相对深度]
            'site_attributes': site_attrs,
            'point_attributes': point_attrs,
            'concentrations': concs,
            'site_id': site_id
        }


# ====================== 创新空间编码模块 ======================
class LearnableMultiScaleSpatialEncoder(nn.Module):
    """
    学习式多尺度空间编码器

    创新点：
    1. 自适应频率学习，替代固定傅里叶编码
    2. 多尺度空间特征提取
    3. 地质先验知识融入
    4. 各向异性空间建模
    """

    def __init__(self, input_dim=3, encoding_dim=64, num_scales=3):
        super().__init__()
        self.input_dim = input_dim
        self.encoding_dim = encoding_dim
        self.num_scales = num_scales

        # 计算频率特征维度，确保维度兼容性
        # 总的频率特征数 = num_scales * input_dim * freq_dim * 2 (sin + cos)
        # 为了得到期望的encoding_dim，我们需要合理分配频率维度

        # 计算每个尺度每个坐标轴的频率数量
        total_freq_features = encoding_dim - 16  # 减去地质先验特征的16维
        self.freq_dim = max(1, total_freq_features // (num_scales * input_dim * 2))

        # 实际的多尺度特征维度
        self.actual_multiscale_dim = num_scales * input_dim * self.freq_dim * 2

        # 学习式频率参数（可训练）
        self.frequency_weights = nn.Parameter(torch.randn(num_scales, input_dim, self.freq_dim))
        self.phase_shifts = nn.Parameter(torch.randn(num_scales, input_dim, self.freq_dim))

        # 各向异性变换矩阵（考虑x,y,depth的不同物理特性）
        self.anisotropic_transform = nn.Parameter(torch.eye(input_dim))

        # 地质先验嵌入网络
        self.geological_prior = nn.Sequential(
            nn.Linear(input_dim, 32),
            nn.ReLU(),
            nn.Linear(32, 16),
            nn.Tanh()
        )

        # 多尺度融合网络
        fusion_input_dim = self.actual_multiscale_dim + 16  # 多尺度特征 + 地质先验特征
        self.scale_fusion = nn.Sequential(
            nn.Linear(fusion_input_dim, encoding_dim),
            nn.LayerNorm(encoding_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )

        logger.info(f"LearnableMultiScaleSpatialEncoder initialized:")
        logger.info(f"  - encoding_dim: {encoding_dim}")
        logger.info(f"  - num_scales: {num_scales}")
        logger.info(f"  - freq_dim: {self.freq_dim}")
        logger.info(f"  - actual_multiscale_dim: {self.actual_multiscale_dim}")
        logger.info(f"  - fusion_input_dim: {self.actual_multiscale_dim + 16}")

    def forward(self, xyz):
        """
        Args:
            xyz: [B, 3] 3D坐标 (x, y, depth)
        Returns:
            encoded: [B, encoding_dim] 编码后的空间特征
        """
        batch_size = xyz.shape[0]
        device = xyz.device

        # 调试信息
        logger.debug(f"LearnableMultiScaleSpatialEncoder input shape: {xyz.shape}")

        # 各向异性变换
        xyz_transformed = torch.matmul(xyz, self.anisotropic_transform)
        logger.debug(f"After anisotropic transform: {xyz_transformed.shape}")

        # 多尺度编码
        scale_features = []
        for scale in range(self.num_scales):
            # 获取当前尺度的频率和相位
            freq = self.frequency_weights[scale]  # [3, freq_dim]
            phase = self.phase_shifts[scale]      # [3, freq_dim]

            # 计算正弦和余弦特征
            xyz_expanded = xyz_transformed.unsqueeze(-1)  # [B, 3, 1]
            freqs = xyz_expanded * freq.unsqueeze(0)      # [B, 3, freq_dim]
            phases = freqs + phase.unsqueeze(0)

            sin_features = torch.sin(phases)  # [B, 3, freq_dim]
            cos_features = torch.cos(phases)  # [B, 3, freq_dim]

            # 拼接并展平
            scale_feature = torch.cat([sin_features, cos_features], dim=-1)  # [B, 3, 2*freq_dim]
            scale_feature = scale_feature.flatten(start_dim=1)  # [B, 3 * 2 * freq_dim]
            scale_features.append(scale_feature)

            logger.debug(f"Scale {scale} feature shape: {scale_feature.shape}")

        # 多尺度特征融合
        multi_scale_features = torch.cat(scale_features, dim=-1)  # [B, num_scales * 3 * 2 * freq_dim]
        logger.debug(f"Multi-scale features shape: {multi_scale_features.shape}")

        # 地质先验特征
        geological_features = self.geological_prior(xyz)  # [B, 16]
        logger.debug(f"Geological features shape: {geological_features.shape}")

        # 最终融合
        combined_features = torch.cat([multi_scale_features, geological_features], dim=-1)
        logger.debug(f"Combined features shape: {combined_features.shape}")
        logger.debug(f"Scale fusion input dim: {combined_features.shape[-1]}, expected: {self.encoding_dim + 16}")

        encoded = self.scale_fusion(combined_features)
        logger.debug(f"Final encoded shape: {encoded.shape}")

        return encoded


class FourierEncoder(nn.Module):
    """传统傅里叶位置编码器（保持向后兼容）"""

    def __init__(self, bands=16, max_freq=10.0):
        super().__init__()
        self.bands = bands
        self.max_freq = max_freq

    def forward(self, xyz):
        device = xyz.device
        # 生成等间隔频率
        freq_bands = torch.linspace(1.0, self.max_freq, self.bands, device=device).unsqueeze(0)
        xyz = xyz.unsqueeze(-1)  # [B, 3, 1]
        freqs = xyz * freq_bands  # [B, 3, bands]

        # 生成正弦和余弦分量
        sin = torch.sin(2 * np.pi * freqs)
        cos = torch.cos(2 * np.pi * freqs)

        # 拼接所有特征 [基础坐标 + 正弦特征 + 余弦特征]
        return torch.cat([
            xyz.squeeze(-1),
            sin.flatten(start_dim=1),
            cos.flatten(start_dim=1)
        ], dim=-1)


# ====================== 兼容性图构建工具 ======================
def validate_coordinates(coords, function_name="unknown"):
    """
    验证坐标数据的有效性，检测和处理NaN值

    Args:
        coords: torch.Tensor 坐标数据
        function_name: str 调用函数名，用于日志

    Returns:
        coords: 清理后的坐标数据
        valid_mask: 有效坐标的掩码
    """
    if coords.numel() == 0:
        logger.warning(f"{function_name}: 输入坐标为空")
        return coords, torch.ones(coords.shape[0], dtype=torch.bool, device=coords.device)

    # 检测NaN值
    nan_mask = torch.isnan(coords)
    has_nan = nan_mask.any()

    if has_nan:
        nan_count = nan_mask.sum().item()
        total_elements = coords.numel()
        logger.warning(f"{function_name}: 发现 {nan_count}/{total_elements} 个NaN值")

        # 按行检查NaN
        row_nan_mask = nan_mask.any(dim=1)
        nan_rows = row_nan_mask.sum().item()
        total_rows = coords.shape[0]
        logger.warning(f"{function_name}: {nan_rows}/{total_rows} 行包含NaN值")

        # 创建有效坐标掩码
        valid_mask = ~row_nan_mask

        if valid_mask.sum() == 0:
            logger.error(f"{function_name}: 所有坐标都包含NaN值！")
            # 返回零坐标作为fallback
            clean_coords = torch.zeros_like(coords)
            return clean_coords, torch.zeros(coords.shape[0], dtype=torch.bool, device=coords.device)

        # 使用有效坐标
        clean_coords = coords[valid_mask]
        logger.info(f"{function_name}: 使用 {clean_coords.shape[0]} 个有效坐标点")

    else:
        clean_coords = coords
        valid_mask = torch.ones(coords.shape[0], dtype=torch.bool, device=coords.device)

    # 检测无穷大值
    inf_mask = torch.isinf(clean_coords)
    if inf_mask.any():
        inf_count = inf_mask.sum().item()
        logger.warning(f"{function_name}: 发现 {inf_count} 个无穷大值")
        # 将无穷大值替换为有限值
        clean_coords = torch.where(torch.isinf(clean_coords),
                                 torch.sign(clean_coords) * 1e6,
                                 clean_coords)

    return clean_coords, valid_mask

def build_knn_graph_fallback(coords, k):
    """
    Fallback implementation for KNN graph construction with NaN handling
    """
    from sklearn.neighbors import NearestNeighbors

    # 验证和清理坐标
    clean_coords, valid_mask = validate_coordinates(coords, "build_knn_graph_fallback")

    if clean_coords.shape[0] < k + 1:
        logger.warning(f"KNN: 有效坐标数 ({clean_coords.shape[0]}) 少于 k+1 ({k+1})")
        k = max(1, clean_coords.shape[0] - 1)
        if k <= 0:
            return torch.empty((2, 0), dtype=torch.long, device=coords.device)

    try:
        coords_np = clean_coords.detach().cpu().numpy()

        # 再次检查numpy数组中的NaN
        if np.isnan(coords_np).any():
            logger.error("KNN: numpy数组中仍有NaN值，使用零值替换")
            coords_np = np.nan_to_num(coords_np, nan=0.0, posinf=1e6, neginf=-1e6)

        nbrs = NearestNeighbors(n_neighbors=k+1, algorithm='auto').fit(coords_np)
        distances, indices = nbrs.kneighbors(coords_np)

        # Create edge index (exclude self-loops)
        edge_list = []
        valid_indices = torch.where(valid_mask)[0]  # 原始索引

        for i in range(len(coords_np)):
            for j in range(1, k+1):  # Skip first neighbor (self)
                if j < len(indices[i]):
                    # 映射回原始索引
                    src_idx = valid_indices[i].item()
                    dst_idx = valid_indices[indices[i][j]].item()
                    edge_list.append([src_idx, dst_idx])

        if edge_list:
            edge_index = torch.tensor(edge_list, dtype=torch.long).t().contiguous()
        else:
            edge_index = torch.empty((2, 0), dtype=torch.long)

        return edge_index.to(coords.device)

    except Exception as e:
        logger.error(f"KNN图构建失败: {e}")
        # 返回空的边索引
        return torch.empty((2, 0), dtype=torch.long, device=coords.device)

def build_radius_graph_fallback(coords, radius):
    """
    Fallback implementation for radius graph construction with NaN handling
    """
    # 验证和清理坐标
    clean_coords, valid_mask = validate_coordinates(coords, "build_radius_graph_fallback")

    if clean_coords.shape[0] < 2:
        logger.warning(f"Radius: 有效坐标数 ({clean_coords.shape[0]}) 少于2个")
        return torch.empty((2, 0), dtype=torch.long, device=coords.device)

    try:
        coords_np = clean_coords.detach().cpu().numpy()

        # 再次检查numpy数组中的NaN
        if np.isnan(coords_np).any():
            logger.error("Radius: numpy数组中仍有NaN值，使用零值替换")
            coords_np = np.nan_to_num(coords_np, nan=0.0, posinf=1e6, neginf=-1e6)

        n_points = len(coords_np)
        edge_list = []
        valid_indices = torch.where(valid_mask)[0]  # 原始索引

        for i in range(n_points):
            for j in range(i+1, n_points):
                try:
                    dist = np.linalg.norm(coords_np[i] - coords_np[j])
                    if not np.isnan(dist) and not np.isinf(dist) and dist <= radius:
                        # 映射回原始索引
                        src_idx = valid_indices[i].item()
                        dst_idx = valid_indices[j].item()
                        edge_list.append([src_idx, dst_idx])
                        edge_list.append([dst_idx, src_idx])  # Undirected graph
                except Exception as e:
                    logger.debug(f"距离计算失败 i={i}, j={j}: {e}")
                    continue

        if edge_list:
            edge_index = torch.tensor(edge_list, dtype=torch.long).t().contiguous()
        else:
            edge_index = torch.empty((2, 0), dtype=torch.long)

        return edge_index.to(coords.device)

    except Exception as e:
        logger.error(f"半径图构建失败: {e}")
        return torch.empty((2, 0), dtype=torch.long, device=coords.device)

# ====================== 创新图神经网络模块 ======================
class SpatialGraphBuilder(nn.Module):
    """3D空间图构建器（兼容性版本）"""

    def __init__(self, k_neighbors=8, radius=50.0):
        super().__init__()
        self.k_neighbors = k_neighbors
        self.radius = radius

    def build_spatial_graph(self, coords, features):
        """
        构建3D空间图（兼容性实现）

        Args:
            coords: [N, 3] 3D坐标
            features: [N, D] 节点特征

        Returns:
            edge_index: [2, E] 边索引
            edge_attr: [E, edge_dim] 边属性
        """
        device = coords.device

        # 首先验证输入坐标
        clean_coords, valid_mask = validate_coordinates(coords, "SpatialGraphBuilder")

        if clean_coords.shape[0] < 2:
            logger.warning("有效坐标点数少于2个，返回空图")
            return torch.empty((2, 0), dtype=torch.long, device=device), torch.empty((0, 5), device=device)

        # 使用增强的兼容性图构建方法
        edge_index_knn = None
        edge_index_radius = None

        # 尝试使用PyTorch Geometric（如果可用）
        if TORCH_GEOMETRIC_AVAILABLE and KNN_GRAPH_AVAILABLE and RADIUS_GRAPH_AVAILABLE:
            try:
                logger.debug("使用PyTorch Geometric图构建函数")
                edge_index_knn = knn_graph(clean_coords, k=self.k_neighbors, batch=None, loop=False)
                edge_index_radius = radius_graph(clean_coords, r=self.radius, batch=None, loop=False)
                logger.debug(f"PyTorch Geometric图构建成功: KNN边数={edge_index_knn.shape[1]}, 半径图边数={edge_index_radius.shape[1]}")

            except Exception as e:
                logger.warning(f"PyTorch Geometric图构建失败: {e}")
                logger.info("切换到fallback实现")
                edge_index_knn = None
                edge_index_radius = None

        # 使用fallback实现（如果PyTorch Geometric不可用或失败）
        if edge_index_knn is None or edge_index_radius is None:
            logger.debug("使用fallback图构建实现")
            edge_index_knn = build_knn_graph_fallback(coords, self.k_neighbors)
            edge_index_radius = build_radius_graph_fallback(coords, self.radius)
            logger.debug(f"Fallback图构建完成: KNN边数={edge_index_knn.shape[1]}, 半径图边数={edge_index_radius.shape[1]}")

        # 合并两种图结构
        if edge_index_knn.numel() > 0 and edge_index_radius.numel() > 0:
            edge_index = torch.cat([edge_index_knn, edge_index_radius], dim=1)
        elif edge_index_knn.numel() > 0:
            edge_index = edge_index_knn
        elif edge_index_radius.numel() > 0:
            edge_index = edge_index_radius
        else:
            # 创建一个最小的图结构（每个节点连接到自己）
            n_nodes = coords.shape[0]
            edge_index = torch.arange(n_nodes, device=device).repeat(2, 1)

        # 去重
        edge_index = torch.unique(edge_index, dim=1)

        # 计算边属性
        if edge_index.numel() > 0:
            row, col = edge_index
            coord_diff = coords[row] - coords[col]  # [E, 3]

            # 3D距离
            distances = torch.norm(coord_diff, dim=1, keepdim=True)  # [E, 1]

            # 方向向量（归一化）
            directions = F.normalize(coord_diff + 1e-8, p=2, dim=1)  # [E, 3] 添加小常数避免零向量

            # 深度差异（重要的地质特征）
            depth_diff = coord_diff[:, 2:3]  # [E, 1]

            # 边属性：距离 + 方向 + 深度差异
            edge_attr = torch.cat([distances, directions, depth_diff], dim=1)  # [E, 5]
        else:
            edge_attr = torch.empty((0, 5), device=device)

        return edge_index, edge_attr


class GraphAttentionLayer(nn.Module):
    """自定义图注意力层（兼容性实现）"""

    def __init__(self, input_dim, output_dim, heads=4, edge_dim=5, dropout=0.1):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.heads = heads
        self.head_dim = output_dim // heads

        # 注意力权重
        self.W_q = nn.Linear(input_dim, output_dim, bias=False)
        self.W_k = nn.Linear(input_dim, output_dim, bias=False)
        self.W_v = nn.Linear(input_dim, output_dim, bias=False)

        # 边特征处理
        self.edge_proj = nn.Linear(edge_dim, output_dim)

        # 注意力计算
        self.attention = nn.MultiheadAttention(output_dim, heads, dropout=dropout, batch_first=True)

        self.dropout = nn.Dropout(dropout)

    def forward(self, x, edge_index, edge_attr):
        """
        Args:
            x: [N, input_dim] 节点特征
            edge_index: [2, E] 边索引
            edge_attr: [E, edge_dim] 边属性
        """
        N = x.shape[0]

        # 投影到查询、键、值
        Q = self.W_q(x)  # [N, output_dim]
        K = self.W_k(x)  # [N, output_dim]
        V = self.W_v(x)  # [N, output_dim]

        # 如果没有边，返回自注意力结果
        if edge_index.numel() == 0:
            out, _ = self.attention(Q.unsqueeze(0), K.unsqueeze(0), V.unsqueeze(0))
            return out.squeeze(0)

        # 构建邻接矩阵和边特征矩阵
        adj_matrix = torch.zeros(N, N, device=x.device)
        edge_features = torch.zeros(N, N, self.output_dim, device=x.device)

        if edge_index.numel() > 0:
            row, col = edge_index
            adj_matrix[row, col] = 1.0

            # 处理边特征
            edge_feat = self.edge_proj(edge_attr)  # [E, output_dim]
            edge_features[row, col] = edge_feat

        # 计算注意力权重
        attention_scores = torch.matmul(Q, K.transpose(-2, -1)) / (self.head_dim ** 0.5)

        # 应用邻接矩阵掩码
        attention_scores = attention_scores.masked_fill(adj_matrix == 0, float('-inf'))
        attention_weights = F.softmax(attention_scores, dim=-1)
        attention_weights = self.dropout(attention_weights)

        # 聚合特征
        out = torch.matmul(attention_weights, V)

        # 添加边特征的影响
        edge_contribution = torch.sum(edge_features * attention_weights.unsqueeze(-1), dim=1)
        out = out + edge_contribution

        return out


class GeologicalGNN(nn.Module):
    """地质感知图神经网络（兼容性版本）"""

    def __init__(self, input_dim, hidden_dim, num_layers=4, heads=4):
        super().__init__()
        self.num_layers = num_layers
        self.hidden_dim = hidden_dim

        # 输入投影
        self.input_proj = nn.Linear(input_dim, hidden_dim)

        # 图注意力层
        self.gat_layers = nn.ModuleList()
        for i in range(num_layers):
            if TORCH_GEOMETRIC_AVAILABLE and 'GATConv' in globals():
                # 使用PyTorch Geometric的GATConv
                try:
                    self.gat_layers.append(
                        GATConv(hidden_dim, hidden_dim // heads, heads=heads,
                               edge_dim=5, dropout=0.1, concat=True)
                    )
                except:
                    # 如果失败，使用自定义实现
                    self.gat_layers.append(
                        GraphAttentionLayer(hidden_dim, hidden_dim, heads=heads,
                                          edge_dim=5, dropout=0.1)
                    )
            else:
                # 使用自定义图注意力层
                self.gat_layers.append(
                    GraphAttentionLayer(hidden_dim, hidden_dim, heads=heads,
                                      edge_dim=5, dropout=0.1)
                )

        # 层归一化
        self.layer_norms = nn.ModuleList([
            nn.LayerNorm(hidden_dim) for _ in range(num_layers)
        ])

        # 残差连接权重
        self.residual_weights = nn.Parameter(torch.ones(num_layers))

    def forward(self, x, edge_index, edge_attr):
        """
        Args:
            x: [N, input_dim] 节点特征
            edge_index: [2, E] 边索引
            edge_attr: [E, 5] 边属性

        Returns:
            x: [N, hidden_dim] 更新后的节点特征
        """
        x = self.input_proj(x)

        for i, (gat_layer, layer_norm) in enumerate(zip(self.gat_layers, self.layer_norms)):
            residual = x

            # 图注意力传播
            x = gat_layer(x, edge_index, edge_attr)

            # 残差连接和层归一化
            x = layer_norm(x + self.residual_weights[i] * residual)

            # 激活函数
            x = F.relu(x)

        return x


class PhysicsConstrainedDiffusion(nn.Module):
    """物理约束扩散模块"""

    def __init__(self, input_dim, diffusion_coeff=1e-6):
        super().__init__()
        self.diffusion_coeff = diffusion_coeff

        # 扩散参数预测网络
        self.diffusion_net = nn.Sequential(
            nn.Linear(input_dim, input_dim // 2),
            nn.ReLU(),
            nn.Linear(input_dim // 2, 3),  # 3D扩散系数
            nn.Softplus()  # 确保正值
        )

        # 对流项预测网络
        self.advection_net = nn.Sequential(
            nn.Linear(input_dim, input_dim // 2),
            nn.ReLU(),
            nn.Linear(input_dim // 2, 3),  # 3D对流速度
            nn.Tanh()
        )

    def forward(self, features, coords, edge_index):
        """
        基于物理扩散方程的特征传播

        Args:
            features: [N, D] 节点特征
            coords: [N, 3] 3D坐标
            edge_index: [2, E] 边索引
        """
        # 预测局部扩散系数和对流速度
        diffusion_coeffs = self.diffusion_net(features)  # [N, 3]
        advection_velocities = self.advection_net(features)  # [N, 3]

        # 计算空间梯度（基于图结构）
        row, col = edge_index
        coord_diff = coords[col] - coords[row]  # [E, 3]
        feature_diff = features[col] - features[row]  # [E, D]

        # 距离权重
        distances = torch.norm(coord_diff, dim=1, keepdim=True) + 1e-8
        weights = 1.0 / distances  # [E, 1]

        # 扩散项：∇²C
        diffusion_term = torch.zeros_like(features)
        for i in range(features.shape[0]):
            mask = (row == i)
            if mask.any():
                neighbor_diffs = feature_diff[mask]  # [num_neighbors, D]
                neighbor_weights = weights[mask]  # [num_neighbors, 1]
                weighted_diff = (neighbor_diffs * neighbor_weights).sum(dim=0)  # [D]
                diffusion_term[i] = diffusion_coeffs[i].mean() * weighted_diff

        # 对流项：v·∇C（简化实现）
        advection_term = torch.zeros_like(features)
        for i in range(features.shape[0]):
            mask = (row == i)
            if mask.any():
                neighbor_coords = coord_diff[mask]  # [num_neighbors, 3]
                neighbor_features = feature_diff[mask]  # [num_neighbors, D]
                neighbor_weights = weights[mask]  # [num_neighbors, 1]

                # 计算方向梯度
                velocity = advection_velocities[i]  # [3]
                for j in range(neighbor_coords.shape[0]):
                    direction = F.normalize(neighbor_coords[j], p=2, dim=0)
                    velocity_component = torch.dot(velocity, direction)
                    advection_term[i] += velocity_component * neighbor_features[j] * neighbor_weights[j].squeeze()

        # 物理约束的特征更新
        physics_update = diffusion_term + advection_term

        return features + 0.1 * physics_update  # 小的更新步长


# ====================== 传统扩散建模模块（保持兼容性） ======================
class DiffusionModule(nn.Module):
    """污染物扩散建模模块（支持3D空间）"""

    def __init__(self, input_dim):
        super().__init__()
        self.gradient_net = nn.Sequential(
            nn.Linear(input_dim, input_dim * 2),
            nn.ReLU(),
            nn.Linear(input_dim * 2, input_dim)
        )

    def forward(self, context, targets):
        """
        context: [K, D] 已知点的特征，K为已知点数量
        targets: [T, D] 所有点的特征，T为目标点数量
        """
        # 计算点间影响（考虑3D距离）
        similarities = torch.matmul(targets, context.t())  # [T, K]
        weights = F.softmax(similarities, dim=-1)

        # 聚合已知点特征
        context_agg = torch.matmul(weights, context)  # [T, D]

        # 结合目标点原始特征
        combined = targets + context_agg

        # 梯度建模
        gradients = self.gradient_net(combined)

        return combined + gradients


# ====================== 克里金融合模块 ======================
class EnhancedKrigingBlender(nn.Module):
    """增强的克里金融合模块，支持不确定性量化"""

    def __init__(self, input_dim):
        super().__init__()
        self.input_dim = input_dim

        # 自适应权重网络
        self.weight_net = nn.Sequential(
            nn.Linear(input_dim + 1, 64),  # +1 for kriging result
            nn.ReLU(),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 2),  # 两个权重：模型预测和克里金
            nn.Softmax(dim=-1)
        )

        # 基础融合网络（model_features + kriging_results）
        self.basic_fusion = nn.Sequential(
            nn.Linear(input_dim + 1, input_dim),  # +1 for kriging result
            nn.LayerNorm(input_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(input_dim, 1)
        )

        # 不确定性感知融合网络（model_features + kriging_results + uncertainty）
        self.uncertainty_fusion = nn.Sequential(
            nn.Linear(input_dim + 2, input_dim),  # +2 for kriging and uncertainty
            nn.LayerNorm(input_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(input_dim, 1)
        )

    def forward(self, model_features, kriging_results, uncertainty=None):
        """
        Args:
            model_features: [T, D] 模型特征
            kriging_results: [T, 1] 克里金预测结果
            uncertainty: [T, 1] 不确定性估计（可选）
        """
        # 验证输入维度
        logger.debug(f"EnhancedKrigingBlender输入维度:")
        logger.debug(f"  model_features: {model_features.shape}")
        logger.debug(f"  kriging_results: {kriging_results.shape}")
        if uncertainty is not None:
            logger.debug(f"  uncertainty: {uncertainty.shape}")

        # 计算自适应权重
        weight_input = torch.cat([model_features, kriging_results], dim=-1)
        weights = self.weight_net(weight_input)  # [T, 2]

        logger.debug(f"  weight_input: {weight_input.shape}")
        logger.debug(f"  weights: {weights.shape}")

        # 如果有不确定性信息，使用不确定性感知融合
        if uncertainty is not None:
            uncertainty_input = torch.cat([model_features, kriging_results, uncertainty], dim=-1)
            logger.debug(f"  uncertainty_input: {uncertainty_input.shape}")
            logger.debug(f"  uncertainty_fusion expects: {self.uncertainty_fusion[0].in_features}")

            # 验证维度匹配
            if uncertainty_input.shape[1] != self.uncertainty_fusion[0].in_features:
                logger.error(f"不确定性融合维度不匹配:")
                logger.error(f"  输入: {uncertainty_input.shape[1]}")
                logger.error(f"  期望: {self.uncertainty_fusion[0].in_features}")
                raise ValueError(f"不确定性融合维度不匹配: {uncertainty_input.shape[1]} != {self.uncertainty_fusion[0].in_features}")

            uncertainty_adjusted = self.uncertainty_fusion(uncertainty_input)

            # 不确定性加权：不确定性越高，越依赖克里金
            uncertainty_weight = torch.sigmoid(uncertainty)
            final_result = (1 - uncertainty_weight) * uncertainty_adjusted + uncertainty_weight * kriging_results
            return final_result
        else:
            # 使用基础融合（没有不确定性信息）
            basic_input = torch.cat([model_features, kriging_results], dim=-1)
            logger.debug(f"  basic_input: {basic_input.shape}")
            logger.debug(f"  basic_fusion expects: {self.basic_fusion[0].in_features}")

            # 验证维度匹配
            if basic_input.shape[1] != self.basic_fusion[0].in_features:
                logger.error(f"基础融合维度不匹配:")
                logger.error(f"  输入: {basic_input.shape[1]}")
                logger.error(f"  期望: {self.basic_fusion[0].in_features}")
                raise ValueError(f"基础融合维度不匹配: {basic_input.shape[1]} != {self.basic_fusion[0].in_features}")

            model_pred = self.basic_fusion(basic_input)
            basic_fusion = weights[:, 0:1] * model_pred + weights[:, 1:2] * kriging_results
            return basic_fusion


class KrigingBlender(nn.Module):
    """传统克里金融合模块（保持兼容性）"""

    def __init__(self, input_dim):
        super().__init__()
        # 单金属版本：input_dim + 1 (single metal kriging result)
        self.fusion_net = nn.Sequential(
            nn.Linear(input_dim + 1, input_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(input_dim * 2, input_dim),
            nn.ReLU(),
            nn.Linear(input_dim, 1)  # Single metal output
        )

    def forward(self, model_features, kriging_results):
        """
        model_features: [T, D] 模型特征（包含深度信息）
        kriging_results: [T, 1] 克里金预测结果，单金属
        """
        # 拼接特征
        combined = torch.cat([model_features, kriging_results], dim=-1)
        return self.fusion_net(combined)


# ====================== 创新GeoSpatial-GNN模型 ======================
class GeoSpatialGNN(nn.Module):
    """
    创新的地理空间图神经网络模型

    核心创新：
    1. 3D空间图构建与图神经网络
    2. 学习式多尺度空间编码
    3. 物理约束扩散建模
    4. 不确定性量化
    """

    def __init__(self, num_industries):
        super().__init__()

        # 1. 学习式多尺度空间编码器
        if Config.learnable_encoding:
            self.spatial_encoder = LearnableMultiScaleSpatialEncoder(
                input_dim=3,
                encoding_dim=Config.encoding_dim,
                num_scales=Config.multi_scale_levels
            )
            spatial_dim = Config.encoding_dim
        else:
            self.spatial_encoder = FourierEncoder(Config.fourier_bands, Config.fourier_max_freq)
            spatial_dim = 3 + 6 * Config.fourier_bands

        # 2. 场地属性编码
        self.industry_embedding = nn.Embedding(num_industries, Config.embedding_dim)

        # 详细分析场地属性维度
        # site_attributes = ['industry', 'emission', 'area'] (3个属性)
        # industry -> embedding (Config.embedding_dim维)
        # emission, area -> 直接使用 (2维)
        other_site_attrs_count = len(Config.site_attributes) - 1  # 减去industry
        site_attr_dim = Config.embedding_dim + other_site_attrs_count

        # 3. 点级属性维度
        # point_attributes = ['organic', 'depth'] (2个属性)
        point_attr_dim = len(Config.point_attributes)

        # 4. 计算总特征维度并添加详细调试信息
        total_feature_dim = spatial_dim + site_attr_dim + point_attr_dim

        logger.info(f"GeoSpatialGNN feature dimensions (详细计算):")
        logger.info(f"  - spatial_dim: {spatial_dim}")
        logger.info(f"  - site_attr_dim: {site_attr_dim}")
        logger.info(f"    * industry_embedding: {Config.embedding_dim}")
        logger.info(f"    * other_site_attrs: {other_site_attrs_count} (emission, area)")
        logger.info(f"    * site_attributes: {Config.site_attributes}")
        logger.info(f"  - point_attr_dim: {point_attr_dim}")
        logger.info(f"    * point_attributes: {Config.point_attributes}")
        logger.info(f"  - total_feature_dim: {total_feature_dim}")
        logger.info(f"  - target gnn_hidden_dim: {Config.gnn_hidden_dim}")

        # 验证维度计算的正确性
        expected_site_dim = Config.embedding_dim + (len(Config.site_attributes) - 1)
        expected_point_dim = len(Config.point_attributes)
        expected_total = spatial_dim + expected_site_dim + expected_point_dim

        logger.info(f"维度验证:")
        logger.info(f"  - 计算的site_attr_dim: {site_attr_dim}")
        logger.info(f"  - 期望的site_attr_dim: {expected_site_dim}")
        logger.info(f"  - 计算的point_attr_dim: {point_attr_dim}")
        logger.info(f"  - 期望的point_attr_dim: {expected_point_dim}")
        logger.info(f"  - 计算的total_feature_dim: {total_feature_dim}")
        logger.info(f"  - 期望的total_feature_dim: {expected_total}")

        if total_feature_dim != expected_total:
            logger.error(f"维度计算不一致!")
            logger.error(f"  计算值: {total_feature_dim}")
            logger.error(f"  期望值: {expected_total}")
            raise ValueError(f"特征维度计算错误: {total_feature_dim} != {expected_total}")

        # 额外验证：确保与实际数据处理一致
        # 在_encode_features中：
        # - spatial_enc: [B, spatial_dim]
        # - site_enc: [B, embedding_dim + other_site_attrs_count]
        # - point_enc: [B, point_attr_dim]
        # 拼接后: [B, spatial_dim + site_attr_dim + point_attr_dim]

        # 5. 特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Linear(total_feature_dim, Config.gnn_hidden_dim),
            nn.LayerNorm(Config.gnn_hidden_dim),
            nn.ReLU(),
            nn.Dropout(Config.dropout)
        )

        # 存储维度信息用于调试
        self.expected_spatial_dim = spatial_dim
        self.expected_site_attr_dim = site_attr_dim
        self.expected_point_attr_dim = point_attr_dim
        self.expected_total_feature_dim = total_feature_dim

        # 5. 3D空间图构建器
        self.graph_builder = SpatialGraphBuilder(
            k_neighbors=Config.graph_k_neighbors,
            radius=Config.graph_radius
        )

        # 6. 地质感知图神经网络
        self.geological_gnn = GeologicalGNN(
            input_dim=Config.gnn_hidden_dim,
            hidden_dim=Config.gnn_hidden_dim,
            num_layers=Config.gnn_num_layers,
            heads=Config.gnn_heads
        )

        # 7. 物理约束扩散模块
        self.physics_diffusion = PhysicsConstrainedDiffusion(
            input_dim=Config.gnn_hidden_dim,
            diffusion_coeff=Config.diffusion_coeff
        )

        # 8. 不确定性量化模块
        if Config.enable_uncertainty:
            self.uncertainty_head = nn.Sequential(
                nn.Linear(Config.gnn_hidden_dim, 64),
                nn.ReLU(),
                nn.Dropout(0.5),  # 高dropout用于MC采样
                nn.Linear(64, 32),
                nn.ReLU(),
                nn.Dropout(0.5),
                nn.Linear(32, 1),
                nn.Softplus()  # 确保方差为正
            )

        # 9. 主预测头（均值）
        self.prediction_head = nn.Sequential(
            nn.Linear(Config.gnn_hidden_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )

        # 10. 克里金融合模块（增强版）
        self.kriging_blender = EnhancedKrigingBlender(Config.gnn_hidden_dim)

    def forward(self, known_batch, all_points, training=True):
        """
        前向传播

        Args:
            known_batch: 已知点数据
            all_points: 所有点数据
            training: 是否为训练模式

        Returns:
            predictions: 预测结果
            uncertainty: 不确定性（如果启用）
            physics_loss: 物理约束损失
        """
        # 1. 编码所有点的特征
        all_features = self._encode_features(all_points)
        known_features = self._encode_features(known_batch)

        # 2. 构建3D空间图
        coords = all_points['local_coordinates']
        edge_index, edge_attr = self.graph_builder.build_spatial_graph(coords, all_features)

        # 3. 图神经网络特征传播
        gnn_features = self.geological_gnn(all_features, edge_index, edge_attr)

        # 4. 物理约束扩散
        physics_features = self.physics_diffusion(gnn_features, coords, edge_index)

        # 5. 主预测
        predictions = self.prediction_head(physics_features)

        # 6. 不确定性量化
        uncertainty = None
        if Config.enable_uncertainty and training:
            uncertainty = self.uncertainty_head(physics_features)

        # 7. 克里金融合
        kriging_pred = self._calculate_enhanced_kriging(known_batch, all_points, physics_features)
        final_pred = self.kriging_blender(physics_features, kriging_pred, uncertainty)

        # 8. 计算物理约束损失
        physics_loss = self._compute_physics_loss(physics_features, coords, edge_index)

        return final_pred, predictions, kriging_pred, uncertainty, physics_loss

    def _encode_features(self, batch):
        """编码输入特征（支持新的空间编码）- 增强维度检查"""
        coords = batch['local_coordinates']  # [B, 3] 包含 [x, y, rel_depth]
        site_attrs = batch['site_attributes']
        point_attrs = batch['point_attributes']

        logger.debug(f"_encode_features input shapes:")
        logger.debug(f"  - coords: {coords.shape}")
        logger.debug(f"  - site_attrs: {site_attrs.shape}")
        logger.debug(f"  - point_attrs: {point_attrs.shape}")

        # 验证输入维度
        if coords.shape[1] != 3:
            raise ValueError(f"坐标维度错误: 期望3，实际{coords.shape[1]}")

        expected_site_attrs = len(Config.site_attributes)
        if site_attrs.shape[1] != expected_site_attrs:
            raise ValueError(f"场地属性维度错误: 期望{expected_site_attrs}，实际{site_attrs.shape[1]}")

        expected_point_attrs = len(Config.point_attributes)
        if point_attrs.shape[1] != expected_point_attrs:
            raise ValueError(f"点属性维度错误: 期望{expected_point_attrs}，实际{point_attrs.shape[1]}")

        # 场地属性处理
        industry_ids = site_attrs[:, 0].long()
        industry_enc = self.industry_embedding(industry_ids)
        other_site_attrs = site_attrs[:, 1:]
        site_enc = torch.cat([industry_enc, other_site_attrs], dim=1)

        logger.debug(f"Site encoding shapes:")
        logger.debug(f"  - industry_enc: {industry_enc.shape}")
        logger.debug(f"  - other_site_attrs: {other_site_attrs.shape}")
        logger.debug(f"  - site_enc: {site_enc.shape}")

        # 验证场地编码维度
        expected_site_enc_dim = self.expected_site_attr_dim
        if site_enc.shape[1] != expected_site_enc_dim:
            logger.error(f"场地编码维度不匹配:")
            logger.error(f"  期望: {expected_site_enc_dim}")
            logger.error(f"  实际: {site_enc.shape[1]}")
            logger.error(f"  industry_enc: {industry_enc.shape[1]}")
            logger.error(f"  other_site_attrs: {other_site_attrs.shape[1]}")
            raise ValueError(f"场地编码维度不匹配: {site_enc.shape[1]} != {expected_site_enc_dim}")

        # 空间特征编码（使用新的编码器）
        spatial_enc = self.spatial_encoder(coords)
        point_enc = point_attrs

        logger.debug(f"Feature encoding shapes:")
        logger.debug(f"  - spatial_enc: {spatial_enc.shape}")
        logger.debug(f"  - point_enc: {point_enc.shape}")

        # 验证空间编码维度
        expected_spatial_dim = self.expected_spatial_dim
        if spatial_enc.shape[1] != expected_spatial_dim:
            logger.error(f"空间编码维度不匹配:")
            logger.error(f"  期望: {expected_spatial_dim}")
            logger.error(f"  实际: {spatial_enc.shape[1]}")
            raise ValueError(f"空间编码维度不匹配: {spatial_enc.shape[1]} != {expected_spatial_dim}")

        # 验证点属性维度
        expected_point_dim = self.expected_point_attr_dim
        if point_enc.shape[1] != expected_point_dim:
            logger.error(f"点属性维度不匹配:")
            logger.error(f"  期望: {expected_point_dim}")
            logger.error(f"  实际: {point_enc.shape[1]}")
            raise ValueError(f"点属性维度不匹配: {point_enc.shape[1]} != {expected_point_dim}")

        # 拼接所有特征
        raw_features = torch.cat([spatial_enc, site_enc, point_enc], dim=1)

        logger.debug(f"Feature concatenation:")
        logger.debug(f"  - spatial_enc: {spatial_enc.shape[1]} dims")
        logger.debug(f"  - site_enc: {site_enc.shape[1]} dims")
        logger.debug(f"  - point_enc: {point_enc.shape[1]} dims")
        logger.debug(f"  - raw_features: {raw_features.shape}")
        logger.debug(f"  - expected total: {self.expected_total_feature_dim}")
        logger.debug(f"  - fusion layer expects: {self.feature_fusion[0].in_features}")

        # 最终维度验证
        if raw_features.shape[1] != self.expected_total_feature_dim:
            logger.error(f"特征拼接维度不匹配:")
            logger.error(f"  期望: {self.expected_total_feature_dim}")
            logger.error(f"  实际: {raw_features.shape[1]}")
            logger.error(f"  差异: {raw_features.shape[1] - self.expected_total_feature_dim}")
            raise ValueError(f"特征拼接维度不匹配: {raw_features.shape[1]} != {self.expected_total_feature_dim}")

        if raw_features.shape[1] != self.feature_fusion[0].in_features:
            logger.error(f"融合层输入维度不匹配:")
            logger.error(f"  输入特征: {raw_features.shape[1]}")
            logger.error(f"  融合层期望: {self.feature_fusion[0].in_features}")
            logger.error(f"  差异: {raw_features.shape[1] - self.feature_fusion[0].in_features}")
            raise ValueError(f"融合层输入维度不匹配: {raw_features.shape[1]} != {self.feature_fusion[0].in_features}")

        # 特征融合
        try:
            fused_features = self.feature_fusion(raw_features)
            logger.debug(f"Fused features shape: {fused_features.shape}")
            return fused_features
        except RuntimeError as e:
            logger.error(f"Feature fusion runtime error: {e}")
            logger.error(f"Input shape: {raw_features.shape}")
            logger.error(f"Expected input features: {self.feature_fusion[0].in_features}")
            logger.error(f"Output features: {self.feature_fusion[0].out_features}")
            raise

    def _calculate_enhanced_kriging(self, known_batch, all_points, features):
        """增强的克里金插值（考虑特征相似性）"""
        # 获取已知点坐标和浓度（3D）
        known_coords = known_batch['local_coordinates'].detach().cpu().numpy()
        known_concs = known_batch['concentrations'].detach().cpu().numpy()  # [K, 1]

        # 获取预测点坐标（3D）
        pred_coords = all_points['local_coordinates'].detach().cpu().numpy()
        n_points = pred_coords.shape[0]

        # 创建数组存储结果（单金属）
        kriging_preds = np.zeros((n_points, 1))

        # 增强克里金：结合空间距离和特征相似性
        if known_concs[:, 0].var() < 1e-6:
            kriging_preds[:, 0] = known_concs[:, 0].mean()
        else:
            # 计算3D空间距离
            known_3d = known_coords
            pred_3d = pred_coords

            dx = known_3d[:, 0, None] - pred_3d[None, :, 0]
            dy = known_3d[:, 1, None] - pred_3d[None, :, 1]
            dz = known_3d[:, 2, None] - pred_3d[None, :, 2]

            spatial_dist = np.sqrt(dx ** 2 + dy ** 2 + dz ** 2)

            # 计算特征相似性距离
            known_features = features[:len(known_coords)].detach().cpu().numpy()
            all_features = features.detach().cpu().numpy()

            feature_similarities = np.zeros_like(spatial_dist)
            for i in range(len(known_coords)):
                for j in range(n_points):
                    feature_similarities[i, j] = np.exp(-np.linalg.norm(
                        known_features[i] - all_features[j]
                    ) / 10.0)  # 特征相似性权重

            # 组合权重：空间距离 + 特征相似性
            spatial_weights = 1.0 / np.maximum(spatial_dist, 1e-6)
            combined_weights = 0.7 * spatial_weights + 0.3 * feature_similarities

            # 归一化权重
            weight_sums = combined_weights.sum(axis=0)
            weight_sums = np.where(weight_sums < 1e-6, 1.0, weight_sums)
            combined_weights = combined_weights / weight_sums[None, :]

            kriging_preds[:, 0] = np.dot(known_concs[:, 0], combined_weights)

        return torch.tensor(kriging_preds, dtype=torch.float32).to(known_batch['local_coordinates'].device)

    def _compute_physics_loss(self, features, coords, edge_index):
        """计算物理约束损失"""
        # 简化的物理约束：空间平滑性
        row, col = edge_index
        feature_diff = features[row] - features[col]
        coord_diff = coords[row] - coords[col]

        # 距离权重
        distances = torch.norm(coord_diff, dim=1) + 1e-8

        # 平滑性约束：相邻点的特征差异应该与距离成正比
        smoothness_loss = torch.mean((feature_diff.norm(dim=1) / distances) ** 2)

        return Config.physics_weight * smoothness_loss


# ====================== 传统TSISP模型（保持兼容性） ======================
class TSISPModel(nn.Module):
    """完整的TSISP模型：支持3D空间预测（x, y, depth）- 单金属版本"""

    def __init__(self, num_industries):
        super().__init__()
        # Single metal model - no need for num_metals parameter

        # 1. 傅里叶位置编码器（支持3D）
        self.fourier_encoder = FourierEncoder(Config.fourier_bands, Config.fourier_max_freq)
        fourier_dim = 3 + 6 * Config.fourier_bands  # 输出维度（3D输入）

        # 2. 场地属性编码
        self.industry_embedding = nn.Embedding(num_industries, Config.embedding_dim)
        site_attr_dim = Config.embedding_dim + (len(Config.site_attributes) - 1)

        # 3. 点级属性维度（深度已包含）
        point_attr_dim = len(Config.point_attributes)

        # 4. 特征融合层输入总维度
        total_feature_dim = fourier_dim + site_attr_dim + point_attr_dim

        # 5. 特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Linear(total_feature_dim, Config.transformer_dim),
            nn.ReLU(),
            nn.Dropout(Config.dropout)
        )

        # 6. Transformer编码器（支持3D空间）
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=Config.transformer_dim,
            nhead=Config.transformer_heads,
            dim_feedforward=Config.mlp_dim,
            dropout=Config.dropout
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, Config.transformer_depth)

        # 7. 扩散建模模块（支持3D空间）
        self.diffusion_model = DiffusionModule(Config.transformer_dim)

        # 8. 单金属预测头
        self.metal_head = nn.Sequential(
            nn.Linear(Config.transformer_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )

        # 9. 克里金融合模块（支持深度）- 单金属版本
        self.kriging_blender = KrigingBlender(Config.transformer_dim)

    def forward(self, known_batch, all_points):
        """
        基于已知点预测全场地浓度分布（包括不同深度）

        known_batch: 包含已知点数据的字典
        all_points: 包含所有点数据的字典
        """
        # 1. 编码已知点特征（3D坐标）
        known_features = self._encode_features(known_batch)

        # 2. 建立空间依赖关系（3D）
        context = known_features.unsqueeze(0)  # [1, K, D]
        context = self.transformer(context).squeeze(0)  # [K, D]

        # 3. 编码所有点特征（3D坐标）
        all_features = self._encode_features(all_points)

        # 4. 扩散建模（3D）
        diffused_features = self.diffusion_model(context, all_features)  # [T, D]

        # 5. 初步模型预测（单金属）
        model_pred = self.metal_head(diffused_features)  # [T, 1]

        # 6. 计算克里金插值（考虑深度）
        kriging_pred = self._calculate_kriging(known_batch, all_points)

        # 7. 融合模型预测和克里金结果
        final_pred = self.kriging_blender(diffused_features, kriging_pred)

        return final_pred, model_pred, kriging_pred

    def _encode_features(self, batch):
        """编码输入特征（3D坐标处理）"""
        coords = batch['local_coordinates']  # [B, 3] 包含 [x, y, rel_depth]
        site_attrs = batch['site_attributes']
        point_attrs = batch['point_attributes']

        # 场地属性处理
        industry_ids = site_attrs[:, 0].long()
        industry_enc = self.industry_embedding(industry_ids)
        other_site_attrs = site_attrs[:, 1:]
        site_enc = torch.cat([industry_enc, other_site_attrs], dim=1)

        # 特征编码流程（3D傅里叶编码）
        fourier_enc = self.fourier_encoder(coords)  # 坐标->傅里叶特征
        point_enc = point_attrs  # 点级属性直接使用（包含深度）

        # 拼接所有特征
        raw_features = torch.cat([fourier_enc, site_enc, point_enc], dim=1)

        # 特征融合
        fused_features = self.feature_fusion(raw_features)
        return fused_features

    def _calculate_kriging(self, known_batch, all_points):
        """计算克里金插值（考虑深度）- 单金属版本"""
        # 获取已知点坐标和浓度（3D）
        known_coords = known_batch['local_coordinates'].detach().cpu().numpy()
        known_concs = known_batch['concentrations'].detach().cpu().numpy()  # [K, 1]

        # 获取预测点坐标（3D）
        pred_coords = all_points['local_coordinates'].detach().cpu().numpy()
        n_points = pred_coords.shape[0]

        # 创建数组存储结果（单金属）
        kriging_preds = np.zeros((n_points, 1))

        # 单金属克里金插值（考虑3D距离）
        # 跳过没有已知浓度的情况
        if known_concs[:, 0].var() < 1e-6:
            kriging_preds[:, 0] = known_concs[:, 0].mean()
        else:
            # 计算3D距离（x, y, depth）
            known_3d = known_coords
            pred_3d = pred_coords

            # 计算3D距离矩阵
            dx = known_3d[:, 0, None] - pred_3d[None, :, 0]
            dy = known_3d[:, 1, None] - pred_3d[None, :, 1]
            dz = known_3d[:, 2, None] - pred_3d[None, :, 2]

            dist = np.sqrt(dx ** 2 + dy ** 2 + dz ** 2)  # 欧几里得距离

            min_dist = dist.min(axis=0)
            mask = min_dist > 1e-6  # 排除与已知点重叠的点

            # 简单距离反比加权（3D距离）
            weights = 1 / np.maximum(dist, 1e-6)  # 防止除以零
            # Robust weight normalization
            weight_sums = weights.sum(axis=0)
            weight_sums = np.where(weight_sums < 1e-6, 1.0, weight_sums)
            weights = weights / weight_sums[None, :]
            kriging_preds[:, 0] = np.dot(known_concs[:, 0], weights)

            # 对重叠点使用最近邻
            if (~mask).any():
                tree = cKDTree(known_3d)
                dist, nearest_indices = tree.query(pred_3d[~mask])
                kriging_preds[~mask, 0] = known_concs[nearest_indices, 0]

        return torch.tensor(kriging_preds, dtype=torch.float32).to(known_batch['local_coordinates'].device)


# ====================== 增强损失函数（考虑深度） ======================
class InnovativeLoss(nn.Module):
    """创新的损失函数，支持不确定性量化和物理约束"""

    def __init__(self, alpha=2.0, beta=0.7, gamma=0.5, threshold_percentile=0.85):
        super().__init__()
        self.alpha = alpha
        self.beta = beta
        self.gamma = gamma
        self.threshold_percentile = threshold_percentile

    def forward(self, final_pred, model_pred, kriging_pred, target, known_mask, uncertainty=None):
        """
        创新损失函数，支持不确定性量化

        Args:
            final_pred: [T, 1] 最终预测结果
            model_pred: [T, 1] 模型预测结果
            kriging_pred: [T, 1] 克里金预测结果
            target: [T, 1] 真实值
            known_mask: [T] 已知点掩码
            uncertainty: [T, 1] 不确定性估计（可选）
        """
        # 目标中未知点的掩码
        target_mask = ~known_mask

        # 基础MSE损失
        base_loss = F.mse_loss(final_pred[target_mask], target[target_mask])

        # 热点损失
        hotspot_loss = torch.tensor(0.0, device=final_pred.device)
        valid_target = target[target_mask, 0]

        if valid_target.numel() > 0:
            hotspot_threshold = torch.quantile(valid_target, self.threshold_percentile)
            is_hotspot = valid_target > hotspot_threshold

            if is_hotspot.any():
                hotspot_pred = final_pred[target_mask][is_hotspot, 0]
                hotspot_true = valid_target[is_hotspot]
                hotspot_loss = F.mse_loss(hotspot_pred, hotspot_true)

        # 克里金一致性损失
        kriging_consistency_loss = F.mse_loss(model_pred[target_mask], kriging_pred[target_mask])

        # 不确定性损失（如果提供）
        uncertainty_loss = torch.tensor(0.0, device=final_pred.device)
        if uncertainty is not None:
            # 不确定性应该与预测误差相关
            prediction_error = torch.abs(final_pred[target_mask] - target[target_mask])
            uncertainty_pred = uncertainty[target_mask]

            # 负对数似然损失（假设高斯分布）
            uncertainty_loss = torch.mean(
                0.5 * torch.log(uncertainty_pred + 1e-8) +
                0.5 * (prediction_error ** 2) / (uncertainty_pred + 1e-8)
            )

        # 综合损失
        total_loss = (base_loss +
                      self.alpha * hotspot_loss +
                      self.gamma * kriging_consistency_loss +
                      0.1 * uncertainty_loss)

        return total_loss


class EnhancedLoss(nn.Module):
    """传统增强损失函数（保持兼容性）"""

    def __init__(self, alpha=2.0, beta=0.7, gamma=0.5, threshold_percentile=0.85):
        super().__init__()
        self.alpha = alpha
        self.beta = beta
        self.gamma = gamma
        self.threshold_percentile = threshold_percentile

    def forward(self, final_pred, model_pred, kriging_pred, target, known_mask):
        """
        单金属损失计算方法（考虑深度）

        Args:
            final_pred: [T, 1] 最终预测结果
            model_pred: [T, 1] 模型预测结果
            kriging_pred: [T, 1] 克里金预测结果
            target: [T, 1] 真实值
            known_mask: [T] 已知点掩码
        """
        # 目标中未知点的掩码 (用于损失计算)
        target_mask = ~known_mask

        # 基础MSE损失 (最终预测与真实值的差异)
        base_loss = F.mse_loss(final_pred[target_mask], target[target_mask])

        # 热点损失 (高浓度区域，考虑不同深度) - 单金属版本
        hotspot_loss = torch.tensor(0.0, device=final_pred.device)

        # 只考虑未知点
        valid_target = target[target_mask, 0]  # 单金属，取第一列

        if valid_target.numel() > 0:
            # 计算热点阈值
            hotspot_threshold = torch.quantile(valid_target, self.threshold_percentile)

            # 确定热点点
            is_hotspot = valid_target > hotspot_threshold

            if is_hotspot.any():
                # 计算热点区域的损失（考虑深度）
                hotspot_pred = final_pred[target_mask][is_hotspot, 0]
                hotspot_true = valid_target[is_hotspot]
                hotspot_loss = F.mse_loss(hotspot_pred, hotspot_true)

        # 梯度损失 (空间平滑性约束，考虑深度变化)
        gradient_loss = torch.tensor(0.0, device=final_pred.device)

        # 可以添加空间梯度约束，但为简化暂时设为0
        # TODO: 实现3D空间梯度约束

        # 克里金一致性损失 (不同深度下的一致性)
        kriging_consistency_loss = F.mse_loss(model_pred[target_mask], kriging_pred[target_mask])

        # 综合损失
        total_loss = (base_loss +
                      self.alpha * hotspot_loss +
                      self.beta * gradient_loss +
                      self.gamma * kriging_consistency_loss)

        return total_loss


# ====================== 训练函数（支持3D） ======================
def train_epoch(model, dataloader, optimizer, loss_fn, device, epoch):
    """
    Train the model for one epoch with comprehensive monitoring.

    Args:
        model: TSISP model instance
        dataloader: DataLoader containing training sites
        optimizer: PyTorch optimizer
        loss_fn: Enhanced loss function
        device: Computing device (CPU/GPU)
        epoch: Current epoch number

    Returns:
        float: Average training loss for the epoch

    Note:
        Implements dynamic loss weighting, gradient clipping, and memory management
    """
    model.train()
    total_loss_value = 0.0
    site_count = 0

    # 动态调整损失权重
    if epoch < Config.epochs * 0.3:  # 前30%训练
        alpha = 1.0
        beta = 0.5
        gamma = 0.2
    elif epoch < Config.epochs * 0.7:  # 中间40%
        alpha = 1.5
        beta = 0.7
        gamma = 0.4
    else:  # 最后30%
        alpha = Config.alpha
        beta = Config.beta
        gamma = Config.gamma

    # 临时覆盖损失权重
    loss_fn.alpha = alpha
    loss_fn.beta = beta
    loss_fn.gamma = gamma

    for batch in dataloader:
        # 获取场地数据（包含不同深度）
        site_data = batch

        # 移动数据到设备
        coords = site_data['local_coordinates'].to(device)  # 3D坐标
        site_attrs = site_data['site_attributes'].to(device)
        point_attrs = site_data['point_attributes'].to(device)
        concentrations = site_data['concentrations'].to(device)
        site_id = site_data['site_id'].to(device)

        # 获取场地中的总点数（包含不同深度的点）
        n_points = coords.shape[0]

        # 随机选择已知点 (至少保留Config.known_points_min个点)
        min_known = min(Config.known_points_min, n_points)
        max_known = max(min_known, n_points - 1)  # 至少保留一个未知点
        known_count = min_known + int((max_known - min_known) * (1 - Config.mask_ratio))
        known_indices = torch.randperm(n_points)[:known_count]
        known_mask = torch.zeros(n_points, dtype=torch.bool).to(device)
        known_mask[known_indices] = True

        # 创建已知点批次（考虑深度）
        known_batch = {
            'local_coordinates': coords[known_mask],
            'site_attributes': site_attrs[known_mask],
            'point_attributes': point_attrs[known_mask],
            'concentrations': concentrations[known_mask],
            'site_id': site_id[known_mask]
        }

        # 创建全场地点批次（所有深度）
        all_points = {
            'local_coordinates': coords,
            'site_attributes': site_attrs,
            'point_attributes': point_attrs,
            'concentrations': torch.zeros_like(concentrations).to(device),  # 仅占位
            'site_id': site_id
        }

        # 前向传播（支持不同架构）
        if Config.architecture == 'geospatial_gnn':
            final_pred, model_pred, kriging_pred, uncertainty, physics_loss = model(known_batch, all_points, training=True)
        else:
            final_pred, model_pred, kriging_pred = model(known_batch, all_points)
            physics_loss = torch.tensor(0.0, device=device)

        # 计算损失 (只针对未知点)
        if Config.architecture == 'geospatial_gnn':
            base_loss = loss_fn(
                final_pred=final_pred,
                model_pred=model_pred,
                kriging_pred=kriging_pred,
                target=concentrations,
                known_mask=known_mask,
                uncertainty=uncertainty
            )
        else:
            base_loss = loss_fn(
                final_pred=final_pred,
                model_pred=model_pred,
                kriging_pred=kriging_pred,
                target=concentrations,
                known_mask=known_mask
            )

        # 添加物理约束损失
        total_loss = base_loss + physics_loss

        # 反向传播 with gradient clipping
        optimizer.zero_grad()
        total_loss.backward()

        # Gradient clipping for stability
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

        optimizer.step()

        # Memory cleanup for large datasets
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        # 累计统计
        total_loss_value += total_loss.item()
        site_count += 1

    return total_loss_value / max(site_count, 1)  # 平均场地损失


# ====================== 评估函数（支持深度） ======================
def evaluate(model, dataloader, loss_fn, device, return_metal_metrics=False):
    """在验证集上评估模型 - 支持深度数据"""
    model.eval()
    total_loss = 0.0
    site_count = 0

    # 存储评估指标
    metrics = {
        'rmse': [],
        'mae': [],
        'r2': []
    }

    # 单金属指标
    metal_metrics = {
        Config.target_metal: {
            'rmse': [],
            'mae': [],
            'r2': []
        }
    }

    # 场地整体指标
    site_metrics = {
        'site_id': [],
        'num_points': [],
        'rmse': [],
        'mae': [],
        'r2': []
    }

    with torch.no_grad():
        for batch in dataloader:
            site_data = batch

            # 移动数据到设备
            coords = site_data['local_coordinates'].to(device)  # 3D坐标
            site_attrs = site_data['site_attributes'].to(device)
            point_attrs = site_data['point_attributes'].to(device)
            concentrations = site_data['concentrations'].to(device)
            site_id = site_data['site_id'].to(device)

            n_points = coords.shape[0]

            # 随机选择已知点（包含不同深度）
            min_known = min(Config.known_points_min, n_points)
            max_known = max(min_known, n_points - 1)
            known_count = min_known + int((max_known - min_known) * (1 - Config.mask_ratio))
            known_indices = torch.randperm(n_points)[:known_count]
            known_mask = torch.zeros(n_points, dtype=torch.bool).to(device)
            known_mask[known_indices] = True

            # 创建已知点批次（考虑深度）
            known_batch = {
                'local_coordinates': coords[known_mask],
                'site_attributes': site_attrs[known_mask],
                'point_attributes': point_attrs[known_mask],
                'concentrations': concentrations[known_mask],
                'site_id': site_id[known_mask]
            }

            # 创建全场地点批次（所有深度）
            all_points = {
                'local_coordinates': coords,
                'site_attributes': site_attrs,
                'point_attributes': point_attrs,
                'concentrations': torch.zeros_like(concentrations).to(device),
                'site_id': site_id
            }

            # 前向传播（支持不同架构）
            if Config.architecture == 'geospatial_gnn':
                final_pred, model_pred, kriging_pred, uncertainty, physics_loss = model(known_batch, all_points, training=False)
            else:
                final_pred, model_pred, kriging_pred = model(known_batch, all_points)

            # 计算损失 (只针对未知点)
            loss = loss_fn(
                final_pred=final_pred,
                model_pred=model_pred,
                kriging_pred=kriging_pred,
                target=concentrations,
                known_mask=known_mask
            )

            # 反标准化浓度值用于评估
            final_pred_exp = final_pred.expm1().cpu().numpy()
            target_exp = concentrations.expm1().cpu().numpy()

            # 只评估未知点
            target_mask = ~known_mask.cpu().numpy()
            if np.any(target_mask):
                pred_vals = final_pred_exp[target_mask]
                true_vals = target_exp[target_mask]

                # 计算整体指标
                mse = np.mean((pred_vals - true_vals) ** 2)
                rmse = np.sqrt(mse)
                mae = np.mean(np.abs(pred_vals - true_vals))
                r2 = 1 - np.sum((pred_vals - true_vals) ** 2) / np.sum((true_vals - true_vals.mean()) ** 2)

                metrics['rmse'].append(rmse)
                metrics['mae'].append(mae)
                metrics['r2'].append(r2)

                # 计算单金属指标
                metal_pred = pred_vals[:, 0]  # 单金属，取第一列
                metal_true = true_vals[:, 0]

                metal_mse = np.mean((metal_pred - metal_true) ** 2)
                metal_rmse = np.sqrt(metal_mse)
                metal_mae = np.mean(np.abs(metal_pred - metal_true))

                # Robust R² calculation
                ss_res = np.sum((metal_pred - metal_true) ** 2)
                ss_tot = np.sum((metal_true - metal_true.mean()) ** 2)
                metal_r2 = 1 - (ss_res / (ss_tot + 1e-8))  # Add small constant to avoid division by zero

                metal_metrics[Config.target_metal]['rmse'].append(metal_rmse)
                metal_metrics[Config.target_metal]['mae'].append(metal_mae)
                metal_metrics[Config.target_metal]['r2'].append(metal_r2)

                # 场地整体统计（包含所有深度点）
                site_id = batch['site_id'][0].item() if hasattr(batch['site_id'], 'item') else batch['site_id'][0]
                site_metrics['site_id'].append(site_id)
                site_metrics['num_points'].append(len(target_mask))
                site_metrics['rmse'].append(rmse)
                site_metrics['mae'].append(mae)
                site_metrics['r2'].append(r2)

            # 累计损失
            total_loss += loss.item()
            site_count += 1

        # 计算平均指标
        avg_metrics = {k: np.nanmean(v) if v else float('nan') for k, v in metrics.items()}
        avg_metrics['loss'] = total_loss / max(site_count, 1)

        # 添加单金属平均指标
        for metric in ['rmse', 'mae', 'r2']:
            avg_metrics[f"{Config.target_metal}_{metric}"] = np.nanmean(metal_metrics[Config.target_metal][metric])

        # 场地级别指标转换为DataFrame
        site_metrics_df = pd.DataFrame(site_metrics)

        if return_metal_metrics:
            return avg_metrics, site_metrics_df, metal_metrics
        return avg_metrics, site_metrics_df


# ====================== 学习率调度器 ======================
def create_lr_scheduler(optimizer, num_epochs):
    """创建带预热的余弦退火学习率调度器"""
    def lr_lambda(epoch):
        if epoch < Config.warmup_epochs:
            # Warmup phase: linear increase
            return epoch / Config.warmup_epochs
        else:
            # Cosine annealing phase
            progress = (epoch - Config.warmup_epochs) / (num_epochs - Config.warmup_epochs)
            return 0.5 * (1 + np.cos(np.pi * progress))

    return torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)

# ====================== 早停机制 ======================
class EarlyStopping:
    """Early stopping to prevent overfitting"""

    def __init__(self, patience=20, min_delta=1e-4, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_score = None
        self.counter = 0
        self.best_weights = None

    def __call__(self, score, model):
        if self.best_score is None:
            self.best_score = score
            self.save_checkpoint(model)
        elif score < self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                if self.restore_best_weights:
                    model.load_state_dict(self.best_weights)
                return True
        else:
            self.best_score = score
            self.counter = 0
            self.save_checkpoint(model)
        return False

    def save_checkpoint(self, model):
        """Save model checkpoint"""
        self.best_weights = model.state_dict().copy()

# ====================== 主训练流程（支持深度） ======================
def analyze_training_history(history_df):
    """分析训练历史并选择最佳模型（支持深度数据）"""
    # 创建综合评分标准：70% R2 + 30% (1 - 标准化RMSE)
    max_r2 = history_df['val_r2'].max()
    min_rmse = history_df['val_rmse'].min()

    # 标准化RMSE
    history_df['norm_rmse'] = (history_df['val_rmse'] - min_rmse) / (history_df['val_rmse'].max() - min_rmse)

    # 创建综合评分
    history_df['composite_score'] = (
            0.7 * history_df['val_r2'] +
            0.3 * (1 - history_df['norm_rmse']) -
            0.2 * history_df['val_mae'] / history_df['val_mae'].max()
    )

    # 识别稳定期
    stability_threshold = Config.epochs * 0.6  # 训练60%后进入稳定期
    stable_history = history_df[history_df['epoch'] > stability_threshold]

    # 稳定期平均指标
    stable_metrics = {
        'mean_r2': stable_history['val_r2'].mean(),
        'std_r极': stable_history['val_r2'].std(),
        'mean_rmse': stable_history['val_rmse'].mean(),
        'mean_composite': stable_history['composite_score'].mean()
    }

    # 识别候选模型 (综合指标排名前10)
    candidate_models = history_df.sort_values('composite_score', ascending=False).head(10)

    # 从中选择稳定期表现最好的模型
    candidate_models = candidate_models[candidate_models['epoch'] > stability_threshold]

    if candidate_models.empty:
        candidate_models = history_df.sort_values('composite_score', ascending=False).head(3)

    # 优先选择最高R2的模型
    best_row = candidate_models.loc[candidate_models['val_r2'].idxmax()]

    # 如果最高R2模型离稳定期太远，则选用综合分数最高的稳定期模型
    if best_row['epoch'] < stability_threshold:
        stable_candidates = history_df[history_df['epoch'] > stability_threshold]
        if not stable_candidates.empty:
            best_row = stable_candidates.loc[stable_candidates['composite_score'].idxmax()]

    # 输出分析报告
    print("\n===== 训练综合分析报告 =====")
    print(f"总训练轮次: {Config.epochs}")
    print(f"稳定期起始轮次: {int(stability_threshold)}")
    print(f"最高R²得分: {max_r2:.4f} at epoch {history_df['val_r2'].idxmax()}")
    print(f"最低RMSE: {min_rmse:.4f} at epoch {history_df['val_rmse'].idxmin()}")
    print(f"最佳综合评分: {best_row['composite_score']:.4f} at epoch {best_row['epoch']}")
    print(f"  对应R²: {best_row['val_r2']:.4f}")
    print(f"  对应RMSE: {best_row['val_rmse']:.4f}")
    print(f"  对应MAE: {best_row['val_mae']:.4f}")

    # Publication-quality plotting with Nature journal standards
    plt.style.use('default')  # Reset to default style

    # Set publication parameters
    plt.rcParams.update({
        'font.size': 12,
        'font.family': 'Arial',
        'axes.linewidth': 1.5,
        'axes.spines.top': False,
        'axes.spines.right': False,
        'xtick.major.size': 6,
        'xtick.minor.size': 3,
        'ytick.major.size': 6,
        'ytick.minor.size': 3,
        'xtick.direction': 'out',
        'ytick.direction': 'out',
        'lines.linewidth': 2,
        'grid.alpha': 0.3,
        'legend.frameon': False,
        'figure.dpi': 300,
        'savefig.dpi': 300,
        'savefig.bbox': 'tight',
        'savefig.pad_inches': 0.1
    })

    # Create figure with publication-quality layout
    fig, axes = plt.subplots(3, 1, figsize=(10, 12))

    # Define Nature-style color palette
    colors = {
        'validation': '#1f77b4',    # Blue
        'training': '#ff7f0e',      # Orange
        'stability': '#d62728',     # Red
        'best': '#2ca02c',          # Green
        'composite': '#9467bd'      # Purple
    }

    # R² curve with error bands
    ax1 = axes[0]

    # Calculate rolling statistics for smoothing
    window = max(5, len(history_df) // 20)
    val_r2_smooth = history_df['val_r2'].rolling(window=window, center=True).mean()
    val_r2_std = history_df['val_r2'].rolling(window=window, center=True).std()

    # Plot with confidence intervals
    ax1.plot(history_df['epoch'], history_df['val_r2'],
             color=colors['validation'], alpha=0.3, linewidth=1)
    ax1.plot(history_df['epoch'], val_r2_smooth,
             color=colors['validation'], linewidth=2.5, label='Validation R²')
    ax1.fill_between(history_df['epoch'],
                     val_r2_smooth - val_r2_std,
                     val_r2_smooth + val_r2_std,
                     color=colors['validation'], alpha=0.2)

    ax1.plot(history_df['epoch'], history_df['train_r2'],
             color=colors['training'], linewidth=2, alpha=0.7,
             linestyle='--', label='Training R²')

    ax1.axvline(x=stability_threshold, color=colors['stability'],
                linestyle=':', linewidth=2, alpha=0.8, label='Stability threshold')
    ax1.scatter(best_row['epoch'], best_row['val_r2'],
                color=colors['best'], s=100, zorder=5,
                marker='*', edgecolor='white', linewidth=1, label='Best model')

    ax1.set_ylabel('R² Score', fontweight='bold')
    ax1.set_title('Model Performance: Coefficient of Determination',
                  fontweight='bold', pad=20)
    ax1.legend(loc='lower right', fontsize=10)
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(bottom=0)

    # RMSE curve with similar treatment
    ax2 = axes[1]

    val_rmse_smooth = history_df['val_rmse'].rolling(window=window, center=True).mean()
    val_rmse_std = history_df['val_rmse'].rolling(window=window, center=True).std()

    ax2.plot(history_df['epoch'], history_df['val_rmse'],
             color=colors['validation'], alpha=0.3, linewidth=1)
    ax2.plot(history_df['epoch'], val_rmse_smooth,
             color=colors['validation'], linewidth=2.5, label='Validation RMSE')
    ax2.fill_between(history_df['epoch'],
                     val_rmse_smooth - val_rmse_std,
                     val_rmse_smooth + val_rmse_std,
                     color=colors['validation'], alpha=0.2)

    ax2.plot(history_df['epoch'], history_df['train_rmse'],
             color=colors['training'], linewidth=2, alpha=0.7,
             linestyle='--', label='Training RMSE')

    ax2.axvline(x=stability_threshold, color=colors['stability'],
                linestyle=':', linewidth=2, alpha=0.8)
    ax2.scatter(best_row['epoch'], best_row['val_rmse'],
                color=colors['best'], s=100, zorder=5,
                marker='*', edgecolor='white', linewidth=1)

    ax2.set_ylabel('RMSE (mg/kg)', fontweight='bold')
    ax2.set_title('Model Performance: Root Mean Square Error',
                  fontweight='bold', pad=20)
    ax2.legend(loc='upper right', fontsize=10)
    ax2.grid(True, alpha=0.3)

    # Composite score
    ax3 = axes[2]

    comp_smooth = history_df['composite_score'].rolling(window=window, center=True).mean()
    comp_std = history_df['composite_score'].rolling(window=window, center=True).std()

    ax3.plot(history_df['epoch'], history_df['composite_score'],
             color=colors['composite'], alpha=0.3, linewidth=1)
    ax3.plot(history_df['epoch'], comp_smooth,
             color=colors['composite'], linewidth=2.5, label='Composite Score')
    ax3.fill_between(history_df['epoch'],
                     comp_smooth - comp_std,
                     comp_smooth + comp_std,
                     color=colors['composite'], alpha=0.2)

    ax3.axvline(x=stability_threshold, color=colors['stability'],
                linestyle=':', linewidth=2, alpha=0.8)
    ax3.scatter(best_row['epoch'], best_row['composite_score'],
                color=colors['best'], s=100, zorder=5,
                marker='*', edgecolor='white', linewidth=1)

    ax3.set_xlabel('Training Epoch', fontweight='bold')
    ax3.set_ylabel('Composite Score', fontweight='bold')
    ax3.set_title('Model Performance: Composite Evaluation Metric',
                  fontweight='bold', pad=20)
    ax3.legend(loc='lower right', fontsize=10)
    ax3.grid(True, alpha=0.3)

    # Adjust layout for publication
    plt.tight_layout(pad=3.0)

    # Add figure caption
    fig.text(0.5, 0.02,
             'Figure 1. Training dynamics of the TSISP model showing (A) R² coefficient evolution, '
             '(B) RMSE progression, and (C) composite performance metric. Shaded areas represent '
             'confidence intervals from rolling statistics. The star indicates the optimal model.',
             ha='center', fontsize=10, style='italic', wrap=True)

    return best_row['epoch'], best_row

def create_publication_metal_analysis(history_df, output_dir):
    """Create publication-quality single-metal performance analysis"""

    # Set publication parameters
    plt.rcParams.update({
        'font.size': 12,
        'font.family': 'Arial',
        'figure.dpi': 300,
        'savefig.dpi': 300,
        'savefig.bbox': 'tight'
    })

    # Create single metal performance plot
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))

    # Color for the target metal
    metal_color = '#1f77b4'  # Blue

    # Plot R² evolution
    val_col = f'val_{Config.target_metal}_r2'
    train_col = f'train_{Config.target_metal}_r2'

    if val_col in history_df.columns and train_col in history_df.columns:
        # Smooth the curves
        window = max(5, len(history_df) // 20)
        val_smooth = history_df[val_col].rolling(window=window, center=True).mean()
        train_smooth = history_df[train_col].rolling(window=window, center=True).mean()

        ax1.plot(history_df['epoch'], val_smooth,
               color=metal_color, linewidth=2.5, label='Validation R²')
        ax1.plot(history_df['epoch'], train_smooth,
               color=metal_color, linewidth=2, alpha=0.7,
               linestyle='--', label='Training R²')

        # Add confidence intervals
        val_std = history_df[val_col].rolling(window=window, center=True).std()
        ax1.fill_between(history_df['epoch'],
                       val_smooth - val_std, val_smooth + val_std,
                       color=metal_color, alpha=0.2)

    ax1.set_title(f'{Config.target_metal} R² Performance', fontweight='bold', fontsize=14)
    ax1.set_ylabel('R² Score', fontweight='bold')
    ax1.set_xlabel('Training Epoch', fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.legend(fontsize=10)

    # Plot RMSE evolution
    val_rmse_col = f'val_{Config.target_metal}_rmse'
    train_rmse_col = f'train_{Config.target_metal}_rmse'

    if val_rmse_col in history_df.columns and train_rmse_col in history_df.columns:
        val_rmse_smooth = history_df[val_rmse_col].rolling(window=window, center=True).mean()
        train_rmse_smooth = history_df[train_rmse_col].rolling(window=window, center=True).mean()

        ax2.plot(history_df['epoch'], val_rmse_smooth,
               color='#ff7f0e', linewidth=2.5, label='Validation RMSE')
        ax2.plot(history_df['epoch'], train_rmse_smooth,
               color='#ff7f0e', linewidth=2, alpha=0.7,
               linestyle='--', label='Training RMSE')

        # Add confidence intervals
        val_rmse_std = history_df[val_rmse_col].rolling(window=window, center=True).std()
        ax2.fill_between(history_df['epoch'],
                       val_rmse_smooth - val_rmse_std, val_rmse_smooth + val_rmse_std,
                       color='#ff7f0e', alpha=0.2)

    ax2.set_title(f'{Config.target_metal} RMSE Performance', fontweight='bold', fontsize=14)
    ax2.set_ylabel('RMSE (mg/kg)', fontweight='bold')
    ax2.set_xlabel('Training Epoch', fontweight='bold')
    ax2.grid(True, alpha=0.3)
    ax2.legend(fontsize=10)

    # Plot loss evolution
    if 'train_loss' in history_df.columns and 'val_loss' in history_df.columns:
        loss_smooth_train = history_df['train_loss'].rolling(window=window, center=True).mean()
        loss_smooth_val = history_df['val_loss'].rolling(window=window, center=True).mean()

        ax3.plot(history_df['epoch'], loss_smooth_train,
               color='#2ca02c', linewidth=2, alpha=0.7,
               linestyle='--', label='Training Loss')
        ax3.plot(history_df['epoch'], loss_smooth_val,
               color='#2ca02c', linewidth=2.5, label='Validation Loss')

    ax3.set_title('Training Loss Evolution', fontweight='bold', fontsize=14)
    ax3.set_ylabel('Loss', fontweight='bold')
    ax3.set_xlabel('Training Epoch', fontweight='bold')
    ax3.grid(True, alpha=0.3)
    ax3.legend(fontsize=10)
    ax3.set_yscale('log')

    # Final performance summary
    if val_col in history_df.columns:
        final_r2 = history_df[val_col].iloc[-10:].mean()
        final_rmse = history_df[val_rmse_col].iloc[-10:].mean() if val_rmse_col in history_df.columns else 0

        # Create summary text
        summary_text = f"""Final Performance Summary

Target Metal: {Config.target_metal}
Final R²: {final_r2:.4f}
Final RMSE: {final_rmse:.3f} mg/kg

Training Configuration:
- Epochs: {len(history_df)}
- Architecture: 3D Spatial Transformer
- Features: x, y, depth + site attributes
        """

        ax4.text(0.1, 0.5, summary_text, transform=ax4.transAxes,
                fontsize=11, verticalalignment='center',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
        ax4.set_xlim(0, 1)
        ax4.set_ylim(0, 1)
        ax4.axis('off')

    plt.tight_layout(pad=3.0)
    plt.savefig(os.path.join(output_dir, 'analysis', f'{Config.target_metal}_performance_analysis.png'),
                dpi=300, bbox_inches='tight')
    plt.close()

    logger.info(f"Publication-quality visualization created for {Config.target_metal}")

# ====================== Configuration Validation ======================
def validate_config():
    """Validate configuration parameters for consistency and reasonableness."""
    issues = []

    # Check parameter ranges
    if Config.mask_ratio <= 0 or Config.mask_ratio >= 1:
        issues.append("mask_ratio must be between 0 and 1")

    if Config.learning_rate <= 0 or Config.learning_rate > 1:
        issues.append("learning_rate must be positive and reasonable")

    if Config.transformer_depth < 1 or Config.transformer_depth > 20:
        issues.append("transformer_depth should be between 1 and 20")

    if Config.transformer_heads < 1 or Config.transformer_heads > Config.transformer_dim:
        issues.append("transformer_heads must be positive and <= transformer_dim")

    if Config.transformer_dim % Config.transformer_heads != 0:
        issues.append("transformer_dim must be divisible by transformer_heads")

    # Check loss weights
    if any(w < 0 for w in [Config.alpha, Config.beta, Config.gamma]):
        issues.append("Loss weights (alpha, beta, gamma) must be non-negative")

    if issues:
        for issue in issues:
            logger.error(f"Configuration issue: {issue}")
        raise ValueError(f"Configuration validation failed: {issues}")

    logger.info("Configuration validation passed")

def print_model_summary(model, sample_input=None):
    """Print comprehensive model summary with parameter counts."""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    logger.info("=" * 60)
    logger.info("MODEL ARCHITECTURE SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Total parameters: {total_params:,}")
    logger.info(f"Trainable parameters: {trainable_params:,}")
    logger.info(f"Non-trainable parameters: {total_params - trainable_params:,}")

    # Memory estimation
    param_size = total_params * 4 / (1024**2)  # Assuming float32
    logger.info(f"Estimated model size: {param_size:.2f} MB")

    # Architecture breakdown
    logger.info("\nArchitecture Components:")
    for name, module in model.named_children():
        module_params = sum(p.numel() for p in module.parameters())
        logger.info(f"  {name}: {module_params:,} parameters")

    logger.info("=" * 60)


# ====================== 主训练流程（支持深度） ======================
def train_model(csv_path: str, output_dir: str, random_seed: int = 42):
    """
    Main training function with comprehensive error handling and monitoring.

    Args:
        csv_path: Path to the CSV file containing soil data
        output_dir: Directory to save outputs
        random_seed: Random seed for reproducibility

    Returns:
        Tuple[nn.Module, int]: Trained model and best epoch number
    """
    try:
        # Set up reproducibility
        set_random_seeds(random_seed)

        # Validate configuration
        validate_config()

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'checkpoints'), exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'analysis'), exist_ok=True)

        # 选择设备
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"Using device: {device}")

        if torch.cuda.is_available():
            logger.info(f"GPU: {torch.cuda.get_device_name()}")
            logger.info(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

    except Exception as e:
        logger.error(f"Setup failed: {e}")
        raise

    # 数据预处理（深度数据处理）
    preprocessor = SoilDataPreprocessor(csv_path)
    preprocessor.preprocess()
    full_data = preprocessor.get_preprocessed_data()

    # 保存预处理对象（包含深度信息）
    preprocessor.save_scalers(os.path.join(output_dir, 'data_scalers.pth'))

    # 按场地划分数据集（包含不同深度的点）
    gss = GroupShuffleSplit(n_splits=1, test_size=0.2, random_state=42)
    train_idx, test_idx = next(gss.split(full_data, groups=full_data['site_id']))
    train_data = full_data.iloc[train_idx]
    test_data = full_data.iloc[test_idx]

    # 创建场地数据集（包含深度）
    train_sites = []
    for site_id, group in train_data.groupby('site_id'):
        if len(group) >= Config.known_points_min + 1:
            train_sites.append(SoilMultiSiteDataset(group.copy(), device))

    test_sites = []
    for site_id, group in test_data.groupby('site_id'):
        if len(group) >= Config.known_points_min + 1:
            test_sites.append(SoilMultiSiteDataset(group.copy(), device))

    # 创建数据加载器
    train_loader = DataLoader(
        ConcatDataset(train_sites),
        batch_size=1,
        shuffle=True,
        collate_fn=lambda batches: batches[0]
    )

    test_loader = DataLoader(
        ConcatDataset(test_sites),
        batch_size=1,
        shuffle=False,
        collate_fn=lambda batches: batches[0]
    )

    # 初始化模型（支持架构选择）
    num_industries = preprocessor.num_industries

    if Config.architecture == 'geospatial_gnn':
        logger.info("Using innovative GeoSpatial-GNN architecture")
        model = GeoSpatialGNN(num_industries).to(device)
    else:
        logger.info("Using traditional Transformer architecture")
        model = TSISPModel(num_industries).to(device)

    # 参数统计
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"模型参数总数: {total_params:,}")
    print(f"可训练参数: {trainable_params:,}")

    # 损失函数（支持不同架构）
    if Config.architecture == 'geospatial_gnn':
        loss_fn = InnovativeLoss(
            alpha=Config.alpha,
            beta=Config.beta,
            gamma=Config.gamma,
            threshold_percentile=Config.hotspot_threshold
        )
    else:
        loss_fn = EnhancedLoss(
            alpha=Config.alpha,
            beta=Config.beta,
            gamma=Config.gamma,
            threshold_percentile=Config.hotspot_threshold
        )

    # 优化器
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=Config.learning_rate,
        weight_decay=1e-5
    )

    # 学习率调度器
    scheduler = create_lr_scheduler(optimizer, Config.epochs)

    # Early stopping
    early_stopping = EarlyStopping(
        patience=Config.patience,
        min_delta=Config.min_delta,
        restore_best_weights=True
    )

    # Print model summary
    print_model_summary(model)

    # 训练历史记录
    history = {
        'epoch': [],
        'train_loss': [],
        'val_loss': [],
        'train_r2': [],
        'val_r2': [],
        'train_rmse': [],
        'val_rmse': [],
        'train_mae': [],
        'val_mae': [],
        'lr': []
    }

    # 添加详细指标（单金属）
    metal = Config.target_metal
    history[f'train_{metal}_r2'] = []
    history[f'val_{metal}_r2'] = []
    history[f'train_{metal}_rmse'] = []
    history[f'val_{metal}_rmse'] = []
    history[f'train_{metal}_mae'] = []
    history[f'val_{metal}_mae'] = []

    # 初始化最佳模型跟踪
    best_val_r2 = -float('inf')
    best_model_path = os.path.join(output_dir, 'best_r2_model.pth')

    print(f"开始训练... 总轮次: {Config.epochs}")
    for epoch in range(Config.epochs):
        # 训练阶段（3D数据）
        train_loss = train_epoch(model, train_loader, optimizer, loss_fn, device, epoch)

        # 验证阶段（训练集评估）
        train_metrics, _ = evaluate(model, train_loader, loss_fn, device)
        train_r2 = train_metrics.get('r2', float('nan'))
        train_rmse = train_metrics.get('rmse', float('nan'))
        train_mae = train_metrics.get('mae', float('nan'))

        # 验证阶段（测试集评估）
        val_metrics, _, metal_metrics = evaluate(model, test_loader, loss_fn, device, return_metal_metrics=True)
        val_loss = val_metrics['loss']
        val_r2 = val_metrics.get('r2', float('nan'))
        val_rmse = val_metrics.get('rmse', float('nan'))
        val_mae = val_metrics.get('mae', float('nan'))

        # 更新学习率
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']

        # 记录历史
        history['epoch'].append(epoch + 1)
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_r2'].append(train_r2)
        history['val_r2'].append(val_r2)
        history['train_rmse'].append(train_rmse)
        history['val_rmse'].append(val_rmse)
        history['train_mae'].append(train_mae)
        history['val_mae'].append(val_mae)
        history['lr'].append(current_lr)

        # 记录单金属指标
        metal = Config.target_metal
        history[f'train_{metal}_r2'].append(train_metrics.get(f"{metal}_r2", float('nan')))
        history[f'val_{metal}_r2'].append(metal_metrics[metal]['r2'][-1] if metal_metrics[metal]['r2'] else float('nan'))
        history[f'train_{metal}_rmse'].append(train_metrics.get(f"{metal}_rmse", float('nan')))
        history[f'val_{metal}_rmse'].append(metal_metrics[metal]['rmse'][-1] if metal_metrics[metal]['rmse'] else float('nan'))
        history[f'train_{metal}_mae'].append(train_metrics.get(f"{metal}_mae", float('nan')))
        history[f'val_{metal}_mae'].append(metal_metrics[metal]['mae'][-1] if metal_metrics[metal]['mae'] else float('nan'))

        # 打印进度
        print(f"Epoch {epoch + 1}/{Config.epochs}: "
              f"Train Loss: {train_loss:.4f} | "
              f"Val Loss: {val_loss:.4f} | "
              f"R²: Train={train_r2:.4f}, Val={val_r2:.4f} | "
              f"LR: {current_lr:.2e}")

        # 基于R²保存最佳模型
        if val_r2 > best_val_r2:
            best_val_r2 = val_r2
            best_epoch = epoch + 1
            torch.save(model.state_dict(), best_model_path)
            print(f"  → 更新最佳R²模型 (epoch {best_epoch}, R²={val_r2:.4f})")

        # 保存中期检查点
        if (epoch + 1) % 50 == 0:
            checkpoint_path = os.path.join(output_dir, 'checkpoints', f'checkpoint_epoch_{epoch + 1}.pth')
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'train_loss': train_loss,
                'val_loss': val_loss,
                'train_r2': train_r2,
                'val_r2': val_r2
            }, checkpoint_path)
            print(f"保存检查点: {checkpoint_path}")

            # 保存训练历史
            history_df = pd.DataFrame(history)
            history_csv_path = os.path.join(output_dir, 'training_history_interim.csv')
            history_df.to_csv(history_csv_path, index=False)

    # 训练结束处理
    print("\n训练完成! 开始分析最佳模型...")

    # 保存最终模型
    final_model_path = os.path.join(output_dir, 'final_model.pth')
    torch.save(model.state_dict(), final_model_path)

    # 保存完整训练历史
    history_df = pd.DataFrame(history)
    history_csv_path = os.path.join(output_dir, 'training_history_final.csv')
    history_df.to_csv(history_csv_path, index=False)

    # 分析训练历史并选择最佳模型
    best_epoch, best_metrics = analyze_training_history(history_df)

    # 保存分析图
    plt.savefig(os.path.join(output_dir, 'analysis', 'training_analysis.png'), dpi=300)
    plt.close()

    # 优先加载基于R²保存的最佳模型
    best_r2_path = os.path.join(output_dir, 'best_r2_model.pth')
    if os.path.exists(best_r2_path):
        print(f"\n加载基于R²的最佳模型")
        model.load_state_dict(torch.load(best_r2_path))
        best_source = "基于验证集R²保存的模型"
    else:
        # 次优选择：加载分析选出的最佳模型检查点
        checkpoint_path = os.path.join(output_dir, 'checkpoints', f'checkpoint_epoch_{best_epoch}.pth')
        if os.path.exists(checkpoint_path):
            print(f"\n加载分析选出的最佳模型: epoch {best_epoch}")
            checkpoint = torch.load(checkpoint_path)
            model.load_state_dict(checkpoint['model_state_dict'])
            best_source = f"分析选出的最佳模型 (epoch {best_epoch})"
        else:
            # 最后选择：加载最终模型
            print(f"警告: 找不到最佳模型检查点，使用最终模型")
            model.load_state_dict(torch.load(final_model_path))
            best_source = "最终模型"

    # 验证最佳模型表现
    val_metrics, site_metrics, metal_metrics = evaluate(model, test_loader, loss_fn, device,
                                                        return_metal_metrics=True)

    # 保存最佳模型
    final_best_path = os.path.join(output_dir, f'best_model.pth')
    torch.save(model.state_dict(), final_best_path)

    # 保存详细评估结果（含深度数据）
    report = f"最终模型报告 ({best_source})\n"
    report += f"综合评分: {best_metrics['composite_score']:.4f}\n"
    report += f"验证集R²: {val_metrics['r2']:.4f}\n"
    report += f"验证集RMSE: {val_metrics['rmse']:.4f}\n"
    report += f"验证集MAE: {val_metrics['mae']:.4f}\n\n"

    report += f"目标金属 {Config.target_metal} 表现:\n"
    metal = Config.target_metal
    report += f"R²={val_metrics[f'{metal}_r2']:.4f}, RMSE={val_metrics[f'{metal}_rmse']:.4f}, MAE={val_metrics[f'{metal}_mae']:.4f}\n"

    report += "\n场地级别表现（包含不同深度点）:\n"
    report += site_metrics.describe().to_string()

    # 保存报告
    with open(os.path.join(output_dir, 'analysis', 'final_model_report.txt'), 'w', encoding='utf-8') as f:
        f.write(report)

    print(f"\n===== 最终模型评估结果 =====")
    print(report)
    print(f"\n最佳模型已保存至: {final_best_path}")
    print(f"详细报告保存至: {os.path.join(output_dir, 'analysis')}")

    return model, best_epoch

# ====================== 批量训练支持 ======================
def train_multiple_metals(csv_path: str, output_base_dir: str, metals_to_train: List[str] = None, random_seed: int = 42):
    """
    Sequential training of multiple single-metal models.

    Args:
        csv_path: Path to the CSV file containing soil data
        output_base_dir: Base directory for outputs (subdirectories will be created for each metal)
        metals_to_train: List of metals to train. If None, will detect from data
        random_seed: Random seed for reproducibility

    Returns:
        Dict[str, Tuple]: Dictionary mapping metal names to (model, best_epoch) tuples
    """
    logger.info("=" * 80)
    logger.info("BATCH TRAINING: MULTIPLE SINGLE-METAL MODELS")
    logger.info("=" * 80)

    # Detect available metals if not specified
    if metals_to_train is None:
        # Read data to detect available metals
        temp_data = pd.read_csv(csv_path)
        metals_to_train = Config.get_available_metals_in_data(temp_data.columns)
        logger.info(f"Auto-detected metals in dataset: {metals_to_train}")
    else:
        logger.info(f"Training specified metals: {metals_to_train}")

    if not metals_to_train:
        raise ValueError("No metals found in dataset or specified for training")

    # Create base output directory
    os.makedirs(output_base_dir, exist_ok=True)

    # Store results
    trained_models = {}
    training_summary = []

    # Train each metal sequentially
    for i, metal in enumerate(metals_to_train):
        logger.info(f"\n{'='*60}")
        logger.info(f"TRAINING METAL {i+1}/{len(metals_to_train)}: {metal}")
        logger.info(f"{'='*60}")

        try:
            # Set target metal
            Config.set_target_metal(metal)

            # Create metal-specific output directory
            metal_output_dir = os.path.join(output_base_dir, f"model_{metal}")

            # Train the model
            model, best_epoch = train_model(csv_path, metal_output_dir, random_seed)

            # Store results
            trained_models[metal] = (model, best_epoch)

            # Read final metrics for summary
            history_path = os.path.join(metal_output_dir, 'training_history_final.csv')
            if os.path.exists(history_path):
                history_df = pd.read_csv(history_path)
                final_r2 = history_df[f'val_{metal}_r2'].iloc[-10:].mean()
                final_rmse = history_df[f'val_{metal}_rmse'].iloc[-10:].mean()
                final_mae = history_df[f'val_{metal}_mae'].iloc[-10:].mean()

                training_summary.append({
                    'metal': metal,
                    'best_epoch': best_epoch,
                    'final_r2': final_r2,
                    'final_rmse': final_rmse,
                    'final_mae': final_mae,
                    'model_path': os.path.join(metal_output_dir, 'best_model.pth')
                })

            logger.info(f"✓ Successfully trained model for {metal}")

        except Exception as e:
            logger.error(f"✗ Failed to train model for {metal}: {e}")
            logger.error(traceback.format_exc())
            continue

    # Create comprehensive summary
    create_batch_training_summary(training_summary, output_base_dir)

    logger.info(f"\n{'='*80}")
    logger.info("BATCH TRAINING COMPLETED")
    logger.info(f"Successfully trained {len(trained_models)}/{len(metals_to_train)} models")
    logger.info(f"Results saved in: {output_base_dir}")
    logger.info(f"{'='*80}")

    return trained_models

def create_batch_training_summary(training_summary: List[Dict], output_dir: str):
    """Create a comprehensive summary of batch training results."""

    if not training_summary:
        logger.warning("No training results to summarize")
        return

    # Create summary DataFrame
    summary_df = pd.DataFrame(training_summary)

    # Save summary CSV
    summary_csv_path = os.path.join(output_dir, 'batch_training_summary.csv')
    summary_df.to_csv(summary_csv_path, index=False)

    # Create summary visualization
    plt.style.use('default')
    plt.rcParams.update({
        'font.size': 12,
        'font.family': 'Arial',
        'figure.dpi': 300,
        'savefig.dpi': 300,
        'savefig.bbox': 'tight'
    })

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

    metals = summary_df['metal'].tolist()
    colors = plt.cm.Set3(np.linspace(0, 1, len(metals)))

    # R² comparison
    bars1 = ax1.bar(metals, summary_df['final_r2'], color=colors, alpha=0.8, edgecolor='black')
    ax1.set_ylabel('R² Score', fontweight='bold')
    ax1.set_title('Final R² Performance by Metal', fontweight='bold', fontsize=14)
    ax1.set_ylim(0, 1)
    ax1.grid(True, alpha=0.3, axis='y')

    # Add value labels
    for bar, value in zip(bars1, summary_df['final_r2']):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{value:.3f}', ha='center', va='bottom', fontweight='bold')

    # RMSE comparison
    bars2 = ax2.bar(metals, summary_df['final_rmse'], color=colors, alpha=0.8, edgecolor='black')
    ax2.set_ylabel('RMSE (mg/kg)', fontweight='bold')
    ax2.set_title('Final RMSE by Metal', fontweight='bold', fontsize=14)
    ax2.grid(True, alpha=0.3, axis='y')

    # Add value labels
    for bar, value in zip(bars2, summary_df['final_rmse']):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{value:.2f}', ha='center', va='bottom', fontweight='bold')

    # MAE comparison
    bars3 = ax3.bar(metals, summary_df['final_mae'], color=colors, alpha=0.8, edgecolor='black')
    ax3.set_ylabel('MAE (mg/kg)', fontweight='bold')
    ax3.set_title('Final MAE by Metal', fontweight='bold', fontsize=14)
    ax3.grid(True, alpha=0.3, axis='y')

    # Add value labels
    for bar, value in zip(bars3, summary_df['final_mae']):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{value:.2f}', ha='center', va='bottom', fontweight='bold')

    # Training epochs
    bars4 = ax4.bar(metals, summary_df['best_epoch'], color=colors, alpha=0.8, edgecolor='black')
    ax4.set_ylabel('Best Epoch', fontweight='bold')
    ax4.set_title('Convergence Speed by Metal', fontweight='bold', fontsize=14)
    ax4.grid(True, alpha=0.3, axis='y')

    # Add value labels
    for bar, value in zip(bars4, summary_df['best_epoch']):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{int(value)}', ha='center', va='bottom', fontweight='bold')

    plt.tight_layout(pad=3.0)
    plt.savefig(os.path.join(output_dir, 'batch_training_summary.png'),
                dpi=300, bbox_inches='tight')
    plt.close()

    # Create text summary
    summary_text = "BATCH TRAINING SUMMARY\n"
    summary_text += "=" * 50 + "\n\n"
    summary_text += f"Total metals trained: {len(summary_df)}\n"
    summary_text += f"Average R²: {summary_df['final_r2'].mean():.4f} ± {summary_df['final_r2'].std():.4f}\n"
    summary_text += f"Average RMSE: {summary_df['final_rmse'].mean():.3f} ± {summary_df['final_rmse'].std():.3f}\n"
    summary_text += f"Average MAE: {summary_df['final_mae'].mean():.3f} ± {summary_df['final_mae'].std():.3f}\n"
    summary_text += f"Average convergence epoch: {summary_df['best_epoch'].mean():.1f}\n\n"

    summary_text += "Individual Metal Performance:\n"
    summary_text += "-" * 30 + "\n"
    for _, row in summary_df.iterrows():
        summary_text += f"{row['metal']}: R²={row['final_r2']:.4f}, RMSE={row['final_rmse']:.3f}, MAE={row['final_mae']:.3f}, Epoch={int(row['best_epoch'])}\n"

    # Save text summary
    with open(os.path.join(output_dir, 'batch_training_summary.txt'), 'w', encoding='utf-8') as f:
        f.write(summary_text)

    logger.info("Batch training summary created successfully")
    logger.info(f"Summary saved to: {summary_csv_path}")

    return summary_df


def main():
    """
    Main function demonstrating different training modes.

    Usage Examples:

    1. Single Metal Training:
        python train_tsisp.py --metal Pb --output single_metal_output

    2. Batch Training (All Metals):
        python train_tsisp.py --batch --output batch_output

    3. Batch Training (Specific Metals):
        python train_tsisp.py --batch --metals Pb Cd Cu --output batch_output
    """
    import argparse

    parser = argparse.ArgumentParser(description='TSISP Single-Metal Spatial Prediction Training')
    parser.add_argument('--data', type=str, default='空间预测/soil_heavy_metal_data.csv',
                       help='Path to CSV data file')
    parser.add_argument('--metal', type=str, default='Pb',
                       help='Target metal for single-metal training')
    parser.add_argument('--output', type=str, default='tsisp_output',
                       help='Output directory')
    parser.add_argument('--batch', action='store_true',
                       help='Enable batch training for multiple metals')
    parser.add_argument('--metals', nargs='+', type=str,
                       help='Specific metals for batch training (e.g., --metals Pb Cd Cu)')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed for reproducibility')

    args = parser.parse_args()

    # Configuration display
    logger.info("=" * 80)
    logger.info("TSISP MODEL TRAINING - 3D SPATIAL PREDICTION (SINGLE-METAL)")
    logger.info("=" * 80)
    logger.info("Configuration Summary:")
    logger.info(f"  Data file: {args.data}")
    logger.info(f"  Output directory: {args.output}")
    logger.info(f"  Random seed: {args.seed}")
    logger.info(f"  Epochs: {Config.epochs}")
    logger.info(f"  Learning rate: {Config.learning_rate}")
    logger.info(f"  Mask ratio: {Config.mask_ratio}")
    logger.info(f"  Minimal points: {Config.known_points_min}")
    logger.info(f"  Transformer depth: {Config.transformer_depth}")
    logger.info(f"  Transformer heads: {Config.transformer_heads}")
    logger.info(f"  Early stopping patience: {Config.patience}")
    logger.info(f"  3D Features: x, y, depth + site attributes")

    if args.batch:
        logger.info(f"  Training mode: BATCH (Multiple metals)")
        if args.metals:
            logger.info(f"  Target metals: {args.metals}")
        else:
            logger.info(f"  Target metals: Auto-detect from data")
    else:
        logger.info(f"  Training mode: SINGLE METAL")
        logger.info(f"  Target metal: {args.metal}")

    logger.info("=" * 80)

    try:
        if args.batch:
            # Batch training mode
            trained_models = train_multiple_metals(
                csv_path=args.data,
                output_base_dir=args.output,
                metals_to_train=args.metals,
                random_seed=args.seed
            )
            logger.info(f"Batch training completed! Trained {len(trained_models)} models")

        else:
            # Single metal training mode
            Config.set_target_metal(args.metal)
            model, best_epoch = train_model(args.data, args.output, args.seed)
            logger.info(f"Single-metal training completed! Best model at epoch {best_epoch}")

    except Exception as e:
        logger.error(f"Training failed: {e}")
        logger.error(traceback.format_exc())
        raise

if __name__ == "__main__":
    # For backward compatibility, run single metal training with default settings
    # Users can also call main() for command-line interface

    # Default single-metal training example
    data_path = "空间预测/soil_heavy_metal_data.csv"
    output_dir = "空间预测/tsisp_single_metal_output"
    target_metal = "Pb"  # Default metal

    logger.info("=" * 80)
    logger.info("TSISP SINGLE-METAL TRAINING - DEFAULT EXAMPLE")
    logger.info("=" * 80)
    logger.info("Running default single-metal training for demonstration.")
    logger.info("For advanced usage with command-line options, call main() function.")
    logger.info("=" * 80)

    try:
        # Set target metal
        Config.set_target_metal(target_metal)

        # Train single metal model
        model, best_epoch = train_model(data_path, output_dir, random_seed=42)
        logger.info(f"Default training completed! Best model for {target_metal} at epoch {best_epoch}")

        # Example of batch training (commented out)
        """
        # Uncomment to run batch training instead:
        batch_output_dir = "tsisp_batch_output"
        trained_models = train_multiple_metals(
            csv_path=data_path,
            output_base_dir=batch_output_dir,
            metals_to_train=['Pb', 'Cd', 'Cu'],  # Specify metals or None for auto-detect
            random_seed=42
        )
        """

    except Exception as e:
        logger.error(f"Training failed: {e}")
        logger.error(traceback.format_exc())
        raise