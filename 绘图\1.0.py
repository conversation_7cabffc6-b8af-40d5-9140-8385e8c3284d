# 导入matplotlib绘图库
import matplotlib.pyplot as plt
# 导入matplotlib图形补丁模块
import matplotlib.patches as patches
# 导入花式边框补丁类
from matplotlib.patches import FancyBboxPatch
# 导入numpy数值计算库
import numpy as np
# 导入matplotlib主模块
import matplotlib as mpl
# 导入日期时间模块
import datetime

# 设置图形DPI为300，提高图像质量
mpl.rcParams['figure.dpi'] = 300
# 设置保存图像DPI为300
mpl.rcParams['savefig.dpi'] = 300
# 启用路径简化以提高渲染性能
mpl.rcParams['path.simplify'] = True
# 设置路径简化阈值
mpl.rcParams['path.simplify_threshold'] = 1.0
# 设置聚合路径块大小
mpl.rcParams['agg.path.chunksize'] = 10000

# 设置中文字体列表，优先使用黑体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
# 解决负号显示问题
plt.rcParams['axes.unicode_minus'] = False
# 设置默认字体大小为20
plt.rcParams['font.size'] = 20

# 定义可调节的层间距离变量
LAYER_SPACING = 4

# 创建图形和轴对象，设置画布大小为24x20英寸
fig, ax = plt.subplots(1, 1, figsize=(24, 40), constrained_layout=True)
# 设置X轴显示范围为0到24
ax.set_xlim(0, 24)
# 设置Y轴显示范围为0到22
ax.set_ylim(0, 40)
# 隐藏坐标轴
ax.axis('off')

# 定义颜色方案字典
COLORS = {
    'process_fill': '#F0F8FF',    # 处理步骤的淡蓝色填充
    'process_edge': '#1E90FF',    # 处理步骤的蓝色边框
    'data_fill': '#FFF5F5',       # 核心模型的淡红色填充
    'data_edge': '#FF4500',       # 核心模型的红色边框
    'result_fill': '#F0FFF0',     # 最终输出的淡绿色填充
    'result_edge': '#32CD32',     # 最终输出的绿色边框
    'arrow': '#2F4F4F',           # 箭头的深灰色
    'text': '#000000',            # 文字的黑色
    'background': '#FFFFFF',      # 背景的白色
    'layer_bg': '#E6E6FA'         # 层级标签的淡紫色背景
}

# 定义绘制流程框的函数
def draw_box(ax, x, y, width, height, text, box_type='process', text_size=20):
    # 判断框的类型是否为处理步骤
    if box_type == 'process':
        # 创建处理步骤类型的花式边框补丁
        box = FancyBboxPatch(
            (x-width/2, y-height/2), width, height,  # 设置框的位置和大小
            boxstyle="round,pad=0.4,rounding_size=0.3",  # 设置圆角样式
            facecolor=COLORS['process_fill'],  # 设置填充颜色
            edgecolor=COLORS['process_edge'],  # 设置边框颜色
            linestyle='--',  # 设置虚线样式
            linewidth=3.0,  # 设置线宽
            zorder=2  # 设置图层顺序
        )
    # 判断框的类型是否为核心模型
    elif box_type == 'data':
        # 创建核心模型类型的花式边框补丁
        box = FancyBboxPatch(
            (x-width/2, y-height/2), width, height,  # 设置框的位置和大小
            boxstyle="round,pad=0.4,rounding_size=0.3",  # 设置圆角样式
            facecolor=COLORS['data_fill'],  # 设置填充颜色
            edgecolor=COLORS['data_edge'],  # 设置边框颜色
            linestyle='-',  # 设置实线样式
            linewidth=3.5,  # 设置线宽
            zorder=2  # 设置图层顺序
        )
    # 其他类型默认为最终输出
    else:  # result
        # 创建最终输出类型的花式边框补丁
        box = FancyBboxPatch(
            (x-width/2, y-height/2), width, height,  # 设置框的位置和大小
            boxstyle="round,pad=0.4,rounding_size=0.3",  # 设置圆角样式
            facecolor=COLORS['result_fill'],  # 设置填充颜色
            edgecolor=COLORS['result_edge'],  # 设置边框颜色
            linestyle='-',  # 设置实线样式
            linewidth=3.5,  # 设置线宽
            zorder=2  # 设置图层顺序
        )
    
    # 将创建的框添加到轴对象中
    ax.add_patch(box)
    # 在框中添加文字
    ax.text(
        x, y, text,  # 设置文字位置和内容
        ha='center', va='center',  # 设置水平和垂直对齐方式为居中
        fontsize=text_size,  # 设置字体大小
        weight='bold',  # 设置字体粗细为粗体
        color=COLORS['text'],  # 设置文字颜色
        zorder=3  # 设置图层顺序，确保文字在框的上方
    )

# 使用可调节的层间距离计算各层的Y坐标
layer1_y = 30  # 模型基础层的Y坐标
layer2_y = layer1_y - LAYER_SPACING  # 容器化层的Y坐标
layer3_y = layer2_y - LAYER_SPACING  # 应用开发层的Y坐标
layer4_y = layer3_y - LAYER_SPACING  # RAG知识层的Y坐标
layer5_y = layer4_y - LAYER_SPACING  # Agent智能层的Y坐标
layer6_y = layer5_y - LAYER_SPACING  # 系统集成层的Y坐标
layer7_y = layer6_y - LAYER_SPACING  # 服务接口层的Y坐标
layer8_y = layer7_y - LAYER_SPACING  # 安全监控层的Y坐标

# 绘制第一层：模型基础层
# 绘制环境领域轻量化模型框
draw_box(ax, 5, layer1_y, 4.5, 1.2, '环境领域轻量化模型\nvLLM分布式推理', 'data', 30)
# 绘制高并发服务框
draw_box(ax, 12, layer1_y, 4.5, 1.2, '高并发服务保障\n推理优化引擎', 'data', 30)
# 绘制模型量化优化框
draw_box(ax, 19, layer1_y, 4.5, 1.2, '模型量化优化\n轻量化部署', 'data', 30)

# 绘制第二层：容器化层
# 绘制Docker容器化框
draw_box(ax, 5, layer2_y, 4.0, 1.2, 'Docker容器化\n微服务封装', 'process', 30)
# 绘制Kubernetes编排框
draw_box(ax, 12, layer2_y, 4.0, 1.2, 'Kubernetes编排\n弹性资源调度', 'process', 30)
# 绘制工程化部署框
draw_box(ax, 19, layer2_y, 4.0, 1.2, '工程化部署\n全链路闭环', 'process', 30)

# 绘制第三层：应用开发层
# 绘制可视化界面框
draw_box(ax, 5, layer3_y, 3.5, 1.2, '可视化微服务\nReact界面', 'process', 30)
# 绘制数据治理框
draw_box(ax, 10, layer3_y, 3.5, 1.2, '数据治理服务\nFastAPI后端', 'process', 30)
# 绘制AI引擎框
draw_box(ax, 15, layer3_y, 3.5, 1.2, 'AI引擎服务\nLangChain框架', 'process', 30)
# 机器学习模型接口
draw_box(ax, 20, layer3_y, 3.5, 1.2, '机器学习模型\n接口封装', 'process', 30)

# 绘制第四层：RAG知识层
# 绘制GraphRAG架构框
draw_box(ax, 6, layer4_y, 4.0, 1.2, 'GraphRAG架构\n知识引擎', 'process', 30)
# 绘制多源环境知识库框
draw_box(ax, 12, layer4_y, 4.0, 1.2, '多源环境知识库\n水文地质拓扑', 'process', 30)
# 绘制图神经网络框
draw_box(ax, 18, layer4_y, 4.0, 1.2, '图神经网络\n多跳语义检索', 'process', 30)

# 绘制第五层：Agent智能层
# 绘制定制化Agent框
draw_box(ax, 5, layer5_y, 3.5, 1.2, '定制化Agent\nLangChain集成', 'process', 30)
# 绘制污染溯源框
draw_box(ax, 10, layer5_y, 3.5, 1.2, '污染溯源分析\n智能推理', 'process', 30)
# 绘制全流程调度框
draw_box(ax, 15, layer5_y, 3.5, 1.2, '全流程自动化\n任务调度', 'process', 30)
# 绘制报告生成框
draw_box(ax, 20, layer5_y, 3.5, 1.2, '报告自动生成\nOffice集成', 'process', 30)

# 绘制第六层：系统集成层
# 绘制微服务集群框
draw_box(ax, 5, layer6_y, 3.5, 1.2, '微服务集群\nDocker Compose', 'process', 30)
# 绘制服务网关框
draw_box(ax, 10, layer6_y, 3.5, 1.2, '服务网关\nNginx负载均衡', 'process', 30)
# 绘制数据存储框
draw_box(ax, 15, layer6_y, 3.5, 1.2, '数据存储层\nRedis+PostgreSQL', 'process', 30)
# 绘制配置管理框
draw_box(ax, 20, layer6_y, 3.5, 1.2, '配置管理\n环境变量控制', 'process', 30)

# 绘制第七层：服务接口层
# 绘制RESTful API框
draw_box(ax, 6, layer7_y, 4.5, 1.2, 'RESTful API\nOpenAPI规范', 'result', 30)
# 绘制WebSocket框
draw_box(ax, 12, layer7_y, 4.5, 1.2, 'WebSocket\n实时数据推送', 'result', 30)
# 绘制GraphQL框
draw_box(ax, 18, layer7_y, 4.5, 1.2, 'GraphQL\n灵活数据查询', 'result', 30)

# 绘制第八层：安全监控层
# 绘制身份认证框
draw_box(ax, 6, layer8_y, 4.5, 1.2, '身份认证\nJWT+OAuth2.0', 'result', 30)
# 绘制系统监控框
draw_box(ax, 12, layer8_y, 4.5, 1.2, '系统监控\nPrometheus+Grafana', 'result', 30)
# 绘制日志分析框
draw_box(ax, 18, layer8_y, 4.5, 1.2, '日志分析\nELK技术栈', 'result', 30)

# 在图形顶部添加标题
ax.text(12, 32, '智能模型平台技术架构图', ha='center', va='center', 
        fontsize=40, weight='bold', color='#000066')

# 定义层级标签列表，包含标签名称和对应的Y坐标
layer_labels = [
    ('模型基础层', layer1_y),  # 模型基础层标签
    ('容器化层', layer2_y),    # 容器化层标签
    ('应用开发层', layer3_y),  # 应用开发层标签
    ('RAG知识层', layer4_y),   # RAG知识层标签
    ('Agent智能层', layer5_y),  # Agent智能层标签
    ('系统集成层', layer6_y),  # 系统集成层标签
    ('服务接口层', layer7_y),  # 服务接口层标签
    ('安全监控层', layer8_y)   # 安全监控层标签
]

# 遍历层级标签列表，为每个层级添加紫色标签框
for label, y in layer_labels:
    # 创建矩形补丁作为标签背景
    rect = patches.Rectangle(
        (0.95, y-0.95), 0.5, 1.9,  # 设置矩形的位置和大小
        facecolor=COLORS['layer_bg'],  # 设置填充颜色为淡紫色
        edgecolor='#9370DB',  # 设置边框颜色为紫色
        linewidth=2.0,  # 设置边框线宽
        alpha=0.9,  # 设置透明度
        zorder=1  # 设置图层顺序
    )
    # 将矩形添加到轴对象中
    ax.add_patch(rect)
    
    # 在矩形中添加垂直文字
    ax.text(
        1.2, y, label,  # 设置文字位置和内容
        ha='center', va='center',  # 设置水平和垂直对齐方式为居中
        fontsize=25,  # 设置字体大小
        weight='bold',  # 设置字体粗细为粗体
        rotation=90,  # 设置文字旋转90度（垂直显示）
        color='#4B0082',  # 设置文字颜色为深紫色
        zorder=2  # 设置图层顺序
    )

# 绘制层间箭头的函数
def draw_layer_arrow(ax, y_start, y_end, x_center=12):
    """绘制层间向上箭头"""
    arrow_y = (y_start + y_end) / 2
    arrow_height = 0.8
    arrow_width = 1.5
    
    # 创建向上箭头形状的顶点
    arrow_points = np.array([
        [x_center, arrow_y - arrow_height/2],  # 底部中心
        [x_center - arrow_width/2, arrow_y - arrow_height/4],  # 左下
        [x_center - arrow_width/4, arrow_y - arrow_height/4],  # 左中
        [x_center - arrow_width/4, arrow_y + arrow_height/2],  # 左上
        [x_center + arrow_width/4, arrow_y + arrow_height/2],  # 右上
        [x_center + arrow_width/4, arrow_y - arrow_height/4],  # 右中
        [x_center + arrow_width/2, arrow_y - arrow_height/4],  # 右下
    ])
    
    # 创建箭头补丁
    arrow = patches.Polygon(
        arrow_points,
        facecolor='#4169E1',  # 皇家蓝色
        edgecolor='#191970',  # 深蓝色边框
        linewidth=2,
        alpha=0.8,
        zorder=1
    )
    ax.add_patch(arrow)

# 在所有层级绘制完成后，添加层间箭头
# 安全监控层 -> 服务接口层
draw_layer_arrow(ax, layer8_y, layer7_y)
# 服务接口层 -> 系统集成层
draw_layer_arrow(ax, layer7_y, layer6_y)
# 系统集成层 -> Agent智能层
draw_layer_arrow(ax, layer6_y, layer5_y)
# Agent智能层 -> RAG知识层
draw_layer_arrow(ax, layer5_y, layer4_y)
# RAG知识层 -> 应用开发层
draw_layer_arrow(ax, layer4_y, layer3_y)
# 应用开发层 -> 容器化层
draw_layer_arrow(ax, layer3_y, layer2_y)
# 容器化层 -> 模型基础层
draw_layer_arrow(ax, layer2_y, layer1_y)

# 保存图像为PNG文件，设置高DPI和白色背景
plt.savefig('智能模型平台技术架构图_v4.png', dpi=300, bbox_inches='tight', 
            facecolor=COLORS['background'], edgecolor='none')
# 显示图形
plt.show()
