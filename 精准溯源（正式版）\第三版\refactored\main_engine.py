"""
Main Analysis Engine for Heavy Metal Pollution Source Apportionment

This module provides the main analysis engine that orchestrates the complete
pollution source apportionment workflow for Environmental Science & Technology
journal publication standards.

Authors: <AUTHORS>
Version: 3.0 (Refactored)
"""

import numpy as np
import pandas as pd
import logging
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime

from config import ConfigManager, get_config_manager, setup_environment
from data_processor import DataProcessor
from source_apportionment import SourceApportionmentEngine, SourceApportionmentResult
from visualization import PublicationVisualizer


class AnalysisReport:
    """Comprehensive analysis report for publication."""
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize analysis report.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.timestamp = datetime.now()
        self.results = {}
        self.metadata = {}
        self.validations = {}
        
    def add_result(self, name: str, result: any) -> None:
        """Add analysis result."""
        self.results[name] = result
    
    def add_metadata(self, key: str, value: any) -> None:
        """Add metadata information."""
        self.metadata[key] = value
    
    def add_validation(self, name: str, validation: Dict[str, bool]) -> None:
        """Add validation results."""
        self.validations[name] = validation
    
    def generate_summary(self) -> Dict:
        """Generate analysis summary."""
        summary = {
            'timestamp': self.timestamp.isoformat(),
            'configuration': {
                'model': self.config.get_model_config().__dict__,
                'data': self.config.get_data_config().__dict__
            },
            'metadata': self.metadata,
            'validations': self.validations,
            'results_summary': {}
        }
        
        # Add results summary
        if 'source_apportionment' in self.results:
            result = self.results['source_apportionment']
            if isinstance(result, SourceApportionmentResult):
                summary['results_summary'] = {
                    'n_sources': result.source_profiles.shape[0],
                    'n_samples': result.source_contributions.shape[0],
                    'n_metals': result.source_profiles.shape[1],
                    'model_metrics': result.model_metrics,
                    'source_interpretations': result.source_interpretations
                }
        
        return summary
    
    def export_to_json(self, output_path: str) -> None:
        """Export report to JSON file."""
        summary = self.generate_summary()
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False, default=str)


class PollutionSourceAnalyzer:
    """Main pollution source analysis engine."""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize pollution source analyzer.
        
        Args:
            config_file: Path to configuration file
        """
        # Setup environment
        setup_environment()
        
        # Initialize configuration
        self.config = get_config_manager(config_file)
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        self.logger.info("Pollution Source Analyzer initialized")
        
        # Initialize components
        self.data_processor = DataProcessor(self.config)
        self.source_engine = SourceApportionmentEngine(self.config)
        self.visualizer = PublicationVisualizer(self.config)
        
        # Analysis state
        self.datasets = {}
        self.results = None
        self.report = AnalysisReport(self.config)
    
    def load_and_validate_data(self, data_dir: Optional[str] = None) -> Dict[str, pd.DataFrame]:
        """
        Load and validate all datasets.
        
        Args:
            data_dir: Data directory path
            
        Returns:
            Dictionary of validated datasets
        """
        self.logger.info("Loading and validating data")
        
        # Load data
        self.datasets = self.data_processor.load_data(data_dir)
        
        if not self.datasets:
            raise ValueError("No datasets loaded successfully")
        
        # Check for required datasets
        if 'soil' not in self.datasets:
            raise ValueError("Soil dataset is required but not found")
        
        # Preprocess data
        self.datasets = self.data_processor.preprocess_data(self.datasets)
        
        # Add metadata
        self.report.add_metadata('datasets_loaded', list(self.datasets.keys()))
        self.report.add_metadata('data_shapes', {
            name: df.shape for name, df in self.datasets.items()
        })
        
        # Export quality reports
        output_dir = self.config.get_data_config().output_dir
        self.data_processor.export_quality_reports(output_dir)
        
        self.logger.info(f"Loaded {len(self.datasets)} datasets successfully")
        return self.datasets
    
    def prepare_source_data(self) -> pd.DataFrame:
        """
        Prepare combined source data from all source datasets.
        
        Returns:
            Combined source DataFrame
        """
        source_datasets = {k: v for k, v in self.datasets.items() if k != 'soil'}
        
        if not source_datasets:
            raise ValueError("No source datasets available")
        
        # Combine all source data
        all_sources = pd.DataFrame()
        for source_type, df in source_datasets.items():
            if not df.empty:
                df_copy = df.copy()
                df_copy['source_type'] = source_type
                all_sources = pd.concat([all_sources, df_copy], ignore_index=True)
        
        if all_sources.empty:
            raise ValueError("Combined source data is empty")
        
        self.logger.info(f"Prepared source data: {all_sources.shape}")
        return all_sources
    
    def run_source_apportionment(self, include_uncertainty: bool = True) -> SourceApportionmentResult:
        """
        Run complete source apportionment analysis.
        
        Args:
            include_uncertainty: Whether to include uncertainty analysis
            
        Returns:
            Source apportionment results
        """
        if not self.datasets:
            raise ValueError("Data must be loaded first")
        
        self.logger.info("Running source apportionment analysis")
        
        # Prepare data
        soil_data = self.datasets['soil']
        source_data = self.prepare_source_data()
        
        # Run analysis
        self.results = self.source_engine.analyze(
            soil_data, 
            source_data, 
            include_uncertainty=include_uncertainty
        )
        
        # Validate results
        validations = self.source_engine.validate_results(self.results)
        self.report.add_validation('source_apportionment', validations)
        
        # Log validation results
        failed_validations = [k for k, v in validations.items() if not v]
        if failed_validations:
            self.logger.warning(f"Failed validations: {failed_validations}")
        else:
            self.logger.info("All validations passed")
        
        # Add to report
        self.report.add_result('source_apportionment', self.results)
        
        self.logger.info("Source apportionment analysis completed")
        return self.results
    
    def generate_visualizations(self, output_dir: Optional[str] = None) -> None:
        """
        Generate all publication-quality visualizations.
        
        Args:
            output_dir: Output directory for figures
        """
        if self.results is None:
            raise ValueError("Analysis must be run first")
        
        if output_dir is None:
            output_dir = self.config.get_data_config().output_dir
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        self.logger.info("Generating visualizations")
        
        metals = self.config.get_data_config().metals
        soil_data = self.datasets['soil']
        receptor_data = soil_data[metals].values
        
        # Generate individual plots
        self.visualizer.plot_source_profiles(
            self.results, metals,
            output_path / 'source_profiles.png'
        )
        
        self.visualizer.plot_contribution_matrix(
            self.results,
            output_path=output_path / 'contribution_matrix.png'
        )
        
        self.visualizer.plot_similarity_matrix(
            self.results,
            output_path=output_path / 'similarity_matrix.png'
        )
        
        self.visualizer.plot_model_diagnostics(
            self.results, receptor_data, metals,
            output_path=output_path / 'model_diagnostics.png'
        )
        
        # Generate comprehensive publication figure
        self.visualizer.create_publication_figure(
            self.results, receptor_data, metals,
            output_path=output_path / 'publication_figure.png'
        )
        
        self.logger.info(f"Visualizations saved to {output_path}")
    
    def export_results(self, output_dir: Optional[str] = None) -> None:
        """
        Export all results to files.
        
        Args:
            output_dir: Output directory
        """
        if self.results is None:
            raise ValueError("Analysis must be run first")
        
        if output_dir is None:
            output_dir = self.config.get_data_config().output_dir
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        self.logger.info("Exporting results")
        
        # Export source profiles
        metals = self.config.get_data_config().metals
        profiles_df = pd.DataFrame(
            self.results.source_profiles,
            columns=metals,
            index=[f'Source_{i+1}' for i in range(self.results.source_profiles.shape[0])]
        )
        profiles_df.to_csv(output_path / 'source_profiles.csv')
        
        # Export source contributions
        contributions_df = pd.DataFrame(
            self.results.source_contributions,
            columns=[f'Source_{i+1}' for i in range(self.results.source_contributions.shape[1])]
        )
        contributions_df.to_csv(output_path / 'source_contributions.csv')
        
        # Export similarity matrix
        similarity_df = pd.DataFrame(self.results.similarity_matrix)
        similarity_df.to_csv(output_path / 'similarity_matrix.csv')
        
        # Export uncertainty bounds if available
        if self.results.uncertainty_bounds:
            for key, values in self.results.uncertainty_bounds.items():
                uncertainty_df = pd.DataFrame(values)
                uncertainty_df.to_csv(output_path / f'{key}.csv')
        
        # Export analysis report
        self.report.export_to_json(output_path / 'analysis_report.json')
        
        self.logger.info(f"Results exported to {output_path}")
    
    def run_complete_analysis(self, data_dir: Optional[str] = None,
                            output_dir: Optional[str] = None,
                            include_uncertainty: bool = True,
                            generate_plots: bool = True) -> SourceApportionmentResult:
        """
        Run complete analysis workflow.
        
        Args:
            data_dir: Data directory path
            output_dir: Output directory path
            include_uncertainty: Whether to include uncertainty analysis
            generate_plots: Whether to generate visualizations
            
        Returns:
            Source apportionment results
        """
        try:
            self.logger.info("Starting complete analysis workflow")
            
            # Load and validate data
            self.load_and_validate_data(data_dir)
            
            # Run source apportionment
            results = self.run_source_apportionment(include_uncertainty)
            
            # Generate visualizations
            if generate_plots:
                self.generate_visualizations(output_dir)
            
            # Export results
            self.export_results(output_dir)
            
            self.logger.info("Complete analysis workflow finished successfully")
            return results
            
        except Exception as e:
            self.logger.error(f"Analysis workflow failed: {e}")
            raise
    
    def get_summary_statistics(self) -> Dict:
        """Get summary statistics of the analysis."""
        if self.results is None:
            raise ValueError("Analysis must be run first")
        
        return self.report.generate_summary()


def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description='Heavy Metal Pollution Source Apportionment Analysis'
    )
    parser.add_argument('--data-dir', type=str, default='data',
                       help='Data directory path')
    parser.add_argument('--output-dir', type=str, default='results',
                       help='Output directory path')
    parser.add_argument('--config', type=str, default=None,
                       help='Configuration file path')
    parser.add_argument('--no-uncertainty', action='store_true',
                       help='Skip uncertainty analysis')
    parser.add_argument('--no-plots', action='store_true',
                       help='Skip plot generation')
    
    args = parser.parse_args()
    
    # Initialize analyzer
    analyzer = PollutionSourceAnalyzer(args.config)
    
    # Run analysis
    results = analyzer.run_complete_analysis(
        data_dir=args.data_dir,
        output_dir=args.output_dir,
        include_uncertainty=not args.no_uncertainty,
        generate_plots=not args.no_plots
    )
    
    # Print summary
    summary = analyzer.get_summary_statistics()
    print("\n" + "="*50)
    print("ANALYSIS SUMMARY")
    print("="*50)
    print(f"Number of sources identified: {summary['results_summary']['n_sources']}")
    print(f"Number of samples analyzed: {summary['results_summary']['n_samples']}")
    print(f"Model R²: {summary['results_summary']['model_metrics']['r2']:.3f}")
    print("\nSource interpretations:")
    for source_id, interpretation in summary['results_summary']['source_interpretations'].items():
        print(f"  Source {source_id}: {interpretation}")
    print("="*50)


if __name__ == "__main__":
    main()
