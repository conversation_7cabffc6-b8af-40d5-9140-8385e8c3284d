import pandas as pd
import numpy as np
import random
import os


def generate_soil_data(output_path="heavy_metal_data.csv", num_sites=10):
    """
    生成符合要求的土壤重金属数据（包含area面积字段）
    :param output_path: 输出文件路径
    :param num_sites: 厂区数量
    :return: 生成的数据DataFrame
    """
    # 定义工厂类型、排放量和面积范围
    industry_types = ["化工", "冶金", "电子", "机械", "纺织", "制药"]
    size_ranges = {
        "化工": (50_000, 200_000),  # 5-20万平方米
        "冶金": (100_000, 500_000),  # 10-50万平方米
        "电子": (30_000, 150_000),  # 3-15万平方米
        "机械": (40_000, 180_000),  # 4-18万平方米
        "纺织": (30_000, 120_000),  # 3-12万平方米
        "制药": (20_000, 80_000)  # 2-8万平方米
    }

    emission_ranges = {
        "化工": (80, 150),
        "冶金": (150, 300),
        "电子": (30, 80),
        "机械": (50, 120),
        "纺织": (40, 90),
        "制药": (70, 130)
    }

    # 创建基础地理分布
    site_data = []

    # 为每个厂区创建基本信息
    for site_idx in range(num_sites):
        site_id = site_idx + 1
        site_name = f"site_{site_id}"

        # 随机选择工厂类型
        industry = random.choice(industry_types)

        # 根据类型确定排放量和面积
        min_size, max_size = size_ranges[industry]
        min_emission, max_emission = emission_ranges[industry]

        # 生成厂区面积（单位：平方米）
        area = random.uniform(min_size, max_size)

        # 生成厂区排放量
        emission = random.uniform(min_emission, max_emission)

        # 确定厂区中心坐标（范围与图片相似）
        center_x = random.uniform(500, 1500)
        center_y = random.uniform(700, 1900)

        # 每个厂区采样点数量（15-25个）
        points_per_site = random.randint(15, 25)

        # 为每个采样点创建数据
        for point_idx in range(points_per_site):
            # 在厂区附近随机偏移（类似图片中的范围）
            point_x = center_x + random.uniform(-200, 200)
            point_y = center_y + random.uniform(-200, 200)

            # 每个采样点有2-5个不同深度
            num_depths = random.randint(2, 5)
            depths = np.round(np.random.uniform(5, 170, num_depths), 2)
            depths = sorted(depths, reverse=True)  # 按深度降序排列

            for depth in depths:
                # 基础浓度范围（20-50），添加正负30%的波动
                base_value = random.uniform(20, 50)
                concentration_pb = base_value * (1 + random.uniform(-0.3, 0.3))

                # 添加数据行（包含area字段）
                row = {
                    "site_name": site_name,
                    "x": round(point_x, 10),  # 10位小数，与图片精度一致
                    "y": round(point_y, 10),
                    "depth": round(depth, 10),
                    "concentration_Pb": round(concentration_pb, 10),
                    "industry": industry,
                    "emission": round(emission, 2),
                    "area": round(area, 2)  # 面积字段
                }
                site_data.append(row)

    # 创建DataFrame并保存
    df = pd.DataFrame(site_data)
    df.to_csv(output_path, index=False)

    print(f"成功生成数据：{len(df)}条记录，包含{num_sites}个厂区")

    # 打印文件位置
    abs_path = os.path.abspath(output_path)
    print(f"文件位置: {abs_path}")

    return df


if __name__ == "__main__":
    # 生成数据
    generate_soil_data(
        output_path="heavy_metal_complete_data.csv",
        num_sites=10  # 生成10个厂区
    )