# 解决KMeans内存泄漏问题
import os

os.environ['OMP_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'

import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import NMF
from sklearn.cluster import KMeans
from sklearn.neighbors import kneighbors_graph
from scipy.spatial.distance import cdist
import matplotlib.pyplot as plt
import seaborn as sns
from pyproj import Proj
from pulp import *
import torch
import torch.nn as nn
import torch.optim as optim
from torch_geometric.data import Data
from torch_geometric.nn import GCNConv
import torch.nn.functional as F

# --------------------
# 加载真实数据
# --------------------
def load_real_data(factory_path, sample_path):
    """加载真实污染源和采样点数据"""
    # 读取工厂数据
    factories = pd.read_csv(factory_path)

    # 读取采样点数据
    samples = pd.read_csv(sample_path)

    # 自动检测污染物列
    pollutants = [col for col in samples.columns if col.startswith('Pollutant_')]

    return factories, samples, pollutants


# 设置数据文件路径
factory_file = "D:\\python\\机器学习\\精准溯源\\正式版\\simulated_data\\factories.csv"  # 替换为实际工厂数据路径
sample_file = "D:\\python\\机器学习\\精准溯源\\正式版\\simulated_data\\samples.csv"  # 替换为实际采样点数据路径

# 加载数据
factories, samples, pollutants = load_real_data(factory_file, sample_file)


# --------------------
# 1. 污染源指纹构建
# --------------------
def build_source_fingerprints(factories, pollutants):
    """构建标准化污染源指纹"""
    # 使用RobustScaler处理排放数据
    scaler = RobustScaler(quantile_range=(10, 90))
    source_data = scaler.fit_transform(factories[pollutants])

    # 添加企业类型特征增强
    type_mapping = {'Chemical': 0.1, 'Metal': 0.3, 'Textile': 0.2, 'Electronics': 0.4}
    type_vector = factories['type'].map(type_mapping).values

    # 组合污染物指纹和企业类型特征
    source_fingerprints = np.concatenate([
        source_data,
        np.tile(type_vector.reshape(-1, 1), (1, 3))  # 扩展为三维特征
    ], axis=1)

    # 按污染物总和标准化到[0,1]范围
    row_sums = source_fingerprints.sum(axis=1, keepdims=True)
    source_fingerprints = np.divide(source_fingerprints, row_sums,
                                    out=np.zeros_like(source_fingerprints),
                                    where=row_sums != 0)

    return source_fingerprints


source_fingerprints = build_source_fingerprints(factories, pollutants)
factory_ids = factories['factory_id'].values


# --------------------
# 2. 采样点指纹构建
# --------------------
def build_receptor_fingerprints(samples, pollutants):
    """构建标准化采样点指纹"""
    # 确保污染物数据是数值类型
    sample_data = samples[pollutants].apply(pd.to_numeric, errors='coerce')

    # 检查并处理NaN值
    nan_count = sample_data.isna().sum().sum()
    if nan_count > 0:
        print(f"警告: 发现 {nan_count} 个NaN值，将使用中位数填充")
        sample_data = sample_data.fillna(sample_data.median())

    # 对数变换处理浓度值
    sample_data = np.log1p(sample_data)

    # 异常值处理 - 应用Winsorization
    q_low = np.percentile(sample_data, 10, axis=0)
    q_high = np.percentile(sample_data, 90, axis=0)

    # 使用Pandas的clip方法
    clipped_data = sample_data.copy()
    for i, col in enumerate(clipped_data.columns):
        clipped_data[col] = clipped_data[col].clip(lower=q_low[i], upper=q_high[i])

    # 标准化到[0,1]范围
    scaler = StandardScaler()
    receptor_fingerprints = scaler.fit_transform(clipped_data)

    # 按比例重新标准化
    row_sums = receptor_fingerprints.sum(axis=1, keepdims=True)
    receptor_fingerprints = np.divide(receptor_fingerprints, row_sums,
                                      out=np.zeros_like(receptor_fingerprints),
                                      where=row_sums != 0)

    return receptor_fingerprints


receptor_fingerprints = build_receptor_fingerprints(samples, pollutants)
sample_ids = samples['sample_id'].values


# --------------------
# 3. 指纹相似度分析
# --------------------
def calculate_fingerprint_similarity(source_fp, receptor_fp, pollutants):
    """计算污染源与采样点的指纹相似度"""
    # 只使用污染物特征部分进行相似度计算
    source_fp_pollutants = source_fp[:, :len(pollutants)]

    # 计算余弦相似度
    cos_sim = cosine_similarity(receptor_fp, source_fp_pollutants)

    # 计算欧氏距离相似度 (转换为0-1相似度)
    euclidean_dist = cdist(receptor_fp, source_fp_pollutants, 'euclidean')
    max_dist = np.max(euclidean_dist)
    euclidean_sim = 1 - (euclidean_dist / max_dist)

    # 组合相似度 (加权平均)
    combined_sim = 0.7 * cos_sim + 0.3 * euclidean_sim

    return combined_sim


similarity_matrix = calculate_fingerprint_similarity(
    source_fingerprints,
    receptor_fingerprints,
    pollutants
)


# --------------------
# 4. PMF源解析模型
# --------------------
def pmf_source_apportionment(samples, pollutants, n_factors):
    """使用NMF进行源解析"""
    # 准备数据矩阵
    X = samples[pollutants].values

    # 根据因子数量选择合适的初始化方法
    min_dim = min(X.shape)
    init_method = 'nndsvd' if n_factors <= min_dim else 'random'

    # 应用非负矩阵分解
    model = NMF(n_components=n_factors, init=init_method, max_iter=1000)
    W = model.fit_transform(X)  # 源贡献矩阵
    H = model.components_  # 源成分谱

    # 将NMF结果标准化
    W = W / W.sum(axis=1)[:, np.newaxis]
    H = H / H.sum(axis=1)[:, np.newaxis]

    # 匹配NMF源与实际工厂
    factory_mapping = {}
    for factor_idx in range(n_factors):
        # 找到最匹配的工厂
        factor_profile = H[factor_idx]
        similarities = cosine_similarity(factor_profile.reshape(1, -1), source_fingerprints[:, :len(pollutants)])
        best_match = np.argmax(similarities)
        factory_mapping[factor_idx] = factory_ids[best_match]

    return W, H, factory_mapping


n_factors = len(factories)
W, H, factory_mapping = pmf_source_apportionment(samples, pollutants, n_factors)


# --------------------
# 5. 深度聚类分析
# --------------------
class Autoencoder(nn.Module):
    """自编码器用于学习污染模式特征"""

    def __init__(self, input_dim, encoding_dim=8):
        super(Autoencoder, self).__init__()
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, 32),
            nn.ReLU(),
            nn.Linear(32, encoding_dim),
            nn.ReLU()
        )
        self.decoder = nn.Sequential(
            nn.Linear(encoding_dim, 32),
            nn.ReLU(),
            nn.Linear(32, input_dim),
            nn.Sigmoid()
        )

    def forward(self, x):
        encoded = self.encoder(x)
        decoded = self.decoder(encoded)
        return decoded, encoded


def train_autoencoder(receptor_fp, encoding_dim=8, epochs=200):
    input_dim = receptor_fp.shape[1]
    model = Autoencoder(input_dim, encoding_dim)
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    tensor_data = torch.tensor(receptor_fp, dtype=torch.float32)

    for epoch in range(epochs):
        optimizer.zero_grad()
        reconstructed, encoded = model(tensor_data)
        loss = criterion(reconstructed, tensor_data)
        loss.backward()
        optimizer.step()

    with torch.no_grad():
        _, encoded_features = model(tensor_data)

    return encoded_features.numpy()


encoded_features = train_autoencoder(receptor_fingerprints)


def cluster_analysis(features, n_clusters=None):
    """执行GMM聚类分析"""
    if n_clusters is None:
        # 自动确定聚类数量
        distortions = []
        max_clusters = min(10, len(features) - 1)
        for i in range(1, max_clusters):
            km = KMeans(n_clusters=i, random_state=0)
            km.fit(features)
            distortions.append(km.inertia_)

        # 寻找拐点
        diff = np.diff(distortions)
        n_clusters = np.argmax(diff > 0.1 * max(diff)) + 2

    kmeans = KMeans(n_clusters=n_clusters, random_state=42)
    clusters = kmeans.fit_predict(features)
    return clusters


sample_clusters = cluster_analysis(encoded_features)


# --------------------
# 6. 空间图神经网络
# --------------------
def build_graph_data(samples, features):
    """构建图神经网络所需数据结构"""
    # 节点特征
    x = torch.tensor(features, dtype=torch.float)

    # 构建空间位置
    coords = samples[['lon', 'lat']].values

    # 计算距离 (使用平面投影)
    p = Proj(proj='aeqd', ellps='WGS84', lat_0=coords[:, 1].mean(), lon_0=coords[:, 0].mean())
    x_proj, y_proj = p(coords[:, 0], coords[:, 1])
    projected_coords = np.column_stack([x_proj, y_proj])

    # 创建K近邻图 (每个点连接8个最近邻居)
    edge_index = kneighbors_graph(
        projected_coords, n_neighbors=8, mode='connectivity'
    ).tocoo()

    edge_index = torch.tensor(np.array([edge_index.row, edge_index.col]), dtype=torch.long)

    return Data(x=x, edge_index=edge_index, pos=torch.tensor(projected_coords, dtype=torch.float))


class GCN(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim):
        super(GCN, self).__init__()
        self.conv1 = GCNConv(input_dim, hidden_dim)
        self.conv2 = GCNConv(hidden_dim, output_dim)

    def forward(self, data):
        x, edge_index = data.x, data.edge_index
        x = self.conv1(x, edge_index)
        x = torch.relu(x)
        x = self.conv2(x, edge_index)
        return x


def train_gnn(data, hidden_dim=16, output_dim=8, epochs=100):
    """训练GNN获取空间增强特征"""
    model = GCN(input_dim=data.x.size(1), hidden_dim=hidden_dim, output_dim=output_dim)
    optimizer = optim.Adam(model.parameters(), lr=0.01)

    model.train()
    for epoch in range(epochs):
        optimizer.zero_grad()
        out = model(data)

        # 计算邻居相似度 - 使用PyTorch内置函数
        neighbor_sim = F.cosine_similarity(
            out[data.edge_index[0]],
            out[data.edge_index[1]]
        )

        # 无监督损失 - 鼓励空间连续性
        loss = 1 - torch.mean(neighbor_sim)
        loss.backward()
        optimizer.step()

    model.eval()
    with torch.no_grad():
        embeddings = model(data)
    return embeddings.detach().numpy()


graph_data = build_graph_data(samples, receptor_fingerprints)
gnn_embeddings = train_gnn(graph_data)
spatial_clusters = cluster_analysis(gnn_embeddings)


# --------------------
# 7. 最优传输模型
# --------------------
def optimal_transport_model(sources, receptors, samples_data, factories_data, pollutants):
    """使用最优传输量化污染贡献"""
    # 准备工厂和采样点位置
    factory_coords = factories_data[['lon', 'lat']].values
    sample_coords = samples_data[['lon', 'lat']].values

    # 计算空间距离成本
    spatial_dist = cdist(sample_coords, factory_coords, 'euclidean')
    max_dist = np.max(spatial_dist)
    spatial_cost = spatial_dist / max_dist  # 标准化到[0,1]

    # 计算指纹差异成本 (基于相似度)
    sources_pollutants = sources[:, :len(pollutants)]
    similarity = cosine_similarity(receptors, sources_pollutants)
    fingerprint_cost = 1 - similarity  # 转化为成本

    # 组合成本函数
    total_cost = 0.6 * spatial_cost + 0.4 * fingerprint_cost

    # 创建线性规划问题
    prob = LpProblem("Pollution_Attribution", LpMinimize)

    # 决策变量: 工厂i对采样点j的贡献比例
    num_samples = len(samples_data)
    num_factories = len(factories_data)
    var_names = [f'x_{i}_{j}' for i in range(num_samples) for j in range(num_factories)]
    variables = LpVariable.dicts("Contrib", var_names, 0)

    # 目标函数: 最小化传输成本
    prob += lpSum([variables[f'x_{i}_{j}'] * total_cost[i, j]
                   for i in range(num_samples) for j in range(num_factories)])

    # 约束条件1: 每个采样点的污染100%来自各个工厂
    for i in range(num_samples):
        prob += lpSum([variables[f'x_{i}_{j}'] for j in range(num_factories)]) == 1

    # 约束条件2: 工厂总贡献不超过其排放能力（相对比例）
    for j in range(num_factories):
        factory_total = factories.iloc[j][pollutants].sum()
        # 假设每个工厂的最大贡献不超过其相对排放能力的1.2倍
        prob += lpSum([variables[f'x_{i}_{j}'] * samples_data[pollutants].iloc[i].sum()
                       for i in range(num_samples)]) <= 1.2 * factory_total

    # 求解问题
    status = prob.solve(PULP_CBC_CMD(msg=False))

    # 提取结果
    contribution_matrix = np.zeros((num_samples, num_factories))
    for i in range(num_samples):
        for j in range(num_factories):
            var_name = f'x_{i}_{j}'
            contribution_matrix[i, j] = variables[var_name].varValue

    return contribution_matrix


# 使用真实数据计算污染贡献矩阵
contribution_matrix = optimal_transport_model(
    source_fingerprints,
    receptor_fingerprints,
    samples,
    factories,
    pollutants
)


# --------------------
# 8. 结果整合与可视化
# --------------------
def integrate_results(samples, factories, contribution_matrix, similarity_matrix, sample_clusters, spatial_clusters):
    """整合多种方法结果并生成最终溯源报告"""
    # 创建结果数据框
    results = samples[['sample_id', 'lon', 'lat']].copy()

    # 添加最优传输贡献结果
    for j, factory_id in enumerate(factories['factory_id']):
        results[f'contrib_{factory_id}'] = contribution_matrix[:, j]

    # 添加指纹相似度结果
    top_factories = similarity_matrix.argsort(axis=1)[:, -3:][:, ::-1]
    for i in range(3):
        results[f'top_{i + 1}_match'] = [factories.iloc[top_factories[j, i]]['factory_id'] for j in range(len(results))]
        results[f'top_{i + 1}_sim'] = [similarity_matrix[j, top_factories[j, i]] for j in range(len(results))]

    # 添加聚类结果
    results['pollution_cluster'] = sample_clusters
    results['spatial_cluster'] = spatial_clusters

    # 创建置信度列并明确指定为浮点类型
    results['confidence'] = pd.Series(dtype='float64')

    # 计算综合置信度
    for i, row in results.iterrows():
        # 获取主要贡献工厂
        contrib_columns = [f'contrib_{f}' for f in factories['factory_id']]
        main_factory = results.iloc[i][contrib_columns].idxmax()
        main_factory_id = main_factory.split('_')[1]

        # 检查是否在相似度前三匹配中
        in_top3 = main_factory_id in [row['top_1_match'], row['top_2_match'], row['top_3_match']]

        # 检查空间聚类是否一致
        cluster_samples = results[results['spatial_cluster'] == row['spatial_cluster']]
        factory_distances = cdist(
            [[row['lon'], row['lat']]],
            factories[['lon', 'lat']].values
        )[0]
        closest_factory = factories.iloc[np.argmin(factory_distances)]['factory_id']
        spatial_match = main_factory_id == closest_factory

        # 计算置信度 (0-5星)
        base_confidence = float(row[main_factory])  # 确保为浮点数
        if in_top3: base_confidence += 0.15
        if spatial_match: base_confidence += 0.25

        # 计算最终置信度
        confidence_value = min(base_confidence * 5, 5)
        results.at[i, 'confidence'] = confidence_value

    return results


attribution_report = integrate_results(
    samples,
    factories,
    contribution_matrix,
    similarity_matrix,
    sample_clusters,
    spatial_clusters
)


def visualize_results(samples, factories, report, pollutants):
    """创建多维度可视化分析"""
    plt.figure(figsize=(18, 14))

    # 1. 空间污染分布与工厂位置
    plt.subplot(2, 2, 1)

    # 计算污染物总和并确保非负
    total_pollution = samples[pollutants].sum(axis=1)
    total_pollution = np.maximum(total_pollution, 0)  # 确保非负

    # 使用安全的log1p计算
    log_total = np.zeros_like(total_pollution)
    for i, val in enumerate(total_pollution):
        if val > 0:
            log_total[i] = np.log1p(val)
        else:
            log_total[i] = 0

    # 添加log_total到samples副本中用于绘图
    samples_plot = samples.copy()
    samples_plot['log_total'] = log_total

    sns.scatterplot(
        x='lon', y='lat',
        size='log_total',
        hue='log_total',
        data=samples_plot, palette='viridis', alpha=0.7
    )
    plt.scatter(
        factories['lon'], factories['lat'],
        s=100, c='red', marker='X', label='Factories'
    )
    plt.title('Pollution Distribution and Factory Locations')
    plt.xlabel('Longitude')
    plt.ylabel('Latitude')
    plt.legend()

    # 2. 聚类分析结果
    plt.subplot(2, 2, 2)
    sns.scatterplot(
        x='lon', y='lat',
        hue='pollution_cluster', style='spatial_cluster',
        palette='tab10', data=report, s=80
    )
    plt.scatter(
        factories['lon'], factories['lat'],
        s=100, c='black', marker='X', label='Factories'
    )
    plt.title('Pollution and Spatial Clusters')
    plt.legend()

    # 3. 工厂贡献热力图
    plt.subplot(2, 2, 3)
    contrib_columns = [f'contrib_{fid}' for fid in factories['factory_id']]
    contrib_data = report[contrib_columns]
    sns.heatmap(
        contrib_data.T,
        cmap='YlOrRd',
        yticklabels=factories['factory_id'],
        cbar_kws={'label': 'Contribution Ratio'}
    )
    plt.title('Factory Contribution to Sampling Points')
    plt.ylabel('Factory ID')
    plt.xlabel('Sample Index')

    # 4. 置信度分布
    plt.subplot(2, 2, 4)
    sns.histplot(report['confidence'], bins=20, kde=True)
    plt.title('Confidence Level Distribution')
    plt.xlabel('Confidence (0-5)')

    plt.tight_layout()
    plt.savefig('pollution_source_attribution.png', dpi=300)
    plt.close()

    # 导出详细结果
    report.to_csv('pollution_attribution_report.csv', index=False)


# 使用真实数据执行可视化
visualize_results(samples, factories, attribution_report, pollutants)


def main():
    """主函数：执行污染源解析全流程"""
    # 1. 加载真实数据
    factory_file = "D:\\python\\机器学习\\精准溯源\\正式版\\simulated_data\\factories.csv"  # 替换为实际工厂数据路径
    sample_file = "D:\\python\\机器学习\\精准溯源\\正式版\\simulated_data\\samples.csv"  # 替换为实际采样点数据路径
    factories, samples, pollutants = load_real_data(factory_file, sample_file)

    # 2. 构建污染源指纹
    source_fingerprints = build_source_fingerprints(factories, pollutants)

    # 3. 构建采样点指纹
    receptor_fingerprints = build_receptor_fingerprints(samples, pollutants)

    # 4. 计算指纹相似度
    similarity_matrix = calculate_fingerprint_similarity(
        source_fingerprints,
        receptor_fingerprints,
        pollutants
    )

    # 5. PMF源解析
    n_factors = len(factories)
    W, H, factory_mapping = pmf_source_apportionment(samples, pollutants, n_factors)

    # 6. 深度聚类分析
    encoded_features = train_autoencoder(receptor_fingerprints)
    sample_clusters = cluster_analysis(encoded_features)

    # 7. 空间图神经网络
    graph_data = build_graph_data(samples, receptor_fingerprints)
    gnn_embeddings = train_gnn(graph_data)
    spatial_clusters = cluster_analysis(gnn_embeddings)

    # 8. 最优传输模型
    contribution_matrix = optimal_transport_model(
        source_fingerprints,
        receptor_fingerprints,
        samples,
        factories,
        pollutants
    )

    # 9. 结果整合
    attribution_report = integrate_results(
        samples,
        factories,
        contribution_matrix,
        similarity_matrix,
        sample_clusters,
        spatial_clusters
    )

    # 10. 可视化
    visualize_results(samples, factories, attribution_report, pollutants)

    print("污染源解析完成！结果已保存至当前目录")


if __name__ == "__main__":
    # 设置环境变量解决KMeans内存泄漏问题
    os.environ['OMP_NUM_THREADS'] = '1'
    os.environ['MKL_NUM_THREADS'] = '1'

    # 执行主函数
    main()