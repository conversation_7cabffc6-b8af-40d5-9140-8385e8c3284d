#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速NaN预处理修复验证

快速测试增强的NaN处理是否解决了"数据预处理后仍存在NaN值"错误
"""

import pandas as pd
import numpy as np
import logging
import sys
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_preprocessing_with_real_data():
    """使用真实数据测试预处理"""
    print("=" * 50)
    print("测试真实数据预处理")
    print("=" * 50)
    
    # 查找数据文件
    possible_files = [
        "空间预测/soil_heavy_metal_data.csv",
        "soil_heavy_metal_data.csv",
        "data.csv"
    ]
    
    csv_path = None
    for file_path in possible_files:
        if os.path.exists(file_path):
            csv_path = file_path
            break
    
    if csv_path is None:
        print("❌ 未找到数据文件，创建测试数据...")
        return test_preprocessing_with_synthetic_data()
    
    print(f"使用数据文件: {csv_path}")
    
    try:
        # 导入增强的预处理器
        from train_tsisp import Config, SoilDataPreprocessor
        
        # 设置配置
        Config.set_target_metal('Pb')
        
        print(f"目标金属: {Config.target_metal}")
        print("开始预处理...")
        
        # 这里应该会触发我们的增强NaN处理逻辑
        preprocessor = SoilDataPreprocessor(csv_path)
        
        print("✅ 预处理成功完成！")
        
        # 获取预处理后的数据
        processed_data = preprocessor.get_preprocessed_data()
        print(f"预处理后数据形状: {processed_data.shape}")
        
        # 检查NaN
        total_nan = processed_data.isnull().sum().sum()
        print(f"剩余NaN数量: {total_nan}")
        
        if total_nan == 0:
            print("✅ 所有NaN值已成功处理")
            return True
        else:
            print("❌ 仍有NaN值:")
            for col in processed_data.columns:
                nan_count = processed_data[col].isnull().sum()
                if nan_count > 0:
                    print(f"  {col}: {nan_count}")
            return False
        
    except ValueError as e:
        if "数据预处理后仍存在NaN值" in str(e):
            print(f"❌ 仍然出现目标错误: {e}")
            return False
        else:
            print(f"❌ 其他ValueError: {e}")
            return False
    except Exception as e:
        print(f"❌ 预处理失败: {e}")
        return False

def test_preprocessing_with_synthetic_data():
    """使用合成数据测试预处理"""
    print("=" * 50)
    print("测试合成数据预处理")
    print("=" * 50)
    
    # 创建包含NaN的测试数据
    np.random.seed(42)
    n_samples = 30
    
    data = {
        'site_name': ['Site_A'] * 15 + ['Site_B'] * 15,
        'x': np.random.uniform(0, 100, n_samples),
        'y': np.random.uniform(0, 100, n_samples),
        'depth': np.random.uniform(0.5, 5.0, n_samples),
        'industry': np.random.choice(['mining', 'chemical'], n_samples),
        'emission': np.random.uniform(10, 1000, n_samples),
        'area': np.random.uniform(100, 10000, n_samples),
        'organic': np.random.uniform(1, 10, n_samples),
        'concentration_Pb': np.random.uniform(0, 100, n_samples)
    }
    
    df = pd.DataFrame(data)
    
    # 引入NaN值
    df.loc[5, 'x'] = np.nan
    df.loc[10, 'depth'] = np.nan
    df.loc[15, 'concentration_Pb'] = np.nan
    df.loc[20, 'emission'] = np.nan
    
    print(f"测试数据: {df.shape}, NaN数量: {df.isnull().sum().sum()}")
    
    # 保存临时文件
    temp_file = "temp_test_data.csv"
    df.to_csv(temp_file, index=False)
    
    try:
        from train_tsisp import Config, SoilDataPreprocessor
        
        Config.set_target_metal('Pb')
        
        print("开始预处理...")
        preprocessor = SoilDataPreprocessor(temp_file)
        
        print("✅ 预处理成功完成！")
        
        processed_data = preprocessor.get_preprocessed_data()
        total_nan = processed_data.isnull().sum().sum()
        
        print(f"预处理后NaN数量: {total_nan}")
        
        success = total_nan == 0
        
        if success:
            print("✅ 合成数据测试通过")
        else:
            print("❌ 合成数据测试失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 合成数据测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        try:
            os.remove(temp_file)
        except:
            pass

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 50)
    print("测试边界情况")
    print("=" * 50)
    
    edge_cases = [
        # 案例1：所有坐标都是NaN
        {
            'name': '所有坐标NaN',
            'data': pd.DataFrame({
                'site_name': ['Site_A'] * 5,
                'x': [np.nan] * 5,
                'y': [np.nan] * 5,
                'depth': [1, 2, 3, 4, 5],
                'industry': ['mining'] * 5,
                'emission': [100] * 5,
                'area': [1000] * 5,
                'organic': [5] * 5,
                'concentration_Pb': [10] * 5
            })
        },
        
        # 案例2：所有深度都是NaN
        {
            'name': '所有深度NaN',
            'data': pd.DataFrame({
                'site_name': ['Site_A'] * 5,
                'x': [1, 2, 3, 4, 5],
                'y': [1, 2, 3, 4, 5],
                'depth': [np.nan] * 5,
                'industry': ['mining'] * 5,
                'emission': [100] * 5,
                'area': [1000] * 5,
                'organic': [5] * 5,
                'concentration_Pb': [10] * 5
            })
        },
        
        # 案例3：混合NaN
        {
            'name': '混合NaN',
            'data': pd.DataFrame({
                'site_name': ['Site_A'] * 5,
                'x': [1, np.nan, 3, np.nan, 5],
                'y': [np.nan, 2, np.nan, 4, 5],
                'depth': [1, np.nan, np.nan, 4, 5],
                'industry': ['mining'] * 5,
                'emission': [100, np.nan, 300, 400, 500],
                'area': [1000] * 5,
                'organic': [5] * 5,
                'concentration_Pb': [10, 20, np.nan, np.nan, 50]
            })
        }
    ]
    
    results = []
    
    for case in edge_cases:
        print(f"\n测试: {case['name']}")
        
        # 保存临时文件
        temp_file = f"temp_{case['name'].replace(' ', '_')}.csv"
        case['data'].to_csv(temp_file, index=False)
        
        try:
            from train_tsisp import Config, SoilDataPreprocessor
            
            Config.set_target_metal('Pb')
            
            preprocessor = SoilDataPreprocessor(temp_file)
            processed_data = preprocessor.get_preprocessed_data()
            
            total_nan = processed_data.isnull().sum().sum()
            
            if total_nan == 0:
                print(f"  ✅ {case['name']} 测试通过")
                results.append(True)
            else:
                print(f"  ❌ {case['name']} 仍有 {total_nan} 个NaN")
                results.append(False)
                
        except Exception as e:
            print(f"  ❌ {case['name']} 测试失败: {e}")
            results.append(False)
        finally:
            try:
                os.remove(temp_file)
            except:
                pass
    
    return all(results)

def main():
    """主函数"""
    print("TSISP NaN预处理修复快速验证")
    print("验证增强的NaN处理是否解决了预处理错误\n")
    
    # 测试真实数据
    real_data_success = test_preprocessing_with_real_data()
    
    # 测试边界情况
    edge_cases_success = test_edge_cases()
    
    # 总结
    print("\n" + "=" * 50)
    print("验证结果总结")
    print("=" * 50)
    
    if real_data_success:
        print("✅ 真实数据预处理成功")
    else:
        print("❌ 真实数据预处理失败")
    
    if edge_cases_success:
        print("✅ 边界情况测试通过")
    else:
        print("❌ 边界情况测试失败")
    
    overall_success = real_data_success and edge_cases_success
    
    if overall_success:
        print("\n🎉 NaN预处理修复验证成功！")
        print("'数据预处理后仍存在NaN值'错误已解决")
        print("\n现在可以正常运行:")
        print("python train_tsisp.py --metal Pb --output test_output")
    else:
        print("\n❌ 仍有问题需要解决")
        print("建议运行详细调试: python debug_nan_preprocessing.py")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
