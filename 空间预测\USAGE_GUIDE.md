# TSISP创新架构使用指南

## 概述

本指南详细介绍如何使用TSISP的创新GeoSpatial-GNN架构进行土壤重金属空间预测。新架构相比传统Transformer具有显著的性能提升和计算效率优势。

## 快速开始

### 1. 环境配置

```bash
# 安装依赖
pip install torch torch-geometric
pip install pandas numpy scikit-learn matplotlib seaborn
pip install scipy psutil
```

### 2. 基本使用

#### 单金属训练（创新架构）

```python
from train_tsisp import Config, train_model

# 配置创新架构
Config.architecture = 'geospatial_gnn'  # 使用创新GeoSpatial-GNN
Config.set_target_metal('Pb')  # 设置目标金属

# 训练模型
model, best_epoch = train_model(
    csv_path='soil_data.csv',
    output_dir='pb_gnn_model',
    random_seed=42
)
```

#### 传统架构对比

```python
# 使用传统Transformer架构
Config.architecture = 'transformer'
Config.set_target_metal('Pb')

model, best_epoch = train_model(
    csv_path='soil_data.csv',
    output_dir='pb_transformer_model',
    random_seed=42
)
```

### 3. 批量训练

```python
from train_tsisp import train_multiple_metals

# 批量训练多个金属（使用创新架构）
Config.architecture = 'geospatial_gnn'

trained_models = train_multiple_metals(
    csv_path='soil_data.csv',
    output_base_dir='batch_gnn_models',
    metals_to_train=['Pb', 'Cd', 'Cu', 'Ni'],
    random_seed=42
)
```

## 架构配置详解

### 创新架构参数

```python
# GeoSpatial-GNN 核心参数
Config.gnn_hidden_dim = 128      # GNN隐藏层维度
Config.gnn_num_layers = 4        # GNN层数
Config.gnn_heads = 4             # 图注意力头数
Config.graph_k_neighbors = 8     # K近邻图构建参数
Config.graph_radius = 50.0       # 半径图构建参数（米）

# 学习式空间编码参数
Config.learnable_encoding = True  # 启用学习式编码
Config.encoding_dim = 64          # 编码维度
Config.multi_scale_levels = 3     # 多尺度级别数

# 物理约束参数
Config.physics_weight = 0.1       # 物理约束损失权重
Config.diffusion_coeff = 1e-6     # 扩散系数

# 不确定性量化参数
Config.enable_uncertainty = True  # 启用不确定性量化
Config.mc_dropout_samples = 10    # Monte Carlo采样次数
```

### 传统架构参数

```python
# Transformer 参数
Config.transformer_dim = 256      # Transformer维度
Config.transformer_depth = 6      # Transformer层数
Config.transformer_heads = 8      # 注意力头数

# 傅里叶编码参数
Config.fourier_bands = 16         # 频带数量
Config.fourier_max_freq = 10.0    # 最大频率
```

## 命令行使用

### 单金属训练

```bash
# 使用创新架构训练铅(Pb)
python train_tsisp.py --metal Pb --output pb_gnn_model

# 使用传统架构训练
python train_tsisp.py --metal Pb --output pb_transformer_model --architecture transformer
```

### 批量训练

```bash
# 批量训练多个金属
python train_tsisp.py --batch --metals Pb Cd Cu --output batch_models

# 自动检测数据中的所有金属
python train_tsisp.py --batch --output auto_batch_models
```

### 架构对比实验

```bash
# 运行架构对比实验
python architecture_comparison_experiment.py \
    --data soil_data.csv \
    --metal Pb \
    --output comparison_results \
    --runs 3
```

## 高级功能

### 1. 自定义图构建策略

```python
# 调整图构建参数以适应不同场地
Config.graph_k_neighbors = 12     # 增加邻居数量（密集采样）
Config.graph_radius = 100.0       # 增加半径（大型场地）
```

### 2. 物理约束调优

```python
# 根据污染物类型调整扩散系数
Config.diffusion_coeff = 5e-7     # 重金属（较小扩散系数）
Config.physics_weight = 0.2       # 增加物理约束权重
```

### 3. 不确定性量化设置

```python
# 高精度不确定性量化
Config.mc_dropout_samples = 20    # 增加采样次数
Config.enable_uncertainty = True  # 确保启用
```

## 性能优化建议

### 1. 内存优化

```python
# 对于大型数据集
Config.batch_size = 8             # 减少批次大小
Config.gnn_hidden_dim = 64        # 减少隐藏层维度
```

### 2. 计算效率

```python
# 快速训练设置
Config.epochs = 100               # 减少训练轮次
Config.patience = 15              # 早停耐心值
Config.gnn_num_layers = 3         # 减少GNN层数
```

### 3. 高精度设置

```python
# 高精度训练设置
Config.epochs = 200               # 增加训练轮次
Config.learning_rate = 5e-5       # 降低学习率
Config.gnn_num_layers = 6         # 增加GNN层数
```

## 结果解读

### 1. 训练输出

训练完成后，输出目录包含：
- `best_model.pth`: 最佳模型权重
- `training_history_final.csv`: 训练历史
- `analysis/`: 可视化分析结果
- `checkpoints/`: 模型检查点

### 2. 性能指标

- **R²**: 决定系数，越接近1越好
- **RMSE**: 均方根误差，越小越好
- **MAE**: 平均绝对误差，越小越好
- **不确定性**: 预测置信区间

### 3. 可视化结果

- 训练曲线图
- 性能对比图
- 不确定性分析图
- 空间预测图

## 故障排除

### 1. 内存不足

```python
# 解决方案
Config.batch_size = 4
Config.gnn_hidden_dim = 32
torch.cuda.empty_cache()  # 清理GPU缓存
```

### 2. 收敛问题

```python
# 解决方案
Config.learning_rate = 1e-4       # 调整学习率
Config.warmup_epochs = 20         # 增加预热轮次
Config.patience = 30              # 增加早停耐心值
```

### 3. 图构建失败

```python
# 解决方案
Config.graph_k_neighbors = 5      # 减少邻居数量
Config.graph_radius = 30.0        # 减少半径
```

## 最佳实践

### 1. 数据预处理

- 确保坐标系统一致
- 检查数据完整性
- 处理异常值

### 2. 参数调优

- 从默认参数开始
- 逐步调整关键参数
- 使用验证集评估

### 3. 模型评估

- 使用多个指标评估
- 进行交叉验证
- 分析不确定性

### 4. 生产部署

- 保存完整配置
- 记录训练参数
- 建立监控机制

## 示例脚本

### 完整训练流程

```python
#!/usr/bin/env python
import logging
from train_tsisp import Config, train_model

# 配置日志
logging.basicConfig(level=logging.INFO)

# 设置创新架构
Config.architecture = 'geospatial_gnn'
Config.learnable_encoding = True
Config.enable_uncertainty = True

# 设置目标金属
Config.set_target_metal('Pb')

# 优化参数
Config.gnn_hidden_dim = 128
Config.gnn_num_layers = 4
Config.physics_weight = 0.1

try:
    # 训练模型
    model, best_epoch = train_model(
        csv_path='data/soil_data.csv',
        output_dir='results/pb_gnn',
        random_seed=42
    )
    
    print(f"训练成功完成！最佳轮次: {best_epoch}")
    
except Exception as e:
    print(f"训练失败: {e}")
```

## 技术支持

如有问题，请参考：
1. 架构分析文档：`architecture_innovation_analysis.md`
2. 对比实验脚本：`architecture_comparison_experiment.py`
3. 源代码注释：`train_tsisp.py`

创新架构为土壤污染预测带来了革命性的改进，希望本指南能帮助您充分利用这些先进功能！
