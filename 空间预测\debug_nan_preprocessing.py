#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
NaN预处理调试脚本

专门用于调试和验证增强的NaN处理逻辑
解决"数据预处理后仍存在NaN值"错误
"""

import pandas as pd
import numpy as np
import logging
import traceback
import sys
import os

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('nan_debug.log', mode='w', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def create_test_data_with_nan():
    """创建包含各种NaN情况的测试数据"""
    print("=" * 60)
    print("创建测试数据（包含NaN）")
    print("=" * 60)
    
    # 创建基础数据
    np.random.seed(42)
    n_samples = 50
    
    data = {
        'site_name': ['Site_A'] * 20 + ['Site_B'] * 20 + ['Site_C'] * 10,
        'x': np.random.uniform(0, 100, n_samples),
        'y': np.random.uniform(0, 100, n_samples),
        'depth': np.random.uniform(0.5, 5.0, n_samples),
        'industry': np.random.choice(['mining', 'chemical', 'electronics'], n_samples),
        'emission': np.random.uniform(10, 1000, n_samples),
        'area': np.random.uniform(100, 10000, n_samples),
        'organic': np.random.uniform(1, 10, n_samples),
        'concentration_Pb': np.random.uniform(0, 100, n_samples)
    }
    
    df = pd.DataFrame(data)
    
    # 人为引入各种NaN情况
    print("引入NaN值...")
    
    # 1. 坐标NaN（最严重的情况）
    df.loc[5, 'x'] = np.nan
    df.loc[15, 'y'] = np.nan
    df.loc[25, ['x', 'y']] = np.nan
    
    # 2. 深度NaN
    df.loc[8:10, 'depth'] = np.nan
    
    # 3. 浓度NaN
    df.loc[12:14, 'concentration_Pb'] = np.nan
    
    # 4. 场地属性NaN
    df.loc[18, 'emission'] = np.nan
    df.loc[22, 'area'] = np.nan
    df.loc[28, 'organic'] = np.nan
    
    # 5. 行业NaN
    df.loc[35, 'industry'] = np.nan
    
    # 6. 极端情况：整行几乎都是NaN
    df.loc[40, ['x', 'y', 'depth', 'emission', 'area']] = np.nan
    
    # 7. 数值极值（可能导致计算中的NaN）
    df.loc[45, 'x'] = 1e10
    df.loc[46, 'y'] = -1e10
    df.loc[47, 'depth'] = 0.0  # 可能导致除零
    
    print(f"测试数据创建完成: {df.shape}")
    print(f"总NaN数量: {df.isnull().sum().sum()}")
    
    # 保存测试数据
    test_file = "test_data_with_nan.csv"
    df.to_csv(test_file, index=False)
    print(f"测试数据已保存到: {test_file}")
    
    return test_file

def test_enhanced_preprocessing(csv_path):
    """测试增强的预处理逻辑"""
    print("\n" + "=" * 60)
    print("测试增强的预处理逻辑")
    print("=" * 60)
    
    try:
        # 导入增强的预处理器
        from train_tsisp import Config, SoilDataPreprocessor
        
        # 设置配置
        Config.set_target_metal('Pb')
        Config.site_attributes = ['industry', 'emission', 'area']
        Config.point_attributes = ['organic', 'depth']
        
        print(f"目标金属: {Config.target_metal}")
        print(f"场地属性: {Config.site_attributes}")
        print(f"点属性: {Config.point_attributes}")
        
        # 创建预处理器（这里应该会触发我们的增强NaN处理）
        print("\n开始创建预处理器...")
        preprocessor = SoilDataPreprocessor(csv_path)
        
        print("✅ 预处理器创建成功！")
        
        # 获取预处理后的数据
        processed_data = preprocessor.get_preprocessed_data()
        print(f"预处理后数据形状: {processed_data.shape}")
        
        # 详细检查预处理结果
        print("\n预处理结果检查:")
        print("-" * 40)
        
        total_nan = processed_data.isnull().sum().sum()
        print(f"总NaN数量: {total_nan}")
        
        if total_nan == 0:
            print("✅ 所有NaN值已成功处理")
        else:
            print("❌ 仍有NaN值存在:")
            for col in processed_data.columns:
                nan_count = processed_data[col].isnull().sum()
                if nan_count > 0:
                    print(f"  {col}: {nan_count} 个NaN")
        
        # 检查关键列
        critical_columns = ['local_x', 'local_y', 'depth', 'rel_depth']
        target_col = Config.get_target_metal_column()
        if target_col:
            critical_columns.append(target_col)
        
        print(f"\n关键列检查:")
        print("-" * 40)
        
        for col in critical_columns:
            if col in processed_data.columns:
                nan_count = processed_data[col].isnull().sum()
                inf_count = np.isinf(processed_data[col]).sum()
                min_val = processed_data[col].min()
                max_val = processed_data[col].max()
                
                status = "✅" if nan_count == 0 and inf_count == 0 else "❌"
                print(f"  {status} {col}: NaN={nan_count}, Inf={inf_count}, 范围=[{min_val:.3f}, {max_val:.3f}]")
        
        return True, processed_data
        
    except ValueError as e:
        if "数据预处理后仍存在NaN值" in str(e):
            print(f"❌ 仍然出现NaN错误: {e}")
            return False, None
        else:
            print(f"❌ 其他ValueError: {e}")
            traceback.print_exc()
            return False, None
    except Exception as e:
        print(f"❌ 预处理测试失败: {e}")
        traceback.print_exc()
        return False, None

def test_model_integration(processed_data):
    """测试模型集成"""
    print("\n" + "=" * 60)
    print("测试模型集成")
    print("=" * 60)
    
    if processed_data is None:
        print("❌ 没有预处理数据，跳过模型测试")
        return False
    
    try:
        from train_tsisp import Config, GeoSpatialGNN
        
        # 设置配置
        Config.architecture = 'geospatial_gnn'
        Config.learnable_encoding = True
        
        # 创建模型
        num_industries = 5
        model = GeoSpatialGNN(num_industries)
        
        # 从预处理数据创建测试批次
        batch_size = min(8, len(processed_data))
        sample_data = processed_data.sample(n=batch_size).reset_index(drop=True)
        
        # 构建模型输入
        coords = sample_data[['local_x', 'local_y', 'rel_depth']].values
        site_attrs = sample_data[['industry', 'emission', 'area']].values
        point_attrs = sample_data[['organic', 'depth']].values
        concentrations = sample_data[Config.get_target_metal_column()].values
        
        # 检查输入数据
        print(f"模型输入检查:")
        print(f"  坐标形状: {coords.shape}, NaN数: {np.isnan(coords).sum()}")
        print(f"  场地属性形状: {site_attrs.shape}, NaN数: {np.isnan(site_attrs).sum()}")
        print(f"  点属性形状: {point_attrs.shape}, NaN数: {np.isnan(point_attrs).sum()}")
        print(f"  浓度形状: {concentrations.shape}, NaN数: {np.isnan(concentrations).sum()}")
        
        if np.isnan(coords).any() or np.isnan(site_attrs).any() or np.isnan(point_attrs).any():
            print("❌ 模型输入仍包含NaN值")
            return False
        
        # 转换为torch张量
        import torch
        
        known_batch = {
            'local_coordinates': torch.tensor(coords, dtype=torch.float32),
            'site_attributes': torch.tensor(site_attrs, dtype=torch.float32),
            'point_attributes': torch.tensor(point_attrs, dtype=torch.float32),
            'concentrations': torch.tensor(concentrations, dtype=torch.float32).unsqueeze(1)
        }
        
        all_points = {
            'local_coordinates': torch.tensor(coords, dtype=torch.float32),
            'site_attributes': torch.tensor(site_attrs, dtype=torch.float32),
            'point_attributes': torch.tensor(point_attrs, dtype=torch.float32)
        }
        
        # 测试前向传播
        model.eval()
        with torch.no_grad():
            try:
                final_pred, model_pred, kriging_pred, uncertainty, physics_loss = model(
                    known_batch, all_points, training=False
                )
                
                print(f"✅ 模型前向传播成功")
                print(f"  输出形状: {final_pred.shape}")
                print(f"  输出NaN数: {torch.isnan(final_pred).sum().item()}")
                
                if torch.isnan(final_pred).any():
                    print("❌ 模型输出包含NaN")
                    return False
                else:
                    print("✅ 模型输出无NaN")
                    return True
                
            except Exception as e:
                print(f"❌ 模型前向传播失败: {e}")
                return False
        
    except Exception as e:
        print(f"❌ 模型集成测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("TSISP NaN预处理增强调试工具")
    print("专门测试增强的NaN处理逻辑\n")
    
    # 1. 创建测试数据
    test_csv = create_test_data_with_nan()
    
    # 2. 测试增强的预处理
    preprocessing_success, processed_data = test_enhanced_preprocessing(test_csv)
    
    # 3. 测试模型集成
    model_success = False
    if preprocessing_success:
        model_success = test_model_integration(processed_data)
    
    # 4. 总结结果
    print("\n" + "=" * 60)
    print("调试结果总结")
    print("=" * 60)
    
    if preprocessing_success:
        print("✅ 增强的NaN预处理成功")
    else:
        print("❌ 增强的NaN预处理失败")
    
    if model_success:
        print("✅ 模型集成测试成功")
    else:
        print("❌ 模型集成测试失败")
    
    overall_success = preprocessing_success and model_success
    
    if overall_success:
        print("\n🎉 所有测试通过！NaN处理增强成功！")
        print("\n现在可以安全运行:")
        print("python train_tsisp.py --metal Pb --output test_output")
    else:
        print("\n❌ 仍有问题需要解决")
        print("请检查nan_debug.log文件获取详细信息")
    
    # 清理测试文件
    try:
        os.remove(test_csv)
        print(f"\n清理测试文件: {test_csv}")
    except:
        pass
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
