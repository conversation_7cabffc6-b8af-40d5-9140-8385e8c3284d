"""
Configuration Management for Heavy Metal Pollution Source Apportionment

This module provides centralized configuration management for the pollution source
apportionment research tool, designed for Environmental Science & Technology journal
publication standards.

Authors: <AUTHORS>
Version: 3.0 (Refactored)
License: MIT
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Union
from dataclasses import dataclass, field


@dataclass
class ModelConfig:
    """Model configuration parameters with scientific validation."""
    
    # PMF Parameters
    pmf_n_factors: int = 4
    pmf_max_iter: int = 1000
    pmf_init_method: str = 'nndsvd'
    pmf_tolerance: float = 1e-4
    
    # Spatial Cross-Validation
    spatial_cv_folds: int = 5
    spatial_buffer_distance: float = 10.0  # km
    spatial_cv_method: str = 'spatial_clustering'
    
    # Uncertainty Quantification
    uncertainty_n_estimators: int = 100
    uncertainty_quantiles: List[float] = field(default_factory=lambda: [0.05, 0.95])
    uncertainty_method: str = 'quantile'
    
    # Deep Learning
    dl_hidden_dims: List[int] = field(default_factory=lambda: [128, 64, 32])
    dl_dropout_rate: float = 0.2
    dl_learning_rate: float = 0.001
    dl_epochs: int = 200
    dl_batch_size: int = 32
    
    # Feature Engineering
    include_interactions: bool = True
    spatial_features: bool = True
    statistical_features: bool = True
    
    # Optimization
    optim_n_trials: int = 100
    optim_timeout: int = 3600  # seconds
    
    def validate(self) -> bool:
        """Validate configuration parameters."""
        if self.pmf_n_factors < 2 or self.pmf_n_factors > 10:
            raise ValueError("PMF factors must be between 2 and 10")
        
        if self.spatial_cv_folds < 3:
            raise ValueError("Spatial CV folds must be at least 3")
        
        if not (0.0001 <= self.dl_learning_rate <= 0.1):
            raise ValueError("Learning rate must be between 0.0001 and 0.1")
        
        return True


@dataclass
class DataConfig:
    """Data configuration and paths."""
    
    # Data paths
    data_dir: str = "data"
    output_dir: str = "results"
    
    # File names
    soil_file: str = "soil.csv"
    atmosphere_file: str = "atmosphere.csv"
    irrigation_file: str = "irrigation.csv"
    pesticide_file: str = "pesticide.csv"
    manure_file: str = "manure.csv"
    industry_file: str = "industry.csv"
    
    # Heavy metals
    metals: List[str] = field(default_factory=lambda: [
        'Cu', 'Cr', 'Ni', 'Zn', 'Pb', 'Cd', 'As'
    ])
    
    # Source types
    source_types: List[str] = field(default_factory=lambda: [
        'atmosphere', 'irrigation', 'pesticide', 'manure', 'industry'
    ])
    
    # Coordinate columns
    coord_columns: List[str] = field(default_factory=lambda: ['lon', 'lat'])
    
    def get_data_path(self, filename: str) -> Path:
        """Get full path for data file."""
        return Path(self.data_dir) / filename
    
    def get_output_path(self, filename: str) -> Path:
        """Get full path for output file."""
        return Path(self.output_dir) / filename


@dataclass
class VisualizationConfig:
    """Visualization configuration for publication-quality figures."""
    
    # Figure settings
    figure_dpi: int = 300
    figure_format: str = 'png'
    figure_size: tuple = (12, 8)
    
    # Font settings
    font_family: str = 'serif'
    font_size: int = 12
    title_size: int = 14
    label_size: int = 10
    
    # Color schemes
    color_palette: str = 'viridis'
    categorical_colors: List[str] = field(default_factory=lambda: [
        '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'
    ])
    
    # Academic style
    use_latex: bool = False
    grid_alpha: float = 0.3
    spine_width: float = 1.0


class ConfigManager:
    """Centralized configuration manager for the pollution source apportionment system."""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize configuration manager.
        
        Args:
            config_file: Path to configuration file (JSON format)
        """
        self.model_config = ModelConfig()
        self.data_config = DataConfig()
        self.viz_config = VisualizationConfig()
        
        if config_file and os.path.exists(config_file):
            self.load_from_file(config_file)
        
        self._setup_logging()
        self._validate_configs()
    
    def load_from_file(self, config_file: str) -> None:
        """Load configuration from JSON file."""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # Update configurations
            if 'model' in config_data:
                for key, value in config_data['model'].items():
                    if hasattr(self.model_config, key):
                        setattr(self.model_config, key, value)
            
            if 'data' in config_data:
                for key, value in config_data['data'].items():
                    if hasattr(self.data_config, key):
                        setattr(self.data_config, key, value)
            
            if 'visualization' in config_data:
                for key, value in config_data['visualization'].items():
                    if hasattr(self.viz_config, key):
                        setattr(self.viz_config, key, value)
                        
        except Exception as e:
            logging.warning(f"Failed to load config file {config_file}: {e}")
    
    def save_to_file(self, config_file: str) -> None:
        """Save current configuration to JSON file."""
        config_data = {
            'model': self.model_config.__dict__,
            'data': self.data_config.__dict__,
            'visualization': self.viz_config.__dict__
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
    
    def _setup_logging(self) -> None:
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('pollution_analysis.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    def _validate_configs(self) -> None:
        """Validate all configurations."""
        self.model_config.validate()
        
        # Validate data paths
        data_dir = Path(self.data_config.data_dir)
        if not data_dir.exists():
            logging.warning(f"Data directory {data_dir} does not exist")
        
        # Create output directory if needed
        output_dir = Path(self.data_config.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
    
    def get_model_config(self) -> ModelConfig:
        """Get model configuration."""
        return self.model_config
    
    def get_data_config(self) -> DataConfig:
        """Get data configuration."""
        return self.data_config
    
    def get_viz_config(self) -> VisualizationConfig:
        """Get visualization configuration."""
        return self.viz_config


# Global configuration instance
_config_manager = None

def get_config_manager(config_file: Optional[str] = None) -> ConfigManager:
    """Get global configuration manager instance."""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager(config_file)
    return _config_manager


def setup_environment() -> None:
    """Setup environment variables for optimal performance."""
    os.environ['OMP_NUM_THREADS'] = '1'
    os.environ['MKL_NUM_THREADS'] = '1'
    os.environ['NUMEXPR_NUM_THREADS'] = '1'
    
    # PyTorch settings
    try:
        import torch
        torch.set_num_threads(1)
        torch.backends.cudnn.benchmark = True
    except ImportError:
        pass


# Constants for backward compatibility
METALS = ['Cu', 'Cr', 'Ni', 'Zn', 'Pb', 'Cd', 'As']
SOURCE_TYPES = ['atmosphere', 'irrigation', 'pesticide', 'manure', 'industry']
