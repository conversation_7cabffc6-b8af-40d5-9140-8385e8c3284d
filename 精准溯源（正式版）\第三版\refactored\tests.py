"""
Comprehensive Test Suite for Pollution Source Apportionment

This module provides comprehensive testing for all components of the pollution
source apportionment system, ensuring scientific rigor and reproducibility.

Authors: <AUTHORS>
Version: 3.0 (Refactored)
"""

import unittest
import numpy as np
import pandas as pd
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List

from config import ConfigManager, ModelConfig, DataConfig
from data_processor import DataProcessor, DataValidator
from source_apportionment import (
    FingerprintCalculator, PMFAnalyzer, SourceApportionmentEngine,
    SourceApportionmentResult
)
from main_engine import PollutionSourceAnalyzer


class TestConfigManager(unittest.TestCase):
    """Test configuration management."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config_manager = ConfigManager()
    
    def test_model_config_validation(self):
        """Test model configuration validation."""
        # Valid configuration
        config = ModelConfig()
        self.assertTrue(config.validate())
        
        # Invalid PMF factors
        config.pmf_n_factors = 1
        with self.assertRaises(ValueError):
            config.validate()
        
        # Invalid learning rate
        config.pmf_n_factors = 4
        config.dl_learning_rate = 1.0
        with self.assertRaises(ValueError):
            config.validate()
    
    def test_data_config_paths(self):
        """Test data configuration path handling."""
        config = DataConfig()
        
        # Test path generation
        data_path = config.get_data_path('test.csv')
        self.assertEqual(data_path, Path('data') / 'test.csv')
        
        output_path = config.get_output_path('result.png')
        self.assertEqual(output_path, Path('results') / 'result.png')
    
    def test_config_serialization(self):
        """Test configuration serialization."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_file = f.name
        
        try:
            # Save configuration
            self.config_manager.save_to_file(config_file)
            
            # Load configuration
            new_config = ConfigManager(config_file)
            
            # Compare configurations
            self.assertEqual(
                self.config_manager.get_model_config().pmf_n_factors,
                new_config.get_model_config().pmf_n_factors
            )
        finally:
            Path(config_file).unlink()


class TestDataProcessor(unittest.TestCase):
    """Test data processing functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config_manager = ConfigManager()
        self.data_processor = DataProcessor(self.config_manager)
        
        # Create test data
        self.test_data = self._create_test_data()
    
    def _create_test_data(self) -> pd.DataFrame:
        """Create synthetic test data."""
        np.random.seed(42)
        n_samples = 50
        metals = ['Cu', 'Cr', 'Ni', 'Zn', 'Pb', 'Cd', 'As']
        
        data = {
            'sample': [f'S{i+1:03d}' for i in range(n_samples)],
            'lon': np.random.uniform(100, 120, n_samples),
            'lat': np.random.uniform(30, 40, n_samples)
        }
        
        # Add metal concentrations with realistic correlations
        base_concentrations = np.random.lognormal(2, 1, (n_samples, len(metals)))
        for i, metal in enumerate(metals):
            data[metal] = base_concentrations[:, i]
        
        return pd.DataFrame(data)
    
    def test_data_validation(self):
        """Test data validation functionality."""
        validator = DataValidator(self.config_manager)
        
        # Test with valid data
        report = validator.validate_dataset(self.test_data, 'test')
        self.assertIsInstance(report.missing_values, dict)
        self.assertIsInstance(report.outliers, dict)
        self.assertIsInstance(report.data_range, dict)
    
    def test_data_cleaning(self):
        """Test data cleaning functionality."""
        # Add some problematic data
        dirty_data = self.test_data.copy()
        dirty_data.loc[0, 'Cu'] = -1  # Negative value
        dirty_data.loc[1, 'Cr'] = np.nan  # Missing value
        dirty_data.loc[2, 'Ni'] = 'invalid'  # Invalid string
        
        # Clean data
        cleaned_data = self.data_processor._clean_dataset(dirty_data, 'test')
        
        # Check cleaning results
        self.assertTrue(pd.isna(cleaned_data.loc[0, 'Cu']))  # Negative converted to NaN
        self.assertTrue(pd.isna(cleaned_data.loc[1, 'Cr']))  # NaN preserved
        self.assertTrue(pd.isna(cleaned_data.loc[2, 'Ni']))  # Invalid converted to NaN
    
    def test_anomaly_detection(self):
        """Test anomaly detection methods."""
        validator = DataValidator(self.config_manager)
        
        # Add obvious outliers
        outlier_data = self.test_data.copy()
        metals = self.config_manager.get_data_config().metals
        outlier_data.loc[0, metals] *= 100  # Make first sample an outlier
        
        # Test different methods
        for method in ['isolation_forest', 'local_outlier_factor', 'statistical']:
            anomalies = validator.detect_anomalies(outlier_data, method=method)
            self.assertIsInstance(anomalies, np.ndarray)
            self.assertEqual(len(anomalies), len(outlier_data))
            # First sample should be detected as anomaly
            self.assertTrue(anomalies[0])


class TestSourceApportionment(unittest.TestCase):
    """Test source apportionment functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config_manager = ConfigManager()
        self.fingerprint_calc = FingerprintCalculator(self.config_manager)
        self.pmf_analyzer = PMFAnalyzer(self.config_manager)
        
        # Create test data
        self.soil_data, self.source_data = self._create_test_datasets()
    
    def _create_test_datasets(self) -> tuple:
        """Create synthetic test datasets."""
        np.random.seed(42)
        metals = ['Cu', 'Cr', 'Ni', 'Zn', 'Pb', 'Cd', 'As']
        
        # Soil data (receptors)
        n_soil = 30
        soil_data = pd.DataFrame({
            'sample': [f'Soil_{i+1:03d}' for i in range(n_soil)],
            'lon': np.random.uniform(100, 120, n_soil),
            'lat': np.random.uniform(30, 40, n_soil)
        })
        
        # Add metal concentrations
        for metal in metals:
            soil_data[metal] = np.random.lognormal(2, 0.5, n_soil)
        
        # Source data
        n_sources = 20
        source_data = pd.DataFrame({
            'sample': [f'Source_{i+1:03d}' for i in range(n_sources)],
            'lon': np.random.uniform(100, 120, n_sources),
            'lat': np.random.uniform(30, 40, n_sources),
            'source_type': np.random.choice(['atmosphere', 'irrigation', 'pesticide', 'manure'], n_sources)
        })
        
        # Add metal concentrations with source-specific patterns
        for i, metal in enumerate(metals):
            base_conc = np.random.lognormal(1.5, 0.8, n_sources)
            # Add source-specific variations
            source_types = source_data['source_type'].values
            for j, source_type in enumerate(['atmosphere', 'irrigation', 'pesticide', 'manure']):
                mask = source_types == source_type
                if np.any(mask):
                    # Each source type has different metal patterns
                    multiplier = 1 + j * 0.5 + i * 0.1
                    base_conc[mask] *= multiplier
            
            source_data[metal] = base_conc
        
        return soil_data, source_data
    
    def test_fingerprint_calculation(self):
        """Test fingerprint calculation."""
        metals = self.config_manager.get_data_config().metals
        
        # Test source fingerprints
        source_fingerprints = self.fingerprint_calc.calculate_source_fingerprints(
            self.source_data, metals
        )
        
        self.assertEqual(source_fingerprints.shape[0], len(self.source_data))
        self.assertEqual(source_fingerprints.shape[1], len(metals))
        
        # Check normalization (rows should sum to 1)
        row_sums = np.sum(source_fingerprints, axis=1)
        np.testing.assert_allclose(row_sums, 1.0, rtol=1e-10)
        
        # Test receptor fingerprints
        receptor_fingerprints = self.fingerprint_calc.calculate_receptor_fingerprints(
            self.soil_data, metals
        )
        
        self.assertEqual(receptor_fingerprints.shape[0], len(self.soil_data))
        self.assertEqual(receptor_fingerprints.shape[1], len(metals))
        
        # Check normalization
        row_sums = np.sum(receptor_fingerprints, axis=1)
        np.testing.assert_allclose(row_sums, 1.0, rtol=1e-10)
    
    def test_similarity_calculation(self):
        """Test similarity matrix calculation."""
        metals = self.config_manager.get_data_config().metals
        
        source_fingerprints = self.fingerprint_calc.calculate_source_fingerprints(
            self.source_data, metals
        )
        receptor_fingerprints = self.fingerprint_calc.calculate_receptor_fingerprints(
            self.soil_data, metals
        )
        
        similarity_matrix = self.fingerprint_calc.calculate_similarity_matrix(
            source_fingerprints, receptor_fingerprints
        )
        
        # Check dimensions
        expected_shape = (len(self.soil_data), len(self.source_data))
        self.assertEqual(similarity_matrix.shape, expected_shape)
        
        # Check value range [0, 1]
        self.assertTrue(np.all(similarity_matrix >= 0))
        self.assertTrue(np.all(similarity_matrix <= 1))
    
    def test_pmf_analysis(self):
        """Test PMF analysis."""
        metals = self.config_manager.get_data_config().metals
        receptor_data = self.soil_data[metals].values
        
        # Fit PMF model
        source_contributions, source_profiles = self.pmf_analyzer.fit(receptor_data)
        
        # Check dimensions
        n_factors = self.config_manager.get_model_config().pmf_n_factors
        self.assertEqual(source_contributions.shape, (len(self.soil_data), n_factors))
        self.assertEqual(source_profiles.shape, (n_factors, len(metals)))
        
        # Check non-negativity
        self.assertTrue(np.all(source_contributions >= 0))
        self.assertTrue(np.all(source_profiles >= 0))
        
        # Test source interpretation
        interpretations = self.pmf_analyzer.interpret_sources(source_profiles, metals)
        self.assertEqual(len(interpretations), n_factors)
        self.assertTrue(all(isinstance(v, str) for v in interpretations.values()))
    
    def test_bootstrap_uncertainty(self):
        """Test bootstrap uncertainty estimation."""
        metals = self.config_manager.get_data_config().metals
        receptor_data = self.soil_data[metals].values
        
        # Fit model first
        self.pmf_analyzer.fit(receptor_data)
        
        # Test uncertainty estimation
        uncertainty_bounds = self.pmf_analyzer.bootstrap_uncertainty(
            receptor_data, n_bootstrap=10  # Small number for testing
        )
        
        # Check that all expected keys are present
        expected_keys = ['contributions_lower', 'contributions_upper', 
                        'profiles_lower', 'profiles_upper']
        for key in expected_keys:
            self.assertIn(key, uncertainty_bounds)
        
        # Check dimensions
        n_factors = self.config_manager.get_model_config().pmf_n_factors
        self.assertEqual(uncertainty_bounds['contributions_lower'].shape, 
                        (len(self.soil_data), n_factors))
        self.assertEqual(uncertainty_bounds['profiles_lower'].shape, 
                        (n_factors, len(metals)))


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.data_dir = Path(self.temp_dir) / 'data'
        self.output_dir = Path(self.temp_dir) / 'results'
        
        # Create directories
        self.data_dir.mkdir(parents=True)
        self.output_dir.mkdir(parents=True)
        
        # Create test datasets
        self._create_test_files()
    
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir)
    
    def _create_test_files(self):
        """Create test data files."""
        np.random.seed(42)
        metals = ['Cu', 'Cr', 'Ni', 'Zn', 'Pb', 'Cd', 'As']
        
        # Soil data
        n_soil = 20
        soil_data = pd.DataFrame({
            'sample': [f'Soil_{i+1:03d}' for i in range(n_soil)],
            'lon': np.random.uniform(100, 120, n_soil),
            'lat': np.random.uniform(30, 40, n_soil)
        })
        for metal in metals:
            soil_data[metal] = np.random.lognormal(2, 0.5, n_soil)
        soil_data.to_csv(self.data_dir / 'soil.csv', index=False)
        
        # Source data files
        source_types = ['atmosphere', 'irrigation', 'pesticide', 'manure']
        for source_type in source_types:
            n_sources = 10
            source_data = pd.DataFrame({
                'sample': [f'{source_type}_{i+1:03d}' for i in range(n_sources)],
                'lon': np.random.uniform(100, 120, n_sources),
                'lat': np.random.uniform(30, 40, n_sources)
            })
            for metal in metals:
                source_data[metal] = np.random.lognormal(1.5, 0.8, n_sources)
            source_data.to_csv(self.data_dir / f'{source_type}.csv', index=False)
    
    def test_complete_workflow(self):
        """Test complete analysis workflow."""
        # Initialize analyzer
        analyzer = PollutionSourceAnalyzer()
        
        # Run complete analysis
        results = analyzer.run_complete_analysis(
            data_dir=str(self.data_dir),
            output_dir=str(self.output_dir),
            include_uncertainty=False,  # Skip for speed
            generate_plots=False  # Skip for testing
        )
        
        # Check results
        self.assertIsInstance(results, SourceApportionmentResult)
        
        # Check output files
        expected_files = [
            'source_profiles.csv',
            'source_contributions.csv',
            'similarity_matrix.csv',
            'analysis_report.json'
        ]
        
        for filename in expected_files:
            file_path = self.output_dir / filename
            self.assertTrue(file_path.exists(), f"Missing output file: {filename}")
        
        # Check analysis summary
        summary = analyzer.get_summary_statistics()
        self.assertIn('results_summary', summary)
        self.assertIn('model_metrics', summary['results_summary'])


def run_tests():
    """Run all tests."""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestConfigManager,
        TestDataProcessor,
        TestSourceApportionment,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_tests()
    exit(0 if success else 1)
