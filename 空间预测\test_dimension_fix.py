#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
张量维度修复验证脚本

验证TSISP GeoSpatial-GNN架构的张量维度修复是否成功
"""

import torch
import numpy as np
import logging
import traceback
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dimension_fix():
    """测试维度修复"""
    print("=" * 60)
    print("测试TSISP张量维度修复")
    print("=" * 60)
    
    try:
        # 导入修复后的模块
        from train_tsisp import Config, GeoSpatialGNN, LearnableMultiScaleSpatialEncoder
        
        # 显示当前配置
        print(f"当前配置:")
        print(f"  encoding_dim: {Config.encoding_dim}")
        print(f"  multi_scale_levels: {Config.multi_scale_levels}")
        print(f"  gnn_hidden_dim: {Config.gnn_hidden_dim}")
        print(f"  embedding_dim: {Config.embedding_dim}")
        
        # 设置架构
        Config.architecture = 'geospatial_gnn'
        Config.learnable_encoding = True
        Config.enable_uncertainty = True
        
        # 测试空间编码器
        print(f"\n1. 测试空间编码器...")
        encoder = LearnableMultiScaleSpatialEncoder(
            input_dim=3,
            encoding_dim=Config.encoding_dim,
            num_scales=Config.multi_scale_levels
        )
        
        # 测试不同批次大小
        test_batch_sizes = [1, 16, 32, 100, 500]
        for batch_size in test_batch_sizes:
            coords = torch.randn(batch_size, 3)
            encoded = encoder(coords)
            expected_shape = (batch_size, Config.encoding_dim)
            
            if encoded.shape == expected_shape:
                print(f"  ✓ batch_size={batch_size}: {encoded.shape}")
            else:
                print(f"  ✗ batch_size={batch_size}: 得到{encoded.shape}, 期望{expected_shape}")
                return False
        
        # 测试完整模型
        print(f"\n2. 测试完整GeoSpatial-GNN模型...")
        num_industries = 5
        model = GeoSpatialGNN(num_industries)
        
        # 创建测试数据
        batch_size = 16
        
        # 模拟真实的数据结构
        known_batch = {
            'local_coordinates': torch.randn(batch_size, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size, 1)).float(),  # industry
                torch.randn(batch_size, 2)  # emission, area
            ], dim=1),
            'point_attributes': torch.randn(batch_size, 2),  # organic, depth
            'concentrations': torch.randn(batch_size, 1)
        }
        
        all_points = {
            'local_coordinates': torch.randn(batch_size * 2, 3),
            'site_attributes': torch.cat([
                torch.randint(0, num_industries, (batch_size * 2, 1)).float(),
                torch.randn(batch_size * 2, 2)
            ], dim=1),
            'point_attributes': torch.randn(batch_size * 2, 2)
        }
        
        print(f"测试数据形状:")
        for key, value in known_batch.items():
            print(f"  known_batch[{key}]: {value.shape}")
        for key, value in all_points.items():
            print(f"  all_points[{key}]: {value.shape}")
        
        # 测试前向传播
        print(f"\n3. 测试前向传播...")
        model.eval()
        with torch.no_grad():
            try:
                final_pred, model_pred, kriging_pred, uncertainty, physics_loss = model(
                    known_batch, all_points, training=False
                )
                
                print(f"前向传播成功！输出形状:")
                print(f"  final_pred: {final_pred.shape}")
                print(f"  model_pred: {model_pred.shape}")
                print(f"  kriging_pred: {kriging_pred.shape}")
                if uncertainty is not None:
                    print(f"  uncertainty: {uncertainty.shape}")
                print(f"  physics_loss: {physics_loss.item():.6f}")
                
                # 验证输出形状
                expected_output_shape = (batch_size * 2, 1)
                if final_pred.shape == expected_output_shape:
                    print(f"  ✓ 输出形状正确: {final_pred.shape}")
                else:
                    print(f"  ✗ 输出形状错误: 得到{final_pred.shape}, 期望{expected_output_shape}")
                    return False
                
            except RuntimeError as e:
                if "mat1 and mat2 shapes cannot be multiplied" in str(e):
                    print(f"  ✗ 仍然存在维度不匹配错误: {e}")
                    return False
                else:
                    print(f"  ✗ 其他运行时错误: {e}")
                    return False
        
        # 测试训练模式
        print(f"\n4. 测试训练模式...")
        model.train()
        try:
            final_pred, model_pred, kriging_pred, uncertainty, physics_loss = model(
                known_batch, all_points, training=True
            )
            print(f"  ✓ 训练模式前向传播成功")
            
            # 测试反向传播
            loss = final_pred.mean()  # 简单的损失
            loss.backward()
            print(f"  ✓ 反向传播成功")
            
        except Exception as e:
            print(f"  ✗ 训练模式测试失败: {e}")
            return False
        
        print(f"\n🎉 所有测试通过！维度修复成功！")
        return True
        
    except ImportError as e:
        print(f"✗ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        traceback.print_exc()
        return False

def test_with_different_configs():
    """测试不同的配置"""
    print("\n" + "=" * 60)
    print("测试不同配置的兼容性")
    print("=" * 60)
    
    configs_to_test = [
        {'encoding_dim': 72, 'multi_scale_levels': 3, 'gnn_hidden_dim': 256},
        {'encoding_dim': 96, 'multi_scale_levels': 3, 'gnn_hidden_dim': 256},
        {'encoding_dim': 108, 'multi_scale_levels': 3, 'gnn_hidden_dim': 256},
    ]
    
    try:
        from train_tsisp import Config
        original_config = {
            'encoding_dim': Config.encoding_dim,
            'multi_scale_levels': Config.multi_scale_levels,
            'gnn_hidden_dim': Config.gnn_hidden_dim
        }
        
        for i, config in enumerate(configs_to_test):
            print(f"\n测试配置 {i+1}: {config}")
            
            # 临时修改配置
            for key, value in config.items():
                setattr(Config, key, value)
            
            # 运行测试
            success = test_dimension_fix()
            if success:
                print(f"  ✓ 配置 {i+1} 测试通过")
            else:
                print(f"  ✗ 配置 {i+1} 测试失败")
        
        # 恢复原始配置
        for key, value in original_config.items():
            setattr(Config, key, value)
            
    except Exception as e:
        print(f"配置测试失败: {e}")

def provide_usage_examples():
    """提供使用示例"""
    print("\n" + "=" * 60)
    print("使用示例")
    print("=" * 60)
    
    examples = '''
# 1. 单金属训练（使用修复后的架构）
python train_tsisp.py --metal Pb --output pb_gnn_model

# 2. 批量训练
python train_tsisp.py --batch --metals Pb Cd Cu --output batch_gnn_models

# 3. 架构对比
python architecture_comparison_experiment.py --data data.csv --metal Pb --output comparison

# 4. 如果仍有问题，运行调试脚本
python debug_tensor_shapes.py

# 5. 手动调整配置（在train_tsisp.py中）
Config.encoding_dim = 72      # 或96, 108
Config.gnn_hidden_dim = 256   # 确保足够大
'''
    
    print(examples)

def main():
    """主函数"""
    print("TSISP张量维度修复验证工具")
    print("验证RuntimeError: mat1 and mat2 shapes cannot be multiplied的修复")
    
    # 运行主要测试
    success = test_dimension_fix()
    
    if success:
        print("\n✅ 维度修复验证成功！")
        
        # 测试不同配置
        test_with_different_configs()
        
        # 提供使用示例
        provide_usage_examples()
        
        print("\n🚀 现在可以正常使用GeoSpatial-GNN架构了！")
        
    else:
        print("\n❌ 维度修复验证失败")
        print("\n可能的解决方案:")
        print("1. 检查Config.encoding_dim是否设置为72、96或108")
        print("2. 确保Config.gnn_hidden_dim >= 256")
        print("3. 运行 python fix_tensor_dimensions.py 获取详细分析")
        print("4. 检查数据预处理是否正确")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
