import os
import logging
from operator import itemgetter
from pathlib import Path
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough, RunnableParallel
from langchain_community.document_loaders import (
    DirectoryLoader,
    PyPDFLoader,  # PDF专用
    Docx2txtLoader,  # Word专用
    UnstructuredExcelLoader,  # Excel专用
    UnstructuredPowerPointLoader,  # PPT专用
    TextLoader  # 文本文件
)
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.embeddings import HuggingFaceEmbeddings  # 修复的导入路径
from langchain_community.chat_models import ChatOllama
from langchain_chroma import Chroma

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

# 1. 配置区域 - 根据需要修改
MODEL_NAME = "deepseek"  # Ollama中的模型名称
KNOWLEDGE_DIR = Path('./knowledge_base')  # 文档目录
VECTOR_STORE_DIR = Path('./chroma_vector_db')  # 向量库目录

# 2. Ollama部署的DeepSeek模型
llm = ChatOllama(
    model=MODEL_NAME,
    temperature=0.3,
    num_ctx=4096,  # 上下文窗口大小
    num_predict=512  # 最大输出token
)

# 3. 嵌入模型设置
embedding_model = HuggingFaceEmbeddings(
    model_name="BAAI/bge-large-zh-v1.5"
)

# 4. 文件类型处理映射
FILE_LOADER_MAP = {
    ".pdf": PyPDFLoader,
    ".docx": Docx2txtLoader,
    ".xlsx": UnstructuredExcelLoader,
    ".pptx": UnstructuredPowerPointLoader,
    ".txt": TextLoader,
    ".csv": TextLoader,
    ".md": TextLoader
}

# 5. 文本分割器
text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=500,
    chunk_overlap=100
)

# 6. 向量数据库
vector_store = Chroma(
    embedding_function=embedding_model,
    persist_directory=str(VECTOR_STORE_DIR)
)
retriever = vector_store.as_retriever(search_kwargs={"k": 5})

# 7. RAG提示模板
prompt_template = PromptTemplate.from_template("""
你是一个严谨的RAG助手。请基于以下上下文信息回答问题：

{context}

要求:
- 若上下文信息不足，请明确说明"根据提供的信息无法回答"
- 回答内容需注明引用的文档来源
- 需要计算时提供清晰步骤

问题：{question}
""")


# 8. RAG执行链
def create_rag_chain():
    """创建RAG处理链"""
    return (
            {"question": RunnablePassthrough()}
            | RunnablePassthrough.assign(context=itemgetter("question") | retriever)
            | {
                "question": itemgetter("question"),
                "context": lambda x: "\n".join(
                    [f"[REF {i}] {doc.page_content} (来源: {os.path.basename(doc.metadata['source'])})"
                     for i, doc in enumerate(x["context"])]
                )
            }
            | prompt_template
            | llm
            | StrOutputParser()
    )


# 9. 多格式文档加载器
def load_documents():
    """加载和处理所有文档"""
    # 确保目录存在
    KNOWLEDGE_DIR.mkdir(parents=True, exist_ok=True)

    all_documents = []

    # 处理每种文件类型
    for ext, loader_cls in FILE_LOADER_MAP.items():
        try:
            # 创建特定类型的加载器
            loader = DirectoryLoader(
                str(KNOWLEDGE_DIR),
                glob=f"**/*{ext}",
                loader_cls=loader_cls,
                silent_errors=True
            )

            # 加载文档
            docs = loader.load()
            if docs:
                logger.info(f"加载了 {len(docs)} 个 {ext} 文件")
                all_documents.extend(docs)
        except Exception as e:
            logger.error(f"加载 {ext} 文件时出错: {str(e)}")

    # 检查是否找到文档
    if not all_documents:
        logger.warning("未找到任何可处理的文档")
        return []

    # 分割文本
    chunks = text_splitter.split_documents(all_documents)
    logger.info(f"总文档块: {len(chunks)}")

    return chunks


# 10. 知识库初始化函数
def init_knowledge_base():
    """初始化向量知识库"""
    logger.info("开始知识库初始化...")

    # 加载并处理文档
    chunks = load_documents()
    if not chunks:
        return False

    # 添加到向量库
    vector_store.add_documents(chunks)
    vector_store.persist()
    logger.info(f"知识库已创建: {VECTOR_STORE_DIR}")
    return True


# 11. 主程序
if __name__ == '__main__':
    # 检查是否需要初始化知识库
    if not VECTOR_STORE_DIR.exists() or not any(KNOWLEDGE_DIR.iterdir()):
        logger.info("检测到需要初始化知识库...")
        success = init_knowledge_base()
        if not success:
            logger.error("知识库初始化失败，请检查文档和设置")
            exit(1)

    # 创建RAG链
    rag_chain = create_rag_chain()

    # 交互式问答
    while True:
        try:
            question = input("\n请输入问题 (输入'退出'结束): ").strip()
            if question.lower() in ['exit', 'quit', '退出']:
                break
            if not question:
                continue

            # 执行查询
            response = rag_chain.invoke(question)
            print(f"\n问题: {question}\n回答: {response}")

        except KeyboardInterrupt:
            print("\n程序已终止")
            break
        except Exception as e:
            logger.error(f"处理问题时出错: {str(e)}")