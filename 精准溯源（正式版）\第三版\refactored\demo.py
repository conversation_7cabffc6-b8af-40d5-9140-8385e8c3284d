"""
Demonstration Script for Heavy Metal Pollution Source Apportionment System

This script demonstrates the capabilities of the refactored pollution source
apportionment system with synthetic data.

Authors: <AUTHORS>
Version: 3.0 (Refactored)
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import tempfile
import shutil

from main_engine import PollutionSourceAnalyzer
from config import ConfigManager


def create_synthetic_datasets(output_dir: str) -> None:
    """
    Create synthetic datasets for demonstration.
    
    Args:
        output_dir: Directory to save synthetic data
    """
    np.random.seed(42)
    metals = ['Cu', 'Cr', 'Ni', 'Zn', 'Pb', 'Cd', 'As']
    
    print("Creating synthetic datasets...")
    
    # Soil samples (receptors)
    n_soil = 50
    soil_data = pd.DataFrame({
        'sample': [f'Soil_{i+1:03d}' for i in range(n_soil)],
        'lon': np.random.uniform(116.0, 117.0, n_soil),
        'lat': np.random.uniform(39.0, 40.0, n_soil)
    })
    
    # Create realistic soil metal concentrations
    # Base concentrations with spatial correlation
    for i, metal in enumerate(metals):
        # Create spatial gradient
        x_coords = (soil_data['lon'] - 116.0) * 100  # Scale to 0-100
        y_coords = (soil_data['lat'] - 39.0) * 100   # Scale to 0-100
        
        # Base concentration with spatial trend
        base_conc = 10 + 5 * np.sin(x_coords * 0.1) + 3 * np.cos(y_coords * 0.1)
        
        # Add metal-specific scaling and noise
        metal_scaling = {
            'Cu': 2.0, 'Cr': 3.0, 'Ni': 1.5, 'Zn': 8.0,
            'Pb': 1.2, 'Cd': 0.1, 'As': 0.8
        }
        
        concentrations = base_conc * metal_scaling[metal] * np.random.lognormal(0, 0.3, n_soil)
        soil_data[metal] = np.maximum(concentrations, 0.1)  # Ensure positive values
    
    soil_data.to_csv(Path(output_dir) / 'soil.csv', index=False)
    print(f"Created soil dataset: {soil_data.shape}")
    
    # Source datasets
    source_types = ['atmosphere', 'irrigation', 'pesticide', 'manure']
    
    for source_type in source_types:
        n_sources = 15
        source_data = pd.DataFrame({
            'sample': [f'{source_type}_{i+1:03d}' for i in range(n_sources)],
            'lon': np.random.uniform(115.8, 117.2, n_sources),
            'lat': np.random.uniform(38.8, 40.2, n_sources)
        })
        
        # Create source-specific metal patterns
        source_patterns = {
            'atmosphere': {
                'Cu': 1.0, 'Cr': 2.0, 'Ni': 1.5, 'Zn': 3.0,
                'Pb': 4.0, 'Cd': 0.5, 'As': 1.0
            },
            'irrigation': {
                'Cu': 0.8, 'Cr': 1.0, 'Ni': 0.8, 'Zn': 2.0,
                'Pb': 0.5, 'Cd': 0.3, 'As': 2.0
            },
            'pesticide': {
                'Cu': 3.0, 'Cr': 1.0, 'Ni': 0.5, 'Zn': 1.5,
                'Pb': 1.0, 'Cd': 0.2, 'As': 5.0
            },
            'manure': {
                'Cu': 2.5, 'Cr': 0.8, 'Ni': 1.0, 'Zn': 6.0,
                'Pb': 0.8, 'Cd': 1.5, 'As': 0.5
            }
        }
        
        pattern = source_patterns[source_type]
        
        for metal in metals:
            base_conc = 5.0 * pattern[metal]
            concentrations = base_conc * np.random.lognormal(0, 0.5, n_sources)
            source_data[metal] = np.maximum(concentrations, 0.01)
        
        source_data.to_csv(Path(output_dir) / f'{source_type}.csv', index=False)
        print(f"Created {source_type} dataset: {source_data.shape}")


def run_demonstration():
    """Run complete demonstration of the system."""
    
    print("="*60)
    print("HEAVY METAL POLLUTION SOURCE APPORTIONMENT DEMONSTRATION")
    print("="*60)
    
    # Create temporary directory for demo
    temp_dir = tempfile.mkdtemp()
    data_dir = Path(temp_dir) / 'data'
    results_dir = Path(temp_dir) / 'results'
    
    try:
        # Create directories
        data_dir.mkdir(parents=True)
        results_dir.mkdir(parents=True)
        
        # Create synthetic datasets
        create_synthetic_datasets(str(data_dir))
        
        print("\n" + "="*60)
        print("RUNNING ANALYSIS")
        print("="*60)
        
        # Initialize analyzer
        analyzer = PollutionSourceAnalyzer()
        
        # Run complete analysis
        results = analyzer.run_complete_analysis(
            data_dir=str(data_dir),
            output_dir=str(results_dir),
            include_uncertainty=True,
            generate_plots=True
        )
        
        # Get summary statistics
        summary = analyzer.get_summary_statistics()
        
        print("\n" + "="*60)
        print("ANALYSIS RESULTS")
        print("="*60)
        
        print(f"Number of sources identified: {summary['results_summary']['n_sources']}")
        print(f"Number of samples analyzed: {summary['results_summary']['n_samples']}")
        print(f"Number of metals analyzed: {summary['results_summary']['n_metals']}")
        print(f"Model R²: {summary['results_summary']['model_metrics']['r2']:.3f}")
        print(f"Model MSE: {summary['results_summary']['model_metrics']['mse']:.4f}")
        
        print("\nSource interpretations:")
        for source_id, interpretation in summary['results_summary']['source_interpretations'].items():
            print(f"  Source {source_id}: {interpretation}")
        
        print("\nValidation results:")
        for validation_name, validations in summary['validations'].items():
            print(f"  {validation_name}:")
            for check, passed in validations.items():
                status = "✓" if passed else "✗"
                print(f"    {status} {check}")
        
        print(f"\nOutput files generated in: {results_dir}")
        output_files = list(results_dir.glob('*'))
        for file_path in sorted(output_files):
            print(f"  - {file_path.name}")
        
        print("\n" + "="*60)
        print("DEMONSTRATION COMPLETED SUCCESSFULLY")
        print("="*60)
        
        # Show some key results
        print("\nKey Results Summary:")
        print("-" * 40)
        
        # Load and display source profiles
        profiles_file = results_dir / 'source_profiles.csv'
        if profiles_file.exists():
            profiles_df = pd.read_csv(profiles_file, index_col=0)
            print("\nSource Profiles (top 3 metals per source):")
            for source in profiles_df.index:
                top_metals = profiles_df.loc[source].nlargest(3)
                metals_str = ", ".join([f"{metal}: {value:.3f}" for metal, value in top_metals.items()])
                print(f"  {source}: {metals_str}")
        
        # Load and display contribution statistics
        contributions_file = results_dir / 'source_contributions.csv'
        if contributions_file.exists():
            contrib_df = pd.read_csv(contributions_file, index_col=0)
            print(f"\nSource Contribution Statistics:")
            total_contrib = contrib_df.sum()
            contrib_pct = (total_contrib / total_contrib.sum()) * 100
            for source, pct in contrib_pct.items():
                print(f"  {source}: {pct:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"\nDemonstration failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up temporary directory
        try:
            shutil.rmtree(temp_dir)
        except:
            pass


def run_configuration_demo():
    """Demonstrate configuration management."""
    
    print("\n" + "="*60)
    print("CONFIGURATION MANAGEMENT DEMONSTRATION")
    print("="*60)
    
    # Create configuration manager
    config_manager = ConfigManager()
    
    print("Default Configuration:")
    print("-" * 30)
    
    model_config = config_manager.get_model_config()
    print(f"PMF factors: {model_config.pmf_n_factors}")
    print(f"PMF max iterations: {model_config.pmf_max_iter}")
    print(f"Spatial CV folds: {model_config.spatial_cv_folds}")
    print(f"Uncertainty estimators: {model_config.uncertainty_n_estimators}")
    
    data_config = config_manager.get_data_config()
    print(f"Metals analyzed: {data_config.metals}")
    print(f"Source types: {data_config.source_types}")
    
    viz_config = config_manager.get_viz_config()
    print(f"Figure DPI: {viz_config.figure_dpi}")
    print(f"Figure format: {viz_config.figure_format}")
    
    # Demonstrate configuration modification
    print("\nModifying Configuration:")
    print("-" * 30)
    
    model_config.pmf_n_factors = 5
    model_config.uncertainty_n_estimators = 50
    
    print(f"Updated PMF factors: {model_config.pmf_n_factors}")
    print(f"Updated uncertainty estimators: {model_config.uncertainty_n_estimators}")
    
    # Validate configuration
    try:
        model_config.validate()
        print("✓ Configuration validation passed")
    except ValueError as e:
        print(f"✗ Configuration validation failed: {e}")


def main():
    """Main demonstration function."""
    
    print("Heavy Metal Pollution Source Apportionment System")
    print("Version 3.0 (Refactored)")
    print("Environmental Science & Technology Journal Standards")
    print()
    
    # Run configuration demonstration
    run_configuration_demo()
    
    # Run main analysis demonstration
    success = run_demonstration()
    
    if success:
        print("\n🎉 All demonstrations completed successfully!")
        print("\nThe refactored system provides:")
        print("  ✓ Modular, maintainable architecture")
        print("  ✓ Comprehensive data validation")
        print("  ✓ Publication-quality visualizations")
        print("  ✓ Uncertainty quantification")
        print("  ✓ Scientific rigor and reproducibility")
        print("  ✓ EST journal publication standards")
    else:
        print("\n❌ Demonstration encountered errors")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
