#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
TSISP环境安装和测试脚本

本脚本用于：
1. 检测和安装必要的依赖包
2. 测试PyTorch Geometric的兼容性
3. 验证GeoSpatial-GNN架构是否正常工作
4. 提供详细的错误诊断和解决方案

使用方法：
python install_and_test.py
"""

import sys
import subprocess
import importlib
import logging
import torch
import numpy as np

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    logger.info(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error("需要Python 3.8或更高版本")
        return False
    return True

def install_package(package_name, pip_name=None):
    """安装Python包"""
    if pip_name is None:
        pip_name = package_name
    
    try:
        importlib.import_module(package_name)
        logger.info(f"✓ {package_name} 已安装")
        return True
    except ImportError:
        logger.info(f"正在安装 {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", pip_name])
            logger.info(f"✓ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"✗ {package_name} 安装失败: {e}")
            return False

def install_torch_geometric():
    """安装PyTorch Geometric"""
    logger.info("检查PyTorch Geometric安装...")
    
    # 检查PyTorch版本
    torch_version = torch.__version__
    logger.info(f"PyTorch版本: {torch_version}")
    
    # 尝试导入torch_geometric
    try:
        import torch_geometric
        logger.info(f"✓ PyTorch Geometric已安装，版本: {torch_geometric.__version__}")
        return True
    except ImportError:
        logger.info("PyTorch Geometric未安装，正在安装...")
        
        # 根据PyTorch版本安装对应的torch_geometric
        try:
            # 获取CUDA版本
            if torch.cuda.is_available():
                cuda_version = torch.version.cuda
                logger.info(f"CUDA版本: {cuda_version}")
                
                # 构建安装命令
                if cuda_version.startswith("11.8"):
                    torch_geo_url = "https://data.pyg.org/whl/torch-2.0.0+cu118.html"
                elif cuda_version.startswith("11.7"):
                    torch_geo_url = "https://data.pyg.org/whl/torch-2.0.0+cu117.html"
                else:
                    torch_geo_url = "https://data.pyg.org/whl/torch-2.0.0+cpu.html"
            else:
                torch_geo_url = "https://data.pyg.org/whl/torch-2.0.0+cpu.html"
            
            # 安装torch_geometric
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                "torch_geometric", "-f", torch_geo_url
            ])
            
            logger.info("✓ PyTorch Geometric安装成功")
            return True
            
        except subprocess.CalledProcessError:
            logger.warning("PyTorch Geometric安装失败，将使用fallback实现")
            return False

def test_torch_geometric_functions():
    """测试PyTorch Geometric函数的可用性"""
    logger.info("测试PyTorch Geometric函数...")
    
    try:
        import torch_geometric
        
        # 测试不同的导入位置
        knn_available = False
        radius_available = False
        
        # 尝试从utils导入
        try:
            from torch_geometric.utils import knn_graph, radius_graph
            knn_available = True
            radius_available = True
            logger.info("✓ 从torch_geometric.utils导入成功")
        except ImportError:
            pass
        
        # 尝试从nn导入
        if not knn_available:
            try:
                from torch_geometric.nn import knn_graph, radius_graph
                knn_available = True
                radius_available = True
                logger.info("✓ 从torch_geometric.nn导入成功")
            except ImportError:
                pass
        
        # 测试函数功能
        if knn_available and radius_available:
            # 创建测试数据
            coords = torch.randn(10, 3)
            
            try:
                edge_index_knn = knn_graph(coords, k=3)
                edge_index_radius = radius_graph(coords, r=1.0)
                logger.info("✓ PyTorch Geometric图构建函数测试成功")
                return True
            except Exception as e:
                logger.warning(f"PyTorch Geometric函数测试失败: {e}")
                return False
        else:
            logger.warning("PyTorch Geometric图构建函数不可用")
            return False
            
    except ImportError:
        logger.warning("PyTorch Geometric未安装")
        return False

def test_fallback_implementation():
    """测试fallback实现"""
    logger.info("测试fallback图构建实现...")
    
    try:
        # 导入我们的train_tsisp模块
        from train_tsisp import build_knn_graph_fallback, build_radius_graph_fallback
        
        # 创建测试数据
        coords = torch.randn(10, 3)
        
        # 测试KNN图构建
        edge_index_knn = build_knn_graph_fallback(coords, k=3)
        logger.info(f"✓ KNN fallback测试成功，边数: {edge_index_knn.shape[1]}")
        
        # 测试半径图构建
        edge_index_radius = build_radius_graph_fallback(coords, radius=1.0)
        logger.info(f"✓ 半径图fallback测试成功，边数: {edge_index_radius.shape[1]}")
        
        return True
        
    except Exception as e:
        logger.error(f"Fallback实现测试失败: {e}")
        return False

def test_geospatial_gnn():
    """测试GeoSpatial-GNN架构"""
    logger.info("测试GeoSpatial-GNN架构...")
    
    try:
        from train_tsisp import Config, SpatialGraphBuilder, GeologicalGNN
        
        # 设置测试配置
        Config.architecture = 'geospatial_gnn'
        
        # 创建测试数据
        batch_size = 16
        coords = torch.randn(batch_size, 3)
        features = torch.randn(batch_size, 64)
        
        # 测试图构建器
        graph_builder = SpatialGraphBuilder(k_neighbors=3, radius=1.0)
        edge_index, edge_attr = graph_builder.build_spatial_graph(coords, features)
        
        logger.info(f"✓ 图构建成功，节点数: {batch_size}, 边数: {edge_index.shape[1]}")
        
        # 测试GNN
        gnn = GeologicalGNN(input_dim=64, hidden_dim=128, num_layers=2, heads=4)
        output = gnn(features, edge_index, edge_attr)
        
        logger.info(f"✓ GNN前向传播成功，输出形状: {output.shape}")
        
        return True
        
    except Exception as e:
        logger.error(f"GeoSpatial-GNN测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("TSISP环境检查和测试")
    logger.info("=" * 60)
    
    # 1. 检查Python版本
    if not check_python_version():
        return False
    
    # 2. 安装基础依赖
    basic_packages = [
        ('numpy', 'numpy'),
        ('pandas', 'pandas'),
        ('sklearn', 'scikit-learn'),
        ('matplotlib', 'matplotlib'),
        ('seaborn', 'seaborn'),
        ('scipy', 'scipy'),
        ('psutil', 'psutil')
    ]
    
    logger.info("检查基础依赖包...")
    for package, pip_name in basic_packages:
        if not install_package(package, pip_name):
            logger.error(f"基础依赖 {package} 安装失败")
            return False
    
    # 3. 检查PyTorch
    try:
        import torch
        logger.info(f"✓ PyTorch版本: {torch.__version__}")
        if torch.cuda.is_available():
            logger.info(f"✓ CUDA可用，版本: {torch.version.cuda}")
        else:
            logger.info("! CUDA不可用，将使用CPU")
    except ImportError:
        logger.error("PyTorch未安装，请先安装PyTorch")
        return False
    
    # 4. 安装和测试PyTorch Geometric
    torch_geo_available = install_torch_geometric()
    if torch_geo_available:
        torch_geo_functions_work = test_torch_geometric_functions()
    else:
        torch_geo_functions_work = False
    
    # 5. 测试fallback实现
    fallback_works = test_fallback_implementation()
    
    # 6. 测试完整的GeoSpatial-GNN架构
    gnn_works = test_geospatial_gnn()
    
    # 7. 总结报告
    logger.info("=" * 60)
    logger.info("测试结果总结")
    logger.info("=" * 60)
    
    if torch_geo_available and torch_geo_functions_work:
        logger.info("✓ PyTorch Geometric完全可用")
        recommendation = "推荐使用完整的PyTorch Geometric功能"
    elif fallback_works:
        logger.info("! PyTorch Geometric不可用，但fallback实现正常")
        recommendation = "可以使用fallback实现，功能略有限制"
    else:
        logger.error("✗ 图构建功能不可用")
        recommendation = "需要修复安装问题"
    
    if gnn_works:
        logger.info("✓ GeoSpatial-GNN架构测试通过")
    else:
        logger.error("✗ GeoSpatial-GNN架构测试失败")
    
    logger.info(f"建议: {recommendation}")
    
    # 8. 提供安装建议
    if not torch_geo_available or not torch_geo_functions_work:
        logger.info("\n安装建议:")
        logger.info("1. 尝试手动安装PyTorch Geometric:")
        logger.info("   pip install torch_geometric")
        logger.info("2. 或者使用conda安装:")
        logger.info("   conda install pytorch-geometric -c pytorch -c conda-forge")
        logger.info("3. 如果仍有问题，可以使用fallback实现")
    
    return gnn_works

if __name__ == "__main__":
    success = main()
    if success:
        logger.info("\n🎉 环境配置成功！可以开始使用TSISP了")
        print("\n使用示例:")
        print("python train_tsisp.py --metal Pb --output test_output")
    else:
        logger.error("\n❌ 环境配置失败，请检查上述错误信息")
        sys.exit(1)
