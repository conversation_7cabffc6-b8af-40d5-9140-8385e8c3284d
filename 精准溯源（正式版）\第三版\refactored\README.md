# Heavy Metal Pollution Source Apportionment System

A comprehensive, publication-quality Python framework for heavy metal pollution source apportionment in agricultural soils, designed for Environmental Science & Technology journal standards.

## 🎯 Overview

This system implements state-of-the-art algorithms for identifying and quantifying pollution sources in agricultural soils using heavy metal fingerprinting and advanced statistical methods. The framework is designed to meet the rigorous standards required for publication in top-tier environmental science journals.

## 🔬 Scientific Methods

### Core Algorithms
- **Positive Matrix Factorization (PMF)**: Advanced receptor modeling for source identification
- **Chemical Fingerprinting**: Standardized compositional analysis with uncertainty quantification
- **Bootstrap Uncertainty Analysis**: Statistical validation of results
- **Spatial Cross-Validation**: Avoiding spatial autocorrelation in model validation

### Key Features
- **Publication-Quality Visualizations**: EST journal-standard figures
- **Comprehensive Data Validation**: Quality control and outlier detection
- **Uncertainty Quantification**: Bootstrap confidence intervals
- **Reproducible Results**: Deterministic algorithms with proper random seeding
- **Modular Architecture**: Extensible and maintainable codebase

## 📦 Installation

### Requirements
```bash
pip install numpy pandas matplotlib seaborn scikit-learn scipy
```

### Optional Dependencies
```bash
pip install torch torch-geometric  # For advanced deep learning features
pip install optuna                 # For hyperparameter optimization
pip install shap                   # For model interpretability
```

## 🚀 Quick Start

### Basic Usage
```python
from main_engine import PollutionSourceAnalyzer

# Initialize analyzer
analyzer = PollutionSourceAnalyzer()

# Run complete analysis
results = analyzer.run_complete_analysis(
    data_dir='data',
    output_dir='results',
    include_uncertainty=True,
    generate_plots=True
)

# Get summary statistics
summary = analyzer.get_summary_statistics()
print(f"Model R²: {summary['results_summary']['model_metrics']['r2']:.3f}")
```

### Command Line Usage
```bash
python main_engine.py --data-dir data --output-dir results --config config.json
```

## 📁 Data Format

### Required Data Files

#### Soil Samples (`soil.csv`)
```csv
sample,lon,lat,Cu,Cr,Ni,Zn,Pb,Cd,As
S001,116.123,39.456,25.3,45.2,18.7,89.1,12.4,0.8,5.2
S002,116.234,39.567,28.1,42.8,20.1,92.3,14.2,0.9,4.8
...
```

#### Source Samples
- `atmosphere.csv`: Atmospheric deposition samples
- `irrigation.csv`: Irrigation water samples  
- `pesticide.csv`: Pesticide application samples
- `manure.csv`: Organic fertilizer samples
- `industry.csv`: Industrial emission samples

### Data Requirements
- **Coordinates**: Longitude (lon) and latitude (lat) in decimal degrees
- **Heavy Metals**: Cu, Cr, Ni, Zn, Pb, Cd, As concentrations (mg/kg)
- **Sample IDs**: Unique identifiers for each sample
- **Quality Control**: Non-negative values, proper units

## ⚙️ Configuration

### Configuration File (`config.json`)
```json
{
  "model": {
    "pmf_n_factors": 4,
    "pmf_max_iter": 1000,
    "spatial_cv_folds": 5,
    "uncertainty_n_estimators": 100
  },
  "data": {
    "data_dir": "data",
    "output_dir": "results",
    "metals": ["Cu", "Cr", "Ni", "Zn", "Pb", "Cd", "As"]
  },
  "visualization": {
    "figure_dpi": 300,
    "figure_format": "png",
    "use_latex": false
  }
}
```

## 📊 Output Files

### Analysis Results
- `source_profiles.csv`: Source chemical profiles
- `source_contributions.csv`: Source contributions to each sample
- `similarity_matrix.csv`: Source-receptor similarity matrix
- `analysis_report.json`: Comprehensive analysis summary

### Visualizations
- `source_profiles.png`: Source chemical fingerprints
- `contribution_matrix.png`: Contribution heatmap
- `model_diagnostics.png`: Model validation plots
- `publication_figure.png`: Comprehensive publication-ready figure

### Quality Reports
- `*_quality_report.json`: Data quality assessment for each dataset

## 🧪 Testing

Run the comprehensive test suite:
```bash
python tests.py
```

### Test Coverage
- Configuration management
- Data processing and validation
- Source apportionment algorithms
- Integration testing
- Scientific validation

## 📈 Model Validation

### Scientific Rigor
- **Mass Balance**: Ensures conservation of mass in source apportionment
- **Non-negativity**: Enforces physically meaningful results
- **Model Fit**: R² > 0.7 for acceptable model performance
- **Bootstrap Uncertainty**: 95% confidence intervals
- **Spatial Validation**: Cross-validation accounting for spatial autocorrelation

### Quality Metrics
- **R² Score**: Coefficient of determination
- **Mean Squared Error**: Reconstruction accuracy
- **Explained Variance**: Model explanatory power
- **Uncertainty Bounds**: Statistical confidence

## 🔬 Scientific Applications

### Research Areas
- Agricultural soil pollution assessment
- Urban environmental monitoring
- Industrial impact evaluation
- Policy development support
- Risk assessment studies

### Publication Standards
- Follows EST journal formatting guidelines
- Includes comprehensive uncertainty analysis
- Provides reproducible methodology
- Implements peer-reviewed algorithms

## 🏗️ Architecture

### Modular Design
```
refactored/
├── config.py              # Configuration management
├── data_processor.py      # Data loading and validation
├── source_apportionment.py # Core algorithms
├── visualization.py       # Publication-quality plots
├── main_engine.py         # Main analysis engine
├── tests.py              # Comprehensive test suite
└── README.md             # Documentation
```

### Key Classes
- `ConfigManager`: Centralized configuration
- `DataProcessor`: Data handling and validation
- `SourceApportionmentEngine`: Core analysis algorithms
- `PublicationVisualizer`: Scientific visualization
- `PollutionSourceAnalyzer`: Main analysis orchestrator

## 🤝 Contributing

### Development Guidelines
1. Follow PEP 8 style guidelines
2. Add comprehensive docstrings
3. Include unit tests for new features
4. Maintain scientific accuracy
5. Update documentation

### Code Quality
- Type hints for all functions
- Comprehensive error handling
- Logging for debugging
- Scientific validation
- Performance optimization

## 📚 References

1. Paatero, P., & Tapper, U. (1994). Positive matrix factorization: A non-negative factor model with optimal utilization of error estimates of data values. *Environmetrics*, 5(2), 111-126.

2. Hopke, P. K. (2016). Review of receptor modeling methods for source apportionment. *Journal of the Air & Waste Management Association*, 66(3), 237-259.

3. Belis, C. A., et al. (2013). Critical review and meta-analysis of ambient particulate matter source apportionment using receptor models in Europe. *Atmospheric Environment*, 69, 94-108.

## 📄 License

MIT License - See LICENSE file for details.

## 👥 Authors

Environmental Science & Machine Learning Research Team
- Version: 3.0 (Refactored)
- Contact: [Your Contact Information]

## 🆘 Support

For technical support or scientific questions:
1. Check the documentation
2. Run the test suite
3. Review example configurations
4. Contact the development team

---

*This software is designed for scientific research and publication in peer-reviewed journals. Please cite appropriately when using in academic work.*
